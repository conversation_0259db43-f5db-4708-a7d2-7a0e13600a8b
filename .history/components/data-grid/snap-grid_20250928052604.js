/**
 * SnapGrid - Advanced Data Grid Engine
 * A comprehensive client-side data grid inspired by AG Grid
 * Following the single-file architecture pattern of snap-charts.js
 *
 * Features:
 * - Virtual scrolling for performance with large datasets
 * - Column menus with sorting, filtering, grouping
 * - Client-side data operations
 * - Cell editing and custom renderers
 * - Performance monitoring integration
 * - Modern UI design matching Snap Dashboard
 *
 * @version 1.0.0
 * <AUTHOR> Dashboard Team
 */

const MS_PER_DAY = 24 * 60 * 60 * 1000;
const DATE_PRESET_OPERATORS = new Set([
    'today', 'yesterday',
    'thisWeek', 'lastWeek',
    'last7Days', 'last30Days', 'last90Days', 'last6Months',
    'thisMonth', 'lastMonth', 'thisYear', 'lastYear',
    'currentMonth', 'currentYear'
]);

class SnapGrid {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.getElementById(container) : container;
        if (!this.container) {
            throw new Error('SnapGrid: Container element not found');
        }

        // Core configuration
        this.options = {
            // Data options
            data: options.data || [],
            columns: options.columns || [],

            // Performance options
            virtualScrolling: options.virtualScrolling !== false,
            rowHeight: options.rowHeight || 40,
            headerHeight: options.headerHeight || 48,
            bufferSize: options.bufferSize || 10,

            // Feature options
            sortable: options.sortable !== false,
            filterable: options.filterable !== false,
            resizable: options.resizable !== false,
            selectable: options.selectable !== false,
            editable: options.editable || false,

            // Query/worker options
            enableWorkerQuery: options.enableWorkerQuery === true,
            workerThreshold: options.workerThreshold || 50000,

            // Checkbox and selection options
            checkboxSelection: options.checkboxSelection !== false,
            headerCheckboxSelection: options.headerCheckboxSelection !== false,

            // Column options
            columnDragging: options.columnDragging !== false,
            pinnedColumns: options.pinnedColumns || ['checkbox'], // Default pinned columns

            // UI options
            theme: options.theme || 'default', // default, compact, dense, comfortable
            showHeader: options.showHeader !== false,
            showFooter: options.showFooter || false,

            // Callbacks
            onRowClick: options.onRowClick || null,
            onCellEdit: options.onCellEdit || null,
            onSort: options.onSort || null,
            onFilter: options.onFilter || null,
            onSelectionChanged: options.onSelectionChanged || null,
            onColumnMoved: options.onColumnMoved || null,

            ...options,
            // Enforce non-editable grid and footer visible by default
            editable: false,
            showFooter: options.showFooter !== false
        };

        // Internal state
        this.data = Array.isArray(this.options.data) ? this.options.data.slice() : [];
        // Index-based row model
        this.filteredIdx = this.data.map((_, i) => i);
        this.sortedIdx = this.filteredIdx.slice();
        this.selectedRows = new Set();
        this.sortState = {};
        this.filterState = {};
        this.checkboxState = {};
        this._checkboxAllValueCounts = {};
        this._checkboxAllValues = Object.create(null);
        this.columnState = {};

        // Layout and filter state tracking
        this.activeQuickFilters = new Set(); // Track which quick filters are active
        this.currentLayoutType = 'default'; // 'default', 'custom', or saved layout ID
        this.originalColumnState = null; // Store original column configuration
        this.isQuickFilterActive = false; // Flag to track if any quick filter is active
        this.isSavedLayoutActive = false; // Flag to track if a saved layout is active

        // Track a persistent Custom layout id if present
        this._customLayoutId = null;

        // Define a single handler to respond to any user customization that should persist as Custom
        this.onUserCustomized = (reason) => {
            try {
                if (this._suppressCustomPersist || this.isQuickFilterActive) {
                    window.SnapLogger?.debug?.('onUserCustomized suppressed', { reason, isQuickFilterActive: this.isQuickFilterActive });
                    return;
                }
                // Persist for column layout changes and filters (but NOT sorts)
                const allowed = reason === 'reorder' || reason === 'visibility' || reason === 'pin' || reason === 'resize' || reason === 'filter';
                if (!allowed) {
                    window.SnapLogger?.debug?.('onUserCustomized ignored', { reason });
                    return;
                }
                if (this.currentLayoutType !== 'custom') {
                    // Switch to Custom when starting from Default or a Saved layout
                    this.switchToCustomLayout();
                }
                // Always save/update the Custom layout when user customizes
                this.saveOrUpdateCustomLayout();
                window.SnapLogger?.debug?.('onUserCustomized', { reason, currentLayoutType: this.currentLayoutType });
            } catch (err) {
                console.error('onUserCustomized error:', err);
            }
        };

        // Internal flag to prevent persisting certain programmatic clears into Custom
        this._suppressCustomPersist = false;

        // Selection state
        this.allRowsSelected = false;
        this.indeterminateSelection = false;
        // Anchor for shift+click range selection (stores last clicked row index)
        this.lastSelectedRowIndex = null;

        // Chunked loading state
        this.expectedTotalRecords = null; // Track expected total during chunked loading

        // Ensure pinned offset maps always exist
        // Some environments reported an error when accessing index 0
        // on undefined maps during initial render. Initialize them here
        // and recompute later via computePinnedOffsets().
        this.pinnedLeftOffsets = {};
        this.pinnedRightOffsets = {};

        // Column dragging state
        this.isDraggingColumn = false;
        this.draggedColumn = null;
        this.dragStartX = 0;
        this.dragStartIndex = 0;

        // Column menu state
        this.currentColumnMenu = null;

        // Prepare columns with checkbox column if enabled
        this.prepareColumns();

        // Store original column state for Default Layout restoration
        this.storeOriginalColumnState();

        // Virtual scrolling state
        this.scrollTop = 0;
        this.visibleStartIndex = 0;
        this.visibleEndIndex = 0;
        this.totalHeight = 0;
        this._scrollScale = 1;
        this._MAX_SCROLL_PX = 33500000; // ~33.5M px safe cap

        // DOM elements
        this.gridElement = null;
        this.headerElement = null;
        this.bodyElement = null;
        this.viewportElement = null;
        this.scrollbarElement = null;

        // Performance monitoring
        this.renderStartTime = 0;
        this.lastRenderDuration = 0;
        this._lastFilterDuration = 0;
        this._scrollRaf = null;

        // Worker query adapter (optional)
        this._queryAdapter = (window.GridQueryAdapter && this.options.enableWorkerQuery) ? new window.GridQueryAdapter('performance-optimizations/grid-query-worker.js') : null;
        this._lastQueryId = 0;
        // Unique values cache
        this._uniqueCache = Object.create(null);
        // Date parsing cache + preset range cache to keep heavy date math off the hot path
        this._dateParseCache = new Map();
        this._presetDateRangeCache = Object.create(null);
        this._todayContextCache = null;
        this._dateColumnCache = new Map();
        this._dateCacheRevision = 0;
        this._activeFilterDescriptors = new Map();

        // Debug flag for verbose date filtering logs (opt-in)
        this._debugDateFiltering = Boolean(this.options && this.options.debugDateFiltering);

        // Filter debouncing
        this.filterDebounceTimer = null;
        this.filterDebounceDelay = 300; // 300ms delay

        // Expose AG-style APIs
        this.buildApis();

        this.init();
    }

    /**
     * Normalize pinned value to AG semantics: 'left' | 'right' | null
     */
    sanitizePinned(pinned) {
        if (pinned === 'left' || pinned === 'right') return pinned;
        if (pinned === true) return 'left';
        return null;
    }

    /**
     * Prepare columns with checkbox column if enabled
     */
    prepareColumns() {
        this.processedColumns = [...this.options.columns];

        // Enforce AG-style pinning semantics on provided columns
        this.processedColumns.forEach(col => {
            col.pinned = this.sanitizePinned(col.pinned);
            // Support AG-style hide/visible flags without breaking existing code
            if (col.visible === false) col.hide = true;
            if (col.hide === false) delete col.hide;
        });

        // Add checkbox column if enabled
        if (this.options.checkboxSelection) {
            const checkboxColumn = {
                field: 'checkbox',
                headerName: '',
                width: 46, // Will be set properly by calculateMinHeaderWidth
                pinned: 'left',
                sortable: false,
                filterable: false,
                resizable: false,
                editable: false,
                cellRenderer: this.renderCheckboxCell.bind(this),
                headerRenderer: this.renderCheckboxHeader.bind(this)
            };

            this.processedColumns.unshift(checkboxColumn);

            // Insert Preview column immediately after checkbox if not provided
            if (!this.processedColumns.find(c => c.field === 'preview')) {
                const previewColumn = {
                    field: 'preview',
                    headerName: 'Preview',
                    width: 60, // Will be set properly by calculateMinHeaderWidth
                    pinned: 'left',
                    sortable: false,
                    filterable: false,
                    resizable: false,
                    editable: false,
                    cellRenderer: () => '<div class="preview-square" title="Preview"></div>'
                };
                this.processedColumns.splice(1, 0, previewColumn);
            }
        }

        // Ensure Actions column exists and is pinned-right (enforce even if provided by user)
        const existingActions = this.processedColumns.find(c => c.field === 'actions');
        if (!existingActions) {
            const actionsColumn = {
                field: 'actions',
                headerName: 'Actions',
                width: 96, // Will be set properly by calculateMinHeaderWidth
                pinned: 'right',
                sortable: false,
                filterable: false,
                resizable: false,
                editable: false,
                cellRenderer: () => `
                    <div class="actions-container">
                        <span class="listing-edit-ic" title="Edit"><img src="assets/edit-ic.svg" alt="Edit" width="14" height="14"></span>
                        <span class="listing-analyse-ic" title="Analyse"><img src="assets/analyse-ic.svg" alt="Analyse" width="16" height="16"></span>
                    </div>
                `
            };
            this.processedColumns.push(actionsColumn);
        } else {
            existingActions.pinned = 'right';
            existingActions.sortable = false;
            existingActions.filterable = false;
            existingActions.resizable = false;
            existingActions.editable = false;
            if (!existingActions.width) existingActions.width = 96;
        }

        // Sort columns by pinned status
        this.sortColumnsByPinned();

        // Ensure every column has a default width for horizontal scrolling
        this.processedColumns.forEach(col => {
            if (!col.width) {
                // Calculate minimum width based on header text
                const minWidth = this.calculateMinHeaderWidth(col);
                const defaultWidth = 150; // Default fallback width
                col.width = Math.max(minWidth, defaultWidth);
            } else {
                // Even if width is provided, ensure it meets minimum requirements
                const minWidth = this.calculateMinHeaderWidth(col);
                col.width = Math.max(minWidth, col.width);
            }
        });
    }

    /**
     * Helper to sort processedColumns by pinned status (left -> center -> right)
     */
    sortColumnsByPinned() {
        this.processedColumns.sort((a, b) => {
            const aPinned = a.pinned === 'left' ? 0 : a.pinned === 'right' ? 2 : 1;
            const bPinned = b.pinned === 'left' ? 0 : b.pinned === 'right' ? 2 : 1;
            return aPinned - bPinned;
        });
    }

    /**
     * Build gridApi and columnApi with AG-style methods
     */
    buildApis() {
        const self = this;

        // Column API
        this.columnApi = {
            // Pin/unpin a column
            setColumnPinned(field, pinned) {
                return self.setColumnPinned(field, pinned);
            },
            // Apply an array of column state entries
            applyColumnState(params = {}) {
                return self.applyColumnState(params);
            },
            // Get current column state
            getColumnState() {
                return self.getColumnState();
            },
            // Set column width
            setColumnWidth(field, width) {
                return self.setColumnWidth(field, width);
            },
            // Move column to target visible index (center block)
            moveColumnByField(field, toIndex) {
            const ok = self.moveColumnByField(field, toIndex);
            if (ok) self.onUserCustomized && self.onUserCustomized('reorder');
            return ok;
            },
            // Show/hide column
            setColumnVisible(field, visible) {
            const ok = self.setColumnVisible(field, visible);
            if (ok) self.onUserCustomized && self.onUserCustomized('visibility');
            return ok;
            },
            // Accessors
            getAllColumns() { return [...self.processedColumns]; },
            getColumn(field) { return self.getColumn(field); }
        };

        // Grid API
        this.gridApi = {
            setRowData(data) { return self.setRowData(data); },
            refreshCells(params = {}) { return self.refreshCells(params); },
            redrawRows(params = {}) { return self.redrawRows(params); },
            ensureColumnVisible(field) { return self.ensureColumnVisible(field); },
            ensureIndexVisible(index) { return self.ensureIndexVisible(index); },
            sizeColumnsToFit() { return self.sizeColumnsToFit(); },
            getDisplayedRowCount() { return self.sortedIdx.length; },
            getDisplayedRowAtIndex(i) { const idx = self.sortedIdx[i]; return (idx != null) ? self.data[idx] : null; }
        };
    }

    /**
     * Render checkbox cell
     */
    renderCheckboxCell(value, column, rowData, rowIndex) {
        const isSelected = this.selectedRows.has(rowIndex);
        const iconName = isSelected ? 'checkbox-ic.svg' : 'uncheckedbox-ic.svg';

        return `
            <div class="snap-grid-checkbox-cell" data-row-index="${rowIndex}">
                <img src="assets/${iconName}"
                     alt="${isSelected ? 'Selected' : 'Not selected'}"
                     class="snap-grid-checkbox-icon">
            </div>
        `;
    }

    /**
     * Render checkbox header
     */
    renderCheckboxHeader() {
        let iconName = 'uncheckedbox-ic.svg';

        if (this.allRowsSelected) {
            iconName = 'checkbox-ic.svg';
        } else if (this.indeterminateSelection) {
            iconName = 'indeterminate-ic.svg';
        }

        return `
            <div class="snap-grid-checkbox-header">
                <img src="assets/${iconName}"
                     alt="Select all"
                     class="snap-grid-checkbox-icon">
            </div>
        `;
    }

    /**
     * Initialize the grid
     */
    init() {
        if (window.SnapLogger) window.SnapLogger.info('Initializing SnapGrid');
        this.renderStartTime = performance.now();

        this.setupDOM();
        this.setupEventListeners();
        this.processData();
        this.render();

        // Establish correct initial UI states (e.g., Clear Filters disabled when no filters)
        this.updateFilterState();

        const initDuration = performance.now() - this.renderStartTime;
        if (window.SnapLogger) window.SnapLogger.info(`SnapGrid initialized in ${initDuration.toFixed(2)}ms`);
    }

    /**
     * Set up the DOM structure
     */
    setupDOM() {
        // Clear container
        this.container.innerHTML = '';
        this.container.className = `snap-grid ${this.options.theme}`;

        // Add ARIA attributes for accessibility
        this.container.setAttribute('role', 'grid');
        this.container.setAttribute('aria-label', 'Data grid');
        this.container.setAttribute('tabindex', '0');

        // Create main grid structure
        this.gridElement = document.createElement('div');
        this.gridElement.className = 'snap-grid-container';

        // Create controls header (styled, no legacy logic)
        this.createControlsHeader();

        // Create header
        if (this.options.showHeader) {
            this.headerElement = document.createElement('div');
            this.headerElement.className = 'snap-grid-header';
            this.headerElement.setAttribute('role', 'rowgroup');
            this.gridElement.appendChild(this.headerElement);
        }

        // Create body with viewport for virtual scrolling
        this.bodyElement = document.createElement('div');
        this.bodyElement.className = 'snap-grid-body';
        this.bodyElement.setAttribute('role', 'rowgroup');

        this.viewportElement = document.createElement('div');
        this.viewportElement.className = 'snap-grid-viewport';
        this.bodyElement.appendChild(this.viewportElement);

        this.gridElement.appendChild(this.bodyElement);

        // Use body's native horizontal scrollbar only

        // Create footer stats bar
        this.createFooter();
        this.container.appendChild(this.gridElement);

        // Set up virtual scrolling container
        this.setupVirtualScrolling();

        // Add screen reader announcements
        this.createAriaLiveRegion();
    }

    /**
     * Create the styled controls header (filters, layout, actions, info)
     */
    createControlsHeader() {
        const controls = document.createElement('div');
        controls.className = 'snap-grid-controls-header';

        // Left group: Filters dropdown + Layout dropdown + Clear Filters
        const left = document.createElement('div');
        left.className = 'snap-grid-controls-left';

        // Create placeholders for dropdowns (will be populated by initializeControlsHeader)
        const filtersWrapper = document.createElement('div');
        filtersWrapper.className = 'snap-grid-filters-dropdown';
        left.appendChild(filtersWrapper);

        const layoutWrapper = document.createElement('div');
        layoutWrapper.className = 'snap-grid-layout-dropdown';
        left.appendChild(layoutWrapper);


        // Center group: Delete + Export
        const center = document.createElement('div');
        center.className = 'snap-grid-controls-center';

        // Create simple buttons first, will be replaced by dropdown setup methods
        const deleteBtn = document.createElement('button');
        deleteBtn.className = 'snap-grid-delete-btn';
        deleteBtn.title = 'Delete Selected';
        deleteBtn.innerHTML = '<img src="assets/delete-ic.svg" alt="Delete" />';
        center.appendChild(deleteBtn);

        const exportBtn = document.createElement('button');
        exportBtn.className = 'snap-grid-export-btn';
        exportBtn.title = 'Export Data';
        exportBtn.innerHTML = '<img src="assets/export-ic.svg" alt="Export" />';
        center.appendChild(exportBtn);

        // Right group: Loaded info pill
        const right = document.createElement('div');
        right.className = 'snap-grid-controls-right';
        const loadedInfo = document.createElement('div');
        loadedInfo.className = 'snap-grid-loaded-info';
        right.appendChild(loadedInfo);

        controls.appendChild(left);
        controls.appendChild(center);
        controls.appendChild(right);

        // Filter labels container - placed below the main controls
        const filterLabelsContainer = document.createElement('div');
        filterLabelsContainer.className = 'snap-grid-filter-labels';
        filterLabelsContainer.style.display = 'none'; // Hidden by default

        this.gridElement.appendChild(controls);
        this.gridElement.appendChild(filterLabelsContainer);

        // Cache references
        this.controlsHeaderElement = controls;
        this.controls = {
            deleteBtn,
            exportBtn,
            loadedInfo
        };

        // Hide Delete/Export by default (no selection) to match old grid behavior
        if (this.controls.deleteBtn) this.controls.deleteBtn.style.display = 'none';
        if (this.controls.exportBtn) this.controls.exportBtn.style.display = 'none';

        // Cache DOM elements for left controls
        this.elements = {
            ...this.elements,
            filtersDropdown: filtersWrapper,
            layoutDropdown: layoutWrapper,
            filterLabelsContainer: filterLabelsContainer
        };

        // Initialize left controls with proper dropdowns and functionality
        this.initializeControlsHeader();

        // Setup dropdown functionality for center controls
        this.setupDeleteDropdown();
        this.setupExportDropdown();
    }

    /**
     * Create footer stats bar
     */
    createFooter() {
        if (!this.options.showFooter) return;

        const footer = document.createElement('div');
        footer.className = 'snap-grid-footer';

        const stats = document.createElement('div');
        stats.className = 'snap-grid-footer-stats';

        const makeStat = (label) => {
            const item = document.createElement('div');
            item.className = 'snap-grid-stat-item';
            const lab = document.createElement('div');
            lab.className = 'snap-grid-stat-label';
            lab.textContent = label;
            const val = document.createElement('div');
            val.className = 'snap-grid-stat-value';
            val.textContent = '0';
            item.appendChild(lab);
            item.appendChild(val);
            return { item, val };
        };

        const rows = makeStat('ROWS');
        const filtered = makeStat('FILTERED');
        const selected = makeStat('SELECTED');

        stats.appendChild(rows.item);
        stats.appendChild(filtered.item);
        stats.appendChild(selected.item);

        // Right-side hint: appears only when rows are selected
        const hint = document.createElement('div');
        hint.className = 'snap-grid-footer-hint';
        hint.style.position = 'absolute';
        hint.style.right = '48px';
        hint.style.top = '50%';
        hint.style.transform = 'translateY(-50%)';
        hint.style.display = 'none'; // hidden until there is a selection
        hint.style.fontSize = '12px';
        hint.style.color = 'var(--text-primary)';
        hint.style.alignItems = 'center';
        hint.style.gap = '6px';
        hint.setAttribute('aria-live', 'polite');

        const makeKey = (text) => {
            const k = document.createElement('span');
            k.textContent = text;
            k.style.display = 'inline-flex';
            k.style.alignItems = 'center';
            k.style.justifyContent = 'center';
            k.style.padding = '2px 6px';
            k.style.border = '1px solid var(--grid-border-color)';
            k.style.borderRadius = '3px';
            k.style.background = 'var(--bg-secondary)';
            k.style.color = 'var(--text-primary)';
            k.style.fontFamily = 'inherit';
            k.style.fontSize = '11px';
            k.style.lineHeight = '1';
            return k;
        };

        // Compose: [Shift] + [Click] to select a range of rows
        hint.appendChild(makeKey('Shift'));
        const plus = document.createElement('span');
        plus.textContent = '+';
        hint.appendChild(plus);
        hint.appendChild(makeKey('Click'));
        const msg = document.createElement('span');
        msg.textContent = 'to select a range of rows';
        hint.appendChild(msg);

        footer.appendChild(hint);

        footer.appendChild(stats);
        this.gridElement.appendChild(footer);

        this.footerElement = footer;
        this.footer = { rows: rows.val, filtered: filtered.val, selected: selected.val, hint };
    }

    /**
     * Generic dropdown builder with styles compatible to .snap-dropdown
     */
    createDropdown({ className = '', items = [], selectedValue = null, onChange = null } = {}) {
        const dropdown = document.createElement('div');
        dropdown.className = `snap-dropdown ${className}`.trim();

        const header = document.createElement('div');
        header.className = 'dropdown-header';
        const title = document.createElement('span');
        title.textContent = '';
        const caret = document.createElement('img');
        caret.src = 'assets/dropdown-ic.svg';
        caret.alt = 'Open';
        header.appendChild(title);
        header.appendChild(caret);

        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';

        const setSelected = (value, text) => {
            title.textContent = text;
            selectedValue = value;
            // Update selected class
            menu.querySelectorAll('.dropdown-item').forEach(el => {
                if (el.dataset.value === String(value)) el.classList.add('selected');
                else el.classList.remove('selected');
            });
        };

        items.forEach(item => {
            if (item.divider) {
                const div = document.createElement('div');
                div.className = 'dropdown-divider';
                menu.appendChild(div);
                return;
            }
            const opt = document.createElement('div');
            opt.className = 'dropdown-item';
            opt.dataset.value = String(item.value);
            if (item.icon) {
                const icon = document.createElement('span');
                icon.className = 'item-icon';
                icon.innerHTML = `<img src="assets/${item.icon}" alt="" />`;
                opt.appendChild(icon);
            }
            const text = document.createElement('span');
            text.className = 'item-text';
            text.textContent = item.text;
            opt.appendChild(text);
            opt.addEventListener('click', (e) => {
                e.stopPropagation();
                setSelected(item.value, item.text);
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
                // Also ensure ALL dropdowns lose focus when an item is selected
                document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                    d.classList.remove('focused');
                    const m = d.querySelector('.dropdown-menu');
                    if (m) m.classList.add('hidden');
                });
                if (typeof onChange === 'function') onChange(item.value, item);
                // Dispatch event for external listeners
                dropdown.dispatchEvent(new CustomEvent('change', { detail: { value: item.value, text: item.text, item } }));
            });
            menu.appendChild(opt);
        });

        header.addEventListener('click', (e) => {
            e.stopPropagation();
            const open = !menu.classList.contains('hidden');
            // Close others
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                if (d !== dropdown) {
                    d.classList.remove('focused');
                    const m = d.querySelector('.dropdown-menu');
                    if (m) m.classList.add('hidden');
                }
            });
            if (!open) {
                dropdown.classList.add('focused');
                menu.classList.remove('hidden');
            } else {
                dropdown.classList.remove('focused');
                menu.classList.add('hidden');
            }
        });

        document.addEventListener('click', () => {
            dropdown.classList.remove('focused');
            menu.classList.add('hidden');
        });

        dropdown.appendChild(header);
        dropdown.appendChild(menu);

        // Initialize selection
        const initItem = items.find(i => i.value === selectedValue) || items.find(i => !i.divider) || null;
        if (initItem) setSelected(initItem.value, initItem.text);

        return dropdown;
    }

    /**
     * Filters dropdown: includes "All" and any provided presets
     */
    createFiltersDropdown() {
        const presets = Array.isArray(this.options.filterPresets) ? this.options.filterPresets : [];
        const items = [
            { value: 'all', text: 'All' },
            ...(presets.length ? [{ divider: true }] : []),
            ...presets.map(p => ({ value: p.value ?? p, text: p.text ?? String(p), icon: p.icon }))
        ];
        const dropdown = this.createDropdown({ className: '', items, selectedValue: 'all', onChange: (val) => {
            if (val === 'all') {
                this.clearFilters();
                if (typeof this.options.onFilterPresetSelected === 'function') this.options.onFilterPresetSelected(val);
                return;
            }
            if (typeof this.options.onFilterPresetSelected === 'function') {
                this.options.onFilterPresetSelected(val);
            }
        } });
        return dropdown;
    }

    /**
     * Layout dropdown: Default, Save, Delete + optional saved layouts (callback-based)
     */
    createLayoutDropdown() {
        const baseItems = [
            { value: 'default', text: 'Default Layout', icon: 'checked-option-ic.svg', isLayout: true, selected: true },
            { divider: true },
            { value: 'save', text: 'Save Layout', icon: 'save-layout-ic.svg' },
            { value: 'delete', text: 'Delete Selected', icon: 'delete-ic.svg' }
        ];
        const savedLayouts = Array.isArray(this.options.savedLayouts) ? this.options.savedLayouts : [];
        if (savedLayouts.length) {
            baseItems.splice(2, 0, ...savedLayouts.map(l => ({ value: l.id || l.value, text: l.name || l.text, icon: 'checked-option-ic.svg', isLayout: true })));
        }

        const dropdown = this.createDropdown({ className: 'layout-dropdown', items: baseItems, selectedValue: 'default', onChange: (val, item) => {
            if (val === 'default') {
                if (typeof this.options.onApplyDefaultLayout === 'function') this.options.onApplyDefaultLayout();
            } else if (val === 'save') {
                if (typeof this.options.onSaveLayout === 'function') this.options.onSaveLayout(this.getCurrentLayout());
            } else if (val === 'delete') {
                if (typeof this.options.onDeleteLayout === 'function') this.options.onDeleteLayout(this.currentLayoutId || null);
            } else {
                this.currentLayoutId = val;
                if (typeof this.options.onApplyLayout === 'function') this.options.onApplyLayout(val);
            }
        } });
        return dropdown;
    }

    /**
     * Capture current layout (order and widths)
     */
    getCurrentLayout() {
        // Include current order, widths, pinned, and hidden state
        return {
            columns: this.processedColumns.map(c => ({ field: c.field, width: c.width || null, pinned: c.pinned || null })),
            hidden: this.processedColumns.reduce((acc, c) => { acc[c.field] = !!c.hide; return acc; }, {}),
            sortState: { ...this.sortState }
        };
    }

    /**
     * Initialize controls header with dropdowns and buttons (from old grid)
     */
    initializeControlsHeader() {
        // Create Filters dropdown
        const filtersDropdown = this.createCustomDropdown([
            { value: 'products-with-sales', text: 'Products With Sales' },
            { value: 'with-no-sales', text: 'With No Sales' },
            { divider: true },
            { value: 'uploaded-today', text: 'Uploaded Today' },
            { value: 'uploaded-yesterday', text: 'Uploaded Yesterday' },
            { value: 'uploaded-this-month', text: 'Uploaded This Month' }
        ], 'Filters');

        // Add filter icon to the dropdown
        const filtersTrigger = filtersDropdown.querySelector('.dropdown-header');
        const filterIcon = document.createElement('img');
        filterIcon.src = 'assets/filters-active-ic.svg';
        filterIcon.alt = 'Filters';
        filterIcon.style.width = '12px';
        filterIcon.style.height = '12px';
        filterIcon.style.marginRight = '8px';
        filtersTrigger.insertBefore(filterIcon, filtersTrigger.firstChild);

        this.elements.filtersDropdown.appendChild(filtersDropdown);

        // Add event listener for Filters dropdown
        filtersDropdown.addEventListener('change', (e) => {
            this.handleFilterDropdownChange(e.detail.value);
        });

        // Create Default Layout dropdown with custom styling
        const layoutDropdown = this.createOldLayoutDropdown();

        // Add layout icon to the dropdown
        const layoutTrigger = layoutDropdown.querySelector('.dropdown-header');
        const layoutIcon = document.createElement('img');
        layoutIcon.src = 'assets/show-hide-col-active-ic.svg';
        layoutIcon.alt = 'Layout';
        layoutIcon.style.width = '12px';
        layoutIcon.style.height = '12px';
        layoutIcon.style.marginRight = '8px';
        layoutTrigger.insertBefore(layoutIcon, layoutTrigger.firstChild);

        this.elements.layoutDropdown.appendChild(layoutDropdown);

        // Setup Layout dropdown event listener
        layoutDropdown.addEventListener('change', (e) => {
            this.handleLayoutDropdownChange(e.detail.value);
        });


    }

    /**
     * Create snap dropdown component (from old grid)
     */
    createCustomDropdown(options, defaultValue = '') {
        const dropdown = document.createElement('div');
        dropdown.className = 'snap-dropdown';

        const trigger = document.createElement('div');
        trigger.className = 'dropdown-header';

        const triggerText = document.createElement('span');
        triggerText.textContent = defaultValue;

        const arrow = document.createElement('img');
        arrow.src = 'assets/dropdown-ic.svg';
        arrow.alt = 'Dropdown';
        arrow.className = 'dropdown-arrow';

        trigger.appendChild(triggerText);
        trigger.appendChild(arrow);

        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';

        // Parse options (can be HTML string or array)
        let optionsList = [];
        if (typeof options === 'string') {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = options;
            const optionElements = tempDiv.querySelectorAll('option');
            optionsList = Array.from(optionElements).map(opt => ({
                value: opt.value,
                text: opt.textContent
            }));
        } else if (Array.isArray(options)) {
            optionsList = options;
        }

        // Check if defaultValue matches any option value
        const matchingOption = optionsList.find(option => option.value === defaultValue);

        optionsList.forEach(option => {
            // Check if this is a divider
            if (option.divider) {
                const dividerElement = this.createMenuSeparator();
                menu.appendChild(dividerElement);
                return;
            }

            const optionElement = document.createElement('div');
            optionElement.className = 'dropdown-item';
            optionElement.textContent = option.text || option.value;
            optionElement.setAttribute('data-value', option.value);

            // Only add selected class if defaultValue matches an actual option value
            if (matchingOption && option.value === defaultValue) {
                optionElement.classList.add('selected');
                triggerText.textContent = option.text || option.value;
            }

            optionElement.addEventListener('click', (e) => {
                e.stopPropagation();

                // Check if dropdown is disabled
                if (dropdown.classList.contains('disabled')) {
                    console.log('🚫 Dropdown item click blocked - dropdown is disabled');
                    return; // Don't process click if disabled
                }

                // Update selected option
                menu.querySelectorAll('.dropdown-item').forEach(opt => {
                    opt.classList.remove('selected');
                });
                optionElement.classList.add('selected');
                triggerText.textContent = optionElement.textContent;

                // Close dropdown
                dropdown.classList.remove('focused');
                menu.classList.add('hidden');
                // Ensure ALL dropdowns lose focus when an item is selected
                document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                    d.classList.remove('focused');
                    const m = d.querySelector('.dropdown-menu');
                    if (m) m.classList.add('hidden');
                });

                // Dispatch change event
                const changeEvent = new CustomEvent('change', {
                    detail: { value: option.value, text: option.text || option.value }
                });
                dropdown.dispatchEvent(changeEvent);
            });

            menu.appendChild(optionElement);
        });

        // Toggle dropdown
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();

            // Check if dropdown is disabled
            if (dropdown.classList.contains('disabled')) {
                return; // Don't open if disabled
            }

            // Close any open column menus before opening dropdown, but NOT when this dropdown lives inside a column menu
            if (!dropdown.closest('.snap-grid-column-menu')) {
                this.hideColumnMenu();
            }

            const isOpen = !menu.classList.contains('hidden');

            // Close all other dropdowns
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                d.classList.remove('focused');
                const menuEl = d.querySelector('.dropdown-menu');
                if (menuEl) menuEl.classList.add('hidden');
            });

            if (!isOpen) {
                dropdown.classList.add('focused');
                menu.classList.remove('hidden');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            dropdown.classList.remove('focused');
            menu.classList.add('hidden');
        });

        dropdown.appendChild(trigger);
        dropdown.appendChild(menu);

        return dropdown;
    }

    /**
     * Create the Layout dropdown with custom styling and icons (from old grid)
     */
    createOldLayoutDropdown() {
        const dropdown = document.createElement('div');
        dropdown.className = 'snap-dropdown layout-dropdown';

        // Create dropdown header
        const trigger = document.createElement('div');
        trigger.className = 'dropdown-header';

        const triggerText = document.createElement('span');
        triggerText.textContent = 'Default Layout';

        const arrow = document.createElement('img');
        arrow.src = 'assets/dropdown-ic.svg';
        arrow.alt = 'Dropdown';
        arrow.className = 'dropdown-arrow';

        trigger.appendChild(triggerText);
        trigger.appendChild(arrow);

        // Create dropdown menu
        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';

        // Create menu items
        const items = [
            { value: 'default', text: 'Default Layout', icon: 'checked-option-ic.svg', selected: true, isLayout: true },
            { value: 'custom', text: 'Custom', icon: 'checked-option-ic.svg', selected: false, isLayout: true, hidden: true }, // Hidden by default
            { divider: true },
            { value: 'save', text: 'Save Layout', icon: 'save-layout-ic.svg', selected: false, isLayout: false },
            { value: 'delete', text: 'Delete Selected', icon: 'delete-ic.svg', selected: false, isLayout: false }
        ];

        items.forEach(item => {
            if (item.divider) {
                const dividerElement = this.createMenuSeparator();
                menu.appendChild(dividerElement);
                return;
            }

            const optionElement = document.createElement('div');
            optionElement.className = 'dropdown-item layout-item';
            optionElement.setAttribute('data-value', item.value);

            // Hide custom layout option by default
            if (item.hidden) {
                optionElement.style.display = 'none';
                optionElement.setAttribute('data-hidden', 'true');
            }

            // Create icon container (always reserve space for icon)
            const iconContainer = document.createElement('div');
            iconContainer.className = 'item-icon';
            iconContainer.style.width = '16px';
            iconContainer.style.height = '16px';
            iconContainer.style.marginRight = '8px';
            iconContainer.style.display = 'flex';
            iconContainer.style.alignItems = 'center';
            iconContainer.style.justifyContent = 'center';

            // Add icon based on item type
            if (item.isLayout) {
                // For layout items, show check icon only if selected
                const icon = document.createElement('img');
                icon.src = `assets/${item.icon}`;
                icon.alt = item.text;
                icon.className = `check-icon ${item.selected ? '' : 'hidden'}`;
                icon.style.width = '12px';
                icon.style.height = '12px';
                iconContainer.appendChild(icon);
            } else {
                // For action items, always show their specific icon
                const icon = document.createElement('img');
                icon.src = `assets/${item.icon}`;
                icon.alt = item.text;
                icon.style.width = '12px';
                icon.style.height = '12px';
                iconContainer.appendChild(icon);
            }

            // Create text container
            const textContainer = document.createElement('span');
            textContainer.textContent = item.text;
            textContainer.className = 'item-text';

            // Assemble the option
            optionElement.appendChild(iconContainer);
            optionElement.appendChild(textContainer);

            // Add selected class if this is the default selected item
            if (item.selected) {
                optionElement.classList.add('selected');
            }

            // Add click handler
            optionElement.addEventListener('click', (e) => {
                e.stopPropagation();

                if (item.isLayout) {
                    // Only layout items can be selected
                    // Update selection using the same logic as saved layouts
                    this.updateLayoutSelection(optionElement);

                    // Update header text
                    triggerText.textContent = item.text;
                }

                // Close dropdown
                dropdown.classList.remove('focused');
                menu.classList.add('hidden');
                // Ensure ALL dropdowns lose focus when an item is selected
                document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                    d.classList.remove('focused');
                    const m = d.querySelector('.dropdown-menu');
                    if (m) m.classList.add('hidden');
                });

                // Dispatch change event
                const changeEvent = new CustomEvent('change', {
                    detail: { value: item.value, text: item.text }
                });
                dropdown.dispatchEvent(changeEvent);

                // If user clicked Custom in the list, ensure it exists/updates
                if (item.value === 'custom') {
                    this.saveOrUpdateCustomLayout();
                }
            });

            menu.appendChild(optionElement);
        });

        // Toggle dropdown
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();

            // Check if dropdown is disabled
            if (dropdown.classList.contains('disabled')) {
                return; // Don't open if disabled
            }

            // Close any open column menus before opening dropdown
            this.hideColumnMenu();

            const isOpen = !menu.classList.contains('hidden');

            // Close all other dropdowns
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                d.classList.remove('focused');
                const menuEl = d.querySelector('.dropdown-menu');
                if (menuEl) menuEl.classList.add('hidden');
            });

            if (!isOpen) {
                // Load saved layouts when opening dropdown
                this.updateLayoutDropdown();

                // Update selection state to match current layout
                this.updateCurrentLayoutSelection();

                dropdown.classList.add('focused');
                menu.classList.remove('hidden');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            dropdown.classList.remove('focused');
            menu.classList.add('hidden');
        });

        dropdown.appendChild(trigger);
        dropdown.appendChild(menu);

        // Load saved layouts into the dropdown
        this.updateLayoutDropdown();

        return dropdown;
    }

    /**
     * Setup delete dropdown functionality
     */
    setupDeleteDropdown() {
        if (!this.controls.deleteBtn) return;

        // Create dropdown container
        const dropdown = document.createElement('div');
        dropdown.className = 'snap-dropdown delete-dropdown';
        dropdown.style.position = 'relative';
        dropdown.style.display = 'inline-block';

        // Create trigger button (reuse existing delete button styling)
        const trigger = this.controls.deleteBtn.cloneNode(true);
        trigger.className = 'snap-grid-delete-btn';
        trigger.style.position = 'relative';

        // Create dropdown menu
        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';
        menu.style.position = 'absolute';
        menu.style.top = 'calc(100% + 4px)';
        menu.style.left = '0';
        menu.style.zIndex = '1000';
        menu.style.minWidth = '200px';

        // Create menu items
        const items = [
            { text: 'Delete Product', action: 'delete-product' },
            { text: 'Delete Design', action: 'delete-design' }
        ];

        items.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'dropdown-item';
            itemElement.textContent = item.text;
            itemElement.setAttribute('data-action', item.action);
            itemElement.style.padding = '8px 12px';
            itemElement.style.cursor = 'pointer';
            itemElement.style.fontFamily = 'Amazon Ember, sans-serif';
            itemElement.style.fontWeight = '500';
            itemElement.style.fontSize = '12px';
            itemElement.style.color = 'var(--text-primary)';

            // Hover effect
            itemElement.addEventListener('mouseenter', () => {
                itemElement.style.background = '#F3F4F6';
            });
            itemElement.addEventListener('mouseleave', () => {
                itemElement.style.background = 'transparent';
            });

            // Click handler
            itemElement.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handleDeleteAction(item.action);
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
            });

            menu.appendChild(itemElement);
        });

        // Replace original button with dropdown
        this.controls.deleteBtn.parentNode.replaceChild(dropdown, this.controls.deleteBtn);
        dropdown.appendChild(trigger);
        dropdown.appendChild(menu);

        // Update reference
        this.controls.deleteBtn = trigger;
        this.controls.deleteDropdown = dropdown;

        // Toggle dropdown on click
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            // Guard: show only when there is a selection
            if (!this.selectedRows || this.selectedRows.size === 0) {
                return;
            }

            const isOpen = !menu.classList.contains('hidden');

            // Close all other dropdowns
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                d.classList.remove('focused');
                const menuEl = d.querySelector('.dropdown-menu');
                if (menuEl) menuEl.classList.add('hidden');
            });

            if (!isOpen) {
                menu.classList.remove('hidden');
                dropdown.classList.add('focused');
            } else {
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!dropdown.contains(e.target)) {
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
            }
        });

        // Initial visibility based on selection
        const hasSelectionInit = this.selectedRows && this.selectedRows.size > 0;
        trigger.style.display = hasSelectionInit ? 'flex' : 'none';
    }

    /**
     * Setup export dropdown functionality
     */
    setupExportDropdown() {
        if (!this.controls.exportBtn) return;

        // Create dropdown container
        const dropdown = document.createElement('div');
        dropdown.className = 'snap-dropdown export-dropdown';
        dropdown.style.position = 'relative';
        dropdown.style.display = 'inline-block';

        // Create trigger button (reuse existing export button styling)
        const trigger = this.controls.exportBtn.cloneNode(true);
        trigger.className = 'snap-grid-export-btn';
        trigger.style.position = 'relative';

        // Create dropdown menu
        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';
        menu.style.position = 'absolute';
        menu.style.top = 'calc(100% + 4px)';
        menu.style.left = '0';
        menu.style.zIndex = '1000';
        menu.style.minWidth = '200px';

        // Create menu items
        const items = [
            { text: 'Export Selected (CSV)', action: 'export-current' },
            { text: 'Export ASINs (CSV)', action: 'export-asins' },
            { text: 'Copy ASINs to Clipboard', action: 'copy-asins' }
        ];

        items.forEach(item => {
            const itemElement = document.createElement('div');
            itemElement.className = 'dropdown-item';
            itemElement.textContent = item.text;
            itemElement.setAttribute('data-action', item.action);
            itemElement.style.padding = '8px 12px';
            itemElement.style.cursor = 'pointer';
            itemElement.style.fontFamily = 'Amazon Ember, sans-serif';
            itemElement.style.fontWeight = '500';
            itemElement.style.fontSize = '12px';
            itemElement.style.color = 'var(--text-primary)';

            // Hover effect
            itemElement.addEventListener('mouseenter', () => {
                itemElement.style.background = '#F3F4F6';
            });
            itemElement.addEventListener('mouseleave', () => {
                itemElement.style.background = 'transparent';
            });

            // Click handler
            itemElement.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handleExportAction(item.action);
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
            });

            menu.appendChild(itemElement);
        });

        // Replace original button with dropdown
        this.controls.exportBtn.parentNode.replaceChild(dropdown, this.controls.exportBtn);
        dropdown.appendChild(trigger);
        dropdown.appendChild(menu);

        // Update reference
        this.controls.exportBtn = trigger;
        this.controls.exportDropdown = dropdown;

        // Toggle dropdown on click
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();
            // Guard: show only when there is a selection
            if (!this.selectedRows || this.selectedRows.size === 0) {
                return;
            }

            const isOpen = !menu.classList.contains('hidden');

            // Close all other dropdowns
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                d.classList.remove('focused');
                const menuEl = d.querySelector('.dropdown-menu');
                if (menuEl) menuEl.classList.add('hidden');
            });

            if (!isOpen) {
                menu.classList.remove('hidden');
                dropdown.classList.add('focused');
            } else {
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!dropdown.contains(e.target)) {
                menu.classList.add('hidden');
                dropdown.classList.remove('focused');
            }
        });

        // Initial visibility based on selection
        const hasSelectionInit = this.selectedRows && this.selectedRows.size > 0;
        trigger.style.display = hasSelectionInit ? 'flex' : 'none';
    }

    /**
     * Set up virtual scrolling
     */
    setupVirtualScrolling() {
        if (!this.options.virtualScrolling) return;

        // Create scrollable container
        this.scrollContainer = document.createElement('div');
        this.scrollContainer.className = 'snap-grid-scroll-container';
        this.bodyElement.appendChild(this.scrollContainer);

        // Calculate total height
        this.updateVirtualScrolling();
    }

    /**
     * Update virtual scrolling calculations
     */
    updateVirtualScrolling() {
        if (!this.options.virtualScrolling) return;

        const rowCount = this.sortedIdx.length;
        this.totalHeight = rowCount * this.options.rowHeight;
        const capped = Math.min(this.totalHeight, this._MAX_SCROLL_PX);
        this._scrollScale = (capped > 0) ? (this.totalHeight / capped) : 1;

        if (this.scrollContainer) {
            this.scrollContainer.style.height = `${capped}px`;
        }

        this.calculateVisibleRange();
    }

    /**
     * Calculate which rows should be visible
     */
    calculateVisibleRange() {
        if (!this.options.virtualScrolling) {
            this.visibleStartIndex = 0;
            this.visibleEndIndex = this.sortedIdx.length;
            return;
        }

        // Check if bodyElement is initialized (similar to AG Grid's null checks)
        if (!this.bodyElement) {
            this.visibleStartIndex = 0;
            this.visibleEndIndex = this.sortedIdx.length;
            return;
        }

        const containerHeight = this.bodyElement.clientHeight;
        const rowHeight = this.options.rowHeight;
        const bufferSize = this.options.bufferSize;
        const scale = this._scrollScale || 1;
        const virtualTop = this.scrollTop * scale;
        const virtualBottom = (this.scrollTop + containerHeight) * scale;

        this.visibleStartIndex = Math.max(0, Math.floor(virtualTop / rowHeight) - bufferSize);
        this.visibleEndIndex = Math.min(
            this.sortedIdx.length,
            Math.ceil(virtualBottom / rowHeight) + bufferSize
        );
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Scroll sync and virtualization
        if (this.bodyElement) {
            try {
                this.bodyElement.addEventListener('scroll', this.handleScroll.bind(this), { passive: true });
            } catch (e) {
                // Fallback for older browsers
                this.bodyElement.addEventListener('scroll', this.handleScroll.bind(this));
            }
        }

        // No dedicated horizontal scrollbar - use body scrollbar only

        // Window resize
        window.addEventListener('resize', this.handleResize.bind(this));

        // Click events (delegated)
        this.container.addEventListener('click', this.handleClick.bind(this));

        // Keyboard events
        this.container.addEventListener('keydown', this.handleKeydown.bind(this));

        // Column resize events
        if (this.options.resizable) {
            this.setupColumnResize();
        }

        // Column dragging events
        if (this.options.columnDragging) {
            this.container.addEventListener('dragstart', this.handleDragStart.bind(this));
            this.container.addEventListener('dragover', this.handleDragOver.bind(this));
            this.container.addEventListener('drop', this.handleDrop.bind(this));
            this.container.addEventListener('dragend', this.handleDragEnd.bind(this));
        }
    }

    /**
     * Handle scroll events for virtual scrolling and menu tracking - exact copy from old grid
     */
    handleScroll(event) {
        const target = event.target;
        const newLeft = target.scrollLeft || 0;
        const newTop = target.scrollTop || 0;

        // Horizontal: keep header in sync
        if (newLeft !== this.scrollLeft) {
            this.scrollLeft = newLeft;
            this.syncHorizontalScroll();
        }

        // Vertical: only update virtualization if enabled and changed
        if (this.options.virtualScrolling && newTop !== this.scrollTop) {
            this.scrollTop = newTop;
            if (!this._scrollRaf) {
                this._scrollRaf = requestAnimationFrame(() => {
                    this._scrollRaf = null;
                    this.calculateVisibleRange();
                    this.renderRows();
                    this.updateHeaderFooterStats();
                });
            }
        }

        // Update active menu position if it exists, with collision detection - exact copy from old grid
        if (this.activeMenu && this.activeMenuTarget) {
            // Skip repositioning if suppressed (during preset filter apply)
            if (this.suppressMenuReposition) {
                console.log('🚫 Menu reposition suppressed in scroll handler (preset filter)');
                return;
            }
            try {
                this.positionMenuWithCollisionDetection(this.activeMenu, this.activeMenuTarget);
            } catch (error) {
                console.warn('Error repositioning menu during scroll:', error);
                // Don't close the menu, just skip repositioning this time
            }
        }
    }

    // Removed handleHScroll - using body scrollbar only

    /**
     * Handle window resize
     */
    handleResize() {
        this.calculateVisibleRange();
        this.render();
        this.updateHorizontalMetrics();
        this.syncHorizontalScroll();
    }

    /**
     * Handle click events
     */
    handleClick(event) {
        const target = event.target;

        // Column menu button click
        if (target.closest('.snap-grid-column-menu-btn')) {
            // Already handled in the button's event listener
            return;
        }

        // Checkbox click handling
        if (target.closest('.snap-grid-checkbox-cell')) {
            this.handleCheckboxClick(event);
            return;
        }

        // Header checkbox click
        if (target.closest('.snap-grid-checkbox-header')) {
            this.handleHeaderCheckboxClick(event);
            return;
        }

        // Header click for sorting
        if (target.closest('.snap-grid-header-cell')) {
            this.handleHeaderClick(event);
        }

        // Row click for selection
        if (target.closest('.snap-grid-row')) {
            this.handleRowClick(event);
        }

        // Cell click for editing
        if (target.closest('.snap-grid-cell')) {
            this.handleCellClick(event);
        }
    }

    /**
     * Handle keyboard events
     */
    handleKeydown(event) {
        // Arrow key navigation
        if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
            this.handleArrowKeys(event);
        }

        // Enter for editing
        if (event.key === 'Enter') {
            this.handleEnterKey(event);
        }

        // Escape to cancel editing
        if (event.key === 'Escape') {
            this.handleEscapeKey(event);
        }

        // Ctrl/Cmd + A: Select all
        if ((event.ctrlKey || event.metaKey) && event.key === 'a') {
            event.preventDefault();
            this.toggleAllRowsSelection();
        }
    }

    /**
     * Handle delete action based on selection
     */
    handleDeleteAction(action) {
        const selectedData = this.getSelectedData();

        if (selectedData.length === 0) {
            console.warn('No rows selected for delete action');
            return;
        }

        console.log(`Delete action: ${action}`, selectedData);

        switch (action) {
            case 'delete-product':
                // Show confirmation popup instead of immediate action
                this.showDeleteProductPopup(selectedData);
                break;
            case 'delete-design':
                // Show confirmation popup instead of immediate action
                this.showDeleteDesignPopup(selectedData);
                break;
            default:
                console.warn('Unknown delete action:', action);
        }
    }

    /**
     * Handle export action based on selection
     */
    handleExportAction(action) {
        console.log(`Export action: ${action}`);

        switch (action) {
            case 'export-current':
                // Use existing CSV export functionality
                this.exportToCsv();
                break;
            case 'export-asins':
                // Call callback if provided, otherwise use fallback
                if (typeof this.options.onExportAsins === 'function') {
                    this.options.onExportAsins(this.getSelectedData());
                } else {
                    this.exportAsinsToCsv();
                }
                break;
            case 'copy-asins':
                // Call callback if provided, otherwise use fallback
                if (typeof this.options.onCopyAsins === 'function') {
                    this.options.onCopyAsins(this.getSelectedData());
                } else {
                    this.copyAsinsToClipboard();
                }
                break;
            default:
                console.warn('Unknown export action:', action);
        }
    }

    /**
     * Export ASINs to CSV (fallback implementation)
     */
    exportAsinsToCsv() {
        const selectedData = this.getSelectedData();
        if (selectedData.length === 0) {
            console.warn('No rows selected for ASIN export');
            return;
        }

        // Extract ASINs from selected data
        const asins = selectedData.map(row => {
            // Look for ASIN field in various possible names
            return row.asin || row.ASIN || row.productId || row.id || '';
        }).filter(asin => asin);

        if (asins.length === 0) {
            console.warn('No ASINs found in selected data');
            return;
        }

        // Create CSV content
        const csvContent = 'ASIN\n' + asins.join('\n');

        // Download CSV
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'asins.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log(`Exported ${asins.length} ASINs to CSV`);
    }

    /**
     * Copy ASINs to clipboard (fallback implementation)
     */
    copyAsinsToClipboard() {
        const selectedData = this.getSelectedData();
        if (selectedData.length === 0) {
            console.warn('No rows selected for ASIN copy');
            return;
        }

        // Extract ASINs from selected data
        const asins = selectedData.map(row => {
            // Look for ASIN field in various possible names
            return row.asin || row.ASIN || row.productId || row.id || '';
        }).filter(asin => asin);

        if (asins.length === 0) {
            console.warn('No ASINs found in selected data');
            return;
        }

        // Copy to clipboard
        const asinText = asins.join('\n');
        navigator.clipboard.writeText(asinText).then(() => {
            this.showNotification(`${asins.length.toLocaleString()} ASIN${asins.length === 1 ? '' : 's'} copied to clipboard.`, 'success');
        }).catch(err => {
            this.showNotification('Failed to copy ASINs to clipboard. Please try again.', 'warning');
            console.error('Failed to copy ASINs to clipboard:', err);
        });
    }

    /**
     * Handle filter dropdown selection changes (from old grid)
     */
    handleFilterDropdownChange(filterValue) {
        console.log('🔍 Filter dropdown changed to:', filterValue);

        // Clear any current selection when changing quick filters
        if (this.selectedRows && this.selectedRows.size > 0) {
            this.clearSelection();
        }

        // Quick Filters must NOT affect Custom persistence or toggling
        this._suppressCustomPersist = true;

        // Always operate quick filters on a clean Default baseline regardless of current layout
        // 1) Clear any previously active quick filters
        this.activeQuickFilters.clear();
        // 2) Switch to Default baseline (ordering/sizing) and ensure full visibility and no filters
        //    - Apply default ordering/sizing from original state
        this.applyDefaultLayout();
        //    - Ensure ALL columns are visible per requirement (override any defaults that hide columns)
        try {
            let anyVisibilityChange = false;
            this.processedColumns.forEach(col => {
                if (col.hidden || col.hide) {
                    col.hidden = false;
                    col.hide = false;
                    anyVisibilityChange = true;
                }
            });
            if (anyVisibilityChange) {
                // Re-render only if we actually changed visibility
                this.renderHeader();
                this.renderRows();
            }
        } catch (e) {
            console.warn('⚠️ Failed to force-show all columns for quick filter baseline:', e);
        }
        // 3) Mark that no saved layout is currently active while in quick filter mode
        this.isSavedLayoutActive = false;
        this.currentLayoutType = 'default';

        // Debug: Log all available columns
        console.log('📋 Available columns:', this.processedColumns.map(col => ({
            field: col.field,
            headerName: col.headerName,
            type: col.type
        })));

        // Find the relevant columns for filtering
        const salesColumn = this.processedColumns.find(col =>
            col.field === 'sales' || col.field === 'Sales' ||
            col.headerName === 'Sales' || col.headerName === 'sales'
        );

        const dateColumn = this.processedColumns.find(col =>
            col.field === 'firstPublished' || col.headerName === 'First Published' ||
            col.field === 'firstPublishedDate' || col.field === 'First Published Date' ||
            col.headerName === 'First Published Date' || col.headerName === 'firstPublishedDate' ||
            col.field === 'publishedDate' || col.headerName === 'Published Date'
        );

        console.log('🔍 Found sales column:', salesColumn);
        console.log('🔍 Found date column:', dateColumn);

        // Clear opposite category filters when switching between sales and uploaded filters
        const isSalesFilter = ['products-with-sales', 'with-no-sales'].includes(filterValue);
        const isUploadedFilter = ['uploaded-today', 'uploaded-yesterday', 'uploaded-this-month'].includes(filterValue);

        if (isSalesFilter && dateColumn) {
            // Clear uploaded filters when switching to sales filters
            console.log('🧹 Clearing uploaded filters before applying sales filter');
            this.clearFilter(dateColumn.field);
            // Remove uploaded filters from active set
            this.activeQuickFilters.delete('uploaded-today');
            this.activeQuickFilters.delete('uploaded-yesterday');
            this.activeQuickFilters.delete('uploaded-this-month');
        } else if (isUploadedFilter && salesColumn) {
            // Clear sales filters when switching to uploaded filters
            console.log('🧹 Clearing sales filters before applying uploaded filter');
            this.clearFilter(salesColumn.field);
            // Remove sales filters from active set
            this.activeQuickFilters.delete('products-with-sales');
            this.activeQuickFilters.delete('with-no-sales');
        }

        switch (filterValue) {
            case 'products-with-sales':
                if (salesColumn) {
                    console.log('🎯 Applying "Products With Sales" filter to column:', salesColumn.field);
                    // Ensure the Sales column is visible (do not switch layout when quick filter active)
                    this.ensureColumnVisible(salesColumn.field);
                    // Apply the filter silently
                    this.setFilter(salesColumn.field, {
                        type: 'number',
                        operator: 'greaterThan',
                        value: 0
                    });
                    // Track active filter
                    this.activeQuickFilters.add('products-with-sales');
                    console.log('✅ Applied "Products With Sales" filter silently');
                } else {
                    console.warn('⚠️ Could not find Sales column for filtering');
                }
                break;

            case 'with-no-sales':
                if (salesColumn) {
                    console.log('🎯 Applying "With No Sales" filter to column:', salesColumn.field);
                    console.log('🎯 Filter config:', {
                        type: 'number',
                        operator: 'equals',
                        value: 0
                    });
                    // Ensure the Sales column is visible (do not switch layout when quick filter active)
                    this.ensureColumnVisible(salesColumn.field);
                    // Apply the filter silently
                    this.setFilter(salesColumn.field, {
                        type: 'number',
                        operator: 'equals',
                        value: 0
                    });
                    // Track active filter
                    this.activeQuickFilters.add('with-no-sales');
                    console.log('✅ Applied "With No Sales" filter silently');
                    console.log('🔍 Current filterConfig after setFilter:', this.filterState);
                } else {
                    console.warn('⚠️ Could not find Sales column for filtering');
                }
                break;

            case 'uploaded-today':
                if (dateColumn) {
                    // Ensure the Date column is visible (do not switch layout when quick filter active)
                    this.ensureColumnVisible(dateColumn.field);
                    this.setFilter(dateColumn.field, {
                        type: 'date',
                        operator: 'today',
                        value: null
                    });
                    // Track active filter
                    this.activeQuickFilters.add('uploaded-today');
                    console.log('✅ Applied "Uploaded Today" filter using predefined "today" option');
                } else {
                    console.warn('⚠️ Could not find Date column for filtering');
                }
                break;

            case 'uploaded-yesterday':
                if (dateColumn) {
                    // Ensure the Date column is visible (do not switch layout when quick filter active)
                    this.ensureColumnVisible(dateColumn.field);
                    this.setFilter(dateColumn.field, {
                        type: 'date',
                        operator: 'yesterday',
                        value: null
                    });
                    // Track active filter
                    this.activeQuickFilters.add('uploaded-yesterday');
                    console.log('✅ Applied "Uploaded Yesterday" filter using predefined "yesterday" option');
                } else {
                    console.warn('⚠️ Could not find Date column for filtering');
                }
                break;

            case 'uploaded-this-month':
                if (dateColumn) {
                    // Ensure the Date column is visible (do not switch layout when quick filter active)
                    this.ensureColumnVisible(dateColumn.field);
                    this.setFilter(dateColumn.field, {
                        type: 'date',
                        operator: 'currentMonth',
                        value: null
                    });
                    // Track active filter
                    this.activeQuickFilters.add('uploaded-this-month');
                    console.log('✅ Applied "Uploaded This Month" filter using predefined "currentMonth" option');
                } else {
                    console.warn('⚠️ Could not find Date column for filtering');
                }
                break;

            default:
                console.log('ℹ️ No filter applied for value:', filterValue);
                break;
        }

        // Update the Filters dropdown header text to show the selected filter
        this.updateFiltersDropdownSelection(filterValue);

        // Update quick filter state/UI and keep layout controls disabled while active
        this.updateFilterState();

        // Re-enable Custom persistence after quick filter completes (but remain isolated from Custom/Saved)
        this._suppressCustomPersist = false;
    }

    /**
     * Handle layout dropdown selection changes (from old grid)
     */
    handleLayoutDropdownChange(value) {
        console.log('🎨 Layout dropdown changed to:', value);

        // Check if quick filters are active
        if (this.isQuickFilterActive) {
            console.log('⚠️ Cannot change layout while quick filters are active');
            // Don't show tooltip here - dropdown should be fully disabled
            return;
        }

        // Clear any current selection when changing layouts
        if (this.selectedRows && this.selectedRows.size > 0) {
            this.clearSelection();
        }

        switch (value) {
            case 'default':
                console.log('✅ Default Layout selected');
                this.applyDefaultLayout();
                this.currentLayoutType = 'default';
                this.isSavedLayoutActive = false;
                this.updateFilterState();
                break;

            case 'custom':
                console.log('✅ Custom Layout selected');
                // Load and apply the Custom layout from localStorage
                const savedLayouts = this.getSavedLayouts();
                const customLayout = savedLayouts.find(layout =>
                    layout.name && layout.name.toLowerCase() === 'custom'
                );
                if (customLayout && customLayout.layout) {
                    this.applyLayout(customLayout.layout);
                }
                this.currentLayoutType = 'custom';
                this.isSavedLayoutActive = false;
                this.updateFilterState();
                break;

            case 'save':
                console.log('💾 Save Layout selected');
                // Use existing callback or implement basic save functionality
                if (typeof this.options.onSaveLayout === 'function') {
                    this.options.onSaveLayout(this.getCurrentLayout());
                } else {
                    this.saveLayoutWithPrompt();
                }
                break;

            case 'delete':
                console.log('🗑️ Delete Selected selected');
                // Implement delete selected layout functionality
                this.deleteSelectedLayout();
                break;

            default:
                // Check if it's a saved layout ID
                if (value.startsWith('layout_')) {
                    const savedLayouts = this.getSavedLayouts();
                    const layout = savedLayouts.find(l => l.id === value);
                    if (layout) {
                        console.log('🎨 Applying saved layout:', layout.name);
                        this.applyLayout(layout);
                        this.currentLayoutType = value;
                        this.isSavedLayoutActive = true;
                        this.updateFilterState();
                    }
                } else {
                    console.log('ℹ️ Unknown layout option:', value);
                }
                break;
        }
    }



    /** Determine if any filters are currently active (quick filters or column filters) */
    hasAnyActiveFilters() {
        if (this.activeQuickFilters && this.activeQuickFilters.size > 0) return true;

        // Check filter/checkbox state per column
        const fields = new Set([
            ...Object.keys(this.filterState || {}),
            ...Object.keys(this.checkboxState || {})
        ]);
        // Also consider processed columns if no explicit keys
        if (fields.size === 0 && Array.isArray(this.processedColumns)) {
            this.processedColumns.forEach(c => {
                if (c && c.field) fields.add(c.field);
            });
        }
        for (const field of fields) {
            if (this.isFilterActive && this.isFilterActive(field)) return true;
        }
        return false;
    }

    /**
     * Update filter state and UI based on active filters
     */
    updateFilterState() {
        this.isQuickFilterActive = this.activeQuickFilters.size > 0;



        // Update dropdown states
        this.updateDropdownStates();

        // Update filter labels
        this.updateFilterLabels();
    }

    /**
     * Get list of columns with active filters
     */
    getActiveColumnFilters() {
        const activeFilters = [];

        // Check filter state (text/number filters)
        if (this.filterState) {
            Object.keys(this.filterState).forEach(field => {
                if (this.isFilterActive(field)) {
                    activeFilters.push(field);
                }
            });
        }

        // Check checkbox state (checkbox filters)
        if (this.checkboxState) {
            Object.keys(this.checkboxState).forEach(field => {
                if (this.isFilterActive(field)) {
                    // Only add if not already in the list
                    if (!activeFilters.includes(field)) {
                        activeFilters.push(field);
                    }
                }
            });
        }

        return activeFilters;
    }

    /**
     * Get user-friendly display name for a column
     */
    getColumnDisplayName(field) {
        const column = this.processedColumns.find(col => col.field === field);
        return column ? (column.headerName || column.field) : field;
    }

    /**
     * Create individual filter label element
     */
    createFilterLabel(field, displayName) {
        const label = document.createElement('div');
        label.className = 'snap-grid-filter-label';
        label.dataset.field = field;

        // Label content wrapper
        const content = document.createElement('div');
        content.className = 'snap-grid-filter-label-content';

        // Filter icon
        const iconWrapper = document.createElement('div');
        iconWrapper.className = 'snap-grid-filter-label-icon';
        const icon = document.createElement('img');
        icon.src = 'assets/filters-active-ic.svg';
        icon.alt = 'Filter';
        iconWrapper.appendChild(icon);

        // Label text
        const text = document.createElement('span');
        text.className = 'snap-grid-filter-label-text';
        text.textContent = displayName;

        content.appendChild(iconWrapper);
        content.appendChild(text);

        // Close button
        const closeBtn = document.createElement('div');
        closeBtn.className = 'snap-grid-filter-label-close';
        const closeIcon = document.createElement('img');
        closeIcon.src = 'assets/clear-filter-ic.svg';
        closeIcon.alt = 'Clear filter';
        closeBtn.appendChild(closeIcon);

        // Add click handler for close button
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            // Close any open column menus
            this.hideColumnMenu();
            this.clearColumnFilter(field);
        });

        label.appendChild(content);
        label.appendChild(closeBtn);

        return label;
    }

    /**
     * Create Clear All button element
     */
    createClearAllButton() {
        const button = document.createElement('button');
        button.className = 'snap-grid-filter-clear-all';

        // Icon wrapper
        const iconWrapper = document.createElement('div');
        iconWrapper.className = 'snap-grid-filter-clear-all-icon';
        const icon = document.createElement('img');
        icon.src = 'assets/filters-inactive-ic.svg';
        icon.alt = 'Clear all filters';
        iconWrapper.appendChild(icon);

        // Button text
        const text = document.createElement('span');
        text.textContent = 'Clear all';

        button.appendChild(iconWrapper);
        button.appendChild(text);

        // Add click handler
        button.addEventListener('click', () => {
            // Close any open column menus
            this.hideColumnMenu();
            this.clearAllColumnFilters();
        });

        return button;
    }

    /**
     * Update filter labels display
     */
    updateFilterLabels() {
        const container = this.elements?.filterLabelsContainer;
        if (!container) return;

        // Clear existing labels
        container.innerHTML = '';

        // Get active column filters (exclude quick filters)
        const activeFilters = this.getActiveColumnFilters();

        // Hide container if no filters
        if (activeFilters.length === 0) {
            container.style.display = 'none';
            return;
        }

        // Show container
        container.style.display = 'flex';

        // Create labels for each active filter
        activeFilters.forEach(field => {
            const displayName = this.getColumnDisplayName(field);
            const label = this.createFilterLabel(field, displayName);
            container.appendChild(label);
        });

        // Add Clear All button if 2 or more filters
        if (activeFilters.length >= 2) {
            const clearAllBtn = this.createClearAllButton();
            container.appendChild(clearAllBtn);
        }
    }

    /**
     * Get Quick Filters that affect a specific column field
     *
     * Note: Use field-name heuristics so this works regardless of which specific
     * date/sales column was chosen for quick filters (e.g., firstPublished, publishedDate).
     */
    getQuickFiltersForField(field) {
        const f = String(field || '').toLowerCase();

        // Identify if the provided field is a Sales-related field
        const isSalesField = (
            f === 'sales' || f.includes('sales') || f.includes('revenue')
        );

        // Identify if the provided field is a Date/Uploaded-related field
        const isDateField = (
            field === 'firstPublished' ||
            field === 'firstPublishedDate' ||
            field === 'publishedDate' ||
            f.includes('date') ||
            f.includes('created')
        );

        const quickFilters = [];

        if (isSalesField) {
            if (this.activeQuickFilters.has('products-with-sales')) {
                quickFilters.push('products-with-sales');
            }
            if (this.activeQuickFilters.has('with-no-sales')) {
                quickFilters.push('with-no-sales');
            }
        }

        if (isDateField) {
            if (this.activeQuickFilters.has('uploaded-today')) {
                quickFilters.push('uploaded-today');
            }
            if (this.activeQuickFilters.has('uploaded-yesterday')) {
                quickFilters.push('uploaded-yesterday');
            }
            if (this.activeQuickFilters.has('uploaded-this-month')) {
                quickFilters.push('uploaded-this-month');
            }
        }

        return quickFilters;
    }

    /**
     * Clear filter for a specific column (from filter label)
     */
    clearColumnFilter(field) {
        // Check if this field was affected by any Quick Filters
        const affectedQuickFilters = this.getQuickFiltersForField(field);

        // Clear the column filter
        this.clearFilter(field);

        // If this field was affected by Quick Filters, remove them and reset dropdown
        if (affectedQuickFilters.length > 0) {
            affectedQuickFilters.forEach(quickFilter => {
                this.activeQuickFilters.delete(quickFilter);
            });

            // Reset the Quick Filters dropdown back to "Filters"
            this.resetFiltersDropdownSelection();

            console.log('🔄 Reset Quick Filters dropdown after clearing filter for field:', field);
        }

        // Check if this was the last filter being cleared individually
        const remainingColumnFilters = this.getActiveColumnFilters();
        if (remainingColumnFilters.length === 0) {
            // If on custom layout, remove it and toggle to default
            if (this.currentLayoutType === 'custom') {
                console.log('🗑️ Last filter cleared individually - removing custom layout and switching to default');
                this.clearCustomLayoutFromStorage();
                this.applyDefaultLayout();
                this.currentLayoutType = 'default';
                this.updateLayoutDropdown();
            }
            // If on saved layout, toggle to default layout and reset filters dropdown
            else if (this.isSavedLayoutActive) {
                console.log('🔄 Last filter cleared individually from saved layout - switching to default and resetting filters');
                this.applyDefaultLayout();
                this.currentLayoutType = 'default';
                this.isSavedLayoutActive = false;
                this.updateLayoutDropdown();
                this.resetFiltersDropdownSelection();
            }
        }
    }

    /**
     * Clear all column filters (from Clear All button)
     */
    clearAllColumnFilters() {
        // Get all active column filters
        const activeFilters = this.getActiveColumnFilters();

        // Clear each filter
        activeFilters.forEach(field => {
            this.clearFilter(field);
        });

        // Clear all Quick Filters and reset dropdown (since all filters are being cleared)
        if (this.activeQuickFilters.size > 0) {
            this.activeQuickFilters.clear();
            this.resetFiltersDropdownSelection();
            console.log('🔄 Reset Quick Filters dropdown after clearing all filters');
        }

        // When Clear All is clicked, delete custom layout and toggle to default layout
        if (this.currentLayoutType === 'custom') {
            console.log('🗑️ Clear All clicked - removing custom layout and switching to default');
            this.clearCustomLayoutFromStorage();
            this.applyDefaultLayout();
            this.currentLayoutType = 'default';
            this.updateLayoutDropdown();
        }
        // If on saved layout, toggle to default layout and reset filters dropdown
        else if (this.isSavedLayoutActive) {
            console.log('🔄 Clear All clicked from saved layout - switching to default and resetting filters');
            this.applyDefaultLayout();
            this.currentLayoutType = 'default';
            this.isSavedLayoutActive = false;
            this.updateLayoutDropdown();
            this.resetFiltersDropdownSelection();
        }

        // Update filter state
        this.updateFilterState();
    }

    /**
     * Update dropdown states based on current filter/layout state
     */
    updateDropdownStates() {
        // The layout dropdown element itself has the .snap-dropdown class
        const layoutDropdown = this.elements?.layoutDropdown?.querySelector('.snap-dropdown') ||
                              this.elements?.layoutDropdown?.querySelector('.layout-dropdown');
        const filtersDropdown = this.elements?.filtersDropdown?.querySelector('.snap-dropdown') ||
                               document.querySelector('#SnapGrid .snap-grid-filters-dropdown .snap-dropdown');

        // Helper function to apply disabled styling
        const applyDisabledStyling = (dropdown, header, title) => {
            dropdown.classList.add('disabled');
            dropdown.setAttribute('aria-disabled', 'true');
            dropdown.setAttribute('title', title);

            if (header) {
                // Remove any inline styles - let CSS handle the styling
                header.style.borderColor = '';
                header.style.color = '';

                const span = header.querySelector('span');
                const img = header.querySelector('img');
                if (span) span.style.color = '';
                if (img) {
                    img.style.filter = '';
                    img.style.opacity = '';

                    // Change to specific disabled icons
                    if (img.src.includes('dropdown-ic.svg')) {
                        img.src = 'assets/dropdown-ic.svg'; // Keep same arrow icon
                    }
                    // Change main icon to show-hide-col-inactive-ic for layout dropdown
                    const mainIcon = header.parentElement.querySelector('img:not(.dropdown-arrow)');
                    if (mainIcon && !mainIcon.classList.contains('dropdown-arrow')) {
                        mainIcon.src = 'assets/show-hide-col-inactive-ic.svg';
                    }
                }
            }
        };

        // Helper function to remove disabled styling
        const removeDisabledStyling = (dropdown, header) => {
            dropdown.classList.remove('disabled');
            dropdown.removeAttribute('aria-disabled');
            dropdown.removeAttribute('title');

            if (header) {
                header.style.borderColor = '';
                header.style.color = '';

                const span = header.querySelector('span');
                const img = header.querySelector('img');
                if (span) span.style.color = '';
                if (img) {
                    img.style.filter = '';
                    img.style.opacity = '';
                }

                // Restore original icons when enabling
                const mainIcon = header.parentElement.querySelector('img:not(.dropdown-arrow)');
                if (mainIcon && !mainIcon.classList.contains('dropdown-arrow')) {
                    // Restore to active icon for layout dropdown
                    if (mainIcon.src.includes('show-hide-col-inactive-ic.svg')) {
                        mainIcon.src = 'assets/show-hide-col-active-ic.svg';
                    }
                }
            }
        };

        // Handle Layout dropdown state
        if (layoutDropdown) {
            const header = layoutDropdown.querySelector('.dropdown-header');
            if (this.isQuickFilterActive) {
                applyDisabledStyling(layoutDropdown, header, 'Layout is disabled when quick filters are active');
            } else {
                removeDisabledStyling(layoutDropdown, header);
            }
        }

        // Handle Filters dropdown state
        if (filtersDropdown) {
            const header = filtersDropdown.querySelector('.dropdown-header');
            // Disable filters dropdown when Custom or Saved layouts are active
            const isCustomOrSavedLayout = this.currentLayoutType === 'custom' || this.isSavedLayoutActive;

            if (isCustomOrSavedLayout) {
                applyDisabledStyling(filtersDropdown, header, 'Quick filters are disabled when Custom or Saved layouts are active');
            } else {
                removeDisabledStyling(filtersDropdown, header);
            }
        }
    }

    /**
     * Reset filters dropdown selection to default "Filters" text
     */
    resetFiltersDropdownSelection() {
        const filtersDropdown = this.elements?.filtersDropdown?.querySelector('.snap-dropdown');
        if (filtersDropdown) {
            const headerSpan = filtersDropdown.querySelector('.dropdown-header span');
            if (headerSpan) {
                headerSpan.textContent = 'Filters';
            }

            // Clear selected state from all filter items
            const menu = filtersDropdown.querySelector('.dropdown-menu');
            if (menu) {
                menu.querySelectorAll('.dropdown-item').forEach(item => {
                    item.classList.remove('selected');
                });
            }
        }

        // When quick filters show as "Filters" default state, Default layout must be active
        if (this.currentLayoutType !== 'default') {
            console.log('🔄 Quick filters reset to default state - ensuring Default layout is active');
            this.applyDefaultLayout();
            this.currentLayoutType = 'default';
            this.isSavedLayoutActive = false;
            this.updateLayoutDropdown();
        }

        // Update filter state to refresh dropdown states (enable layout dropdown)
        this.updateFilterState();
    }

    /**
     * Update filters dropdown selection to show the selected filter
     */
    updateFiltersDropdownSelection(filterValue) {
        const filtersDropdown = this.elements?.filtersDropdown?.querySelector('.snap-dropdown');
        if (!filtersDropdown) return;

        const headerSpan = filtersDropdown.querySelector('.dropdown-header span');
        const menu = filtersDropdown.querySelector('.dropdown-menu');

        if (!headerSpan || !menu) return;

        // Map filter values to display text
        const filterTextMap = {
            'products-with-sales': 'Products With Sales',
            'with-no-sales': 'With No Sales',
            'uploaded-today': 'Uploaded Today',
            'uploaded-yesterday': 'Uploaded Yesterday',
            'uploaded-this-month': 'Uploaded This Month'
        };

        const displayText = filterTextMap[filterValue];
        if (displayText) {
            // Update header text
            console.log(`📝 Updating Filters dropdown header to: "${displayText}"`);
            headerSpan.textContent = displayText;

            // Clear all selected states first
            menu.querySelectorAll('.dropdown-item').forEach(item => {
                item.classList.remove('selected');
            });

            // Add selected state to the matching item
            const selectedItem = menu.querySelector(`[data-value="${filterValue}"]`);
            if (selectedItem) {
                selectedItem.classList.add('selected');
            }
        }
    }

    /**
     * Show tooltip message with visual feedback
     */
    showTooltip(message) {
        console.log('💬 Tooltip:', message);

        // Create a temporary tooltip element
        const tooltip = document.createElement('div');
        tooltip.className = 'snap-grid-tooltip';
        tooltip.textContent = message;
        tooltip.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: var(--z-tooltip, 1000);
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s ease;
        `;

        document.body.appendChild(tooltip);

        // Fade in
        requestAnimationFrame(() => {
            tooltip.style.opacity = '1';
        });

        // Remove after 3 seconds
        setTimeout(() => {
            tooltip.style.opacity = '0';
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.parentNode.removeChild(tooltip);
                }
            }, 200);
        }, 3000);

        // Also log to console for debugging
        if (window.SnapLogger) {
            window.SnapLogger.info(message);
        }
    }

    /**
     * Handle drag start for column dragging
     */
    handleDragStart(event) {
        const headerText = event.target.closest('.snap-grid-header-text');
        if (!headerText || !headerText.draggable) return;

        const headerCell = headerText.closest('.snap-grid-header-cell');
        if (!headerCell) return;

        const columnIndex = parseInt(headerCell.dataset.index);
        const column = this.processedColumns[columnIndex];

        // Don't allow dragging pinned columns
        if (column.pinned) {
            event.preventDefault();
            return;
        }

        this.isDraggingColumn = true;
        this.draggedColumn = columnIndex;
        this.dragStartX = event.clientX;
        this.dragStartIndex = columnIndex;

        headerCell.classList.add('dragging');

        // Set drag data
        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.setData('text/plain', columnIndex.toString());

        // Set the entire header cell as the drag image instead of just the text
        event.dataTransfer.setDragImage(headerCell, event.offsetX, event.offsetY);

        this.announceToScreenReader(`Started dragging column ${column.headerName || column.field}`);
    }

    /**
     * Handle drag over for column dragging
     */
    handleDragOver(event) {
        if (!this.isDraggingColumn) return;

        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';

        const headerCell = event.target.closest('.snap-grid-header-cell');
        if (!headerCell) return;

        const targetIndex = parseInt(headerCell.dataset.index);
        const targetColumn = this.processedColumns[targetIndex];

        // Don't allow dropping on pinned columns
        if (targetColumn.pinned) return;

        // Remove previous drag-over indicators
        document.querySelectorAll('.snap-grid-header-cell.drag-over').forEach(cell => {
            cell.classList.remove('drag-over');
        });

        // Add drag-over indicator
        if (targetIndex !== this.draggedColumn) {
            headerCell.classList.add('drag-over');
        }
    }

    /**
     * Handle drop for column dragging
     */
    handleDrop(event) {
        if (!this.isDraggingColumn) return;

        event.preventDefault();

        const headerCell = event.target.closest('.snap-grid-header-cell');
        if (!headerCell) return;

        const targetIndex = parseInt(headerCell.dataset.index);
        const targetColumn = this.processedColumns[targetIndex];

        // Don't allow dropping on pinned columns
        if (targetColumn.pinned || targetIndex === this.draggedColumn) return;

        // Move column
        this.moveColumn(this.draggedColumn, targetIndex);

        // Trigger callback
        if (this.options.onColumnMoved) {
            this.options.onColumnMoved(this.draggedColumn, targetIndex);
        }
    }

    /**
     * Handle drag end for column dragging
     */
    handleDragEnd(event) {
        this.isDraggingColumn = false;

        // Remove all drag-related classes
        document.querySelectorAll('.snap-grid-header-cell.dragging').forEach(cell => {
            cell.classList.remove('dragging');
        });

        document.querySelectorAll('.snap-grid-header-cell.drag-over').forEach(cell => {
            cell.classList.remove('drag-over');
        });

        this.draggedColumn = null;
        this.dragStartX = 0;
        this.dragStartIndex = 0;
    }

    /**
     * Move column from one index to another
     */
    moveColumn(fromIndex, toIndex) {
        if (fromIndex === toIndex) return;

        // Store current scroll position to maintain it after reordering
        const currentScrollLeft = this.bodyElement?.scrollLeft || 0;

        // Move column in processedColumns array
        const column = this.processedColumns.splice(fromIndex, 1)[0];
        this.processedColumns.splice(toIndex, 0, column);

        // Update pinned offsets after column reordering
        this.computePinnedOffsets();

        // Re-render the grid
        this.render();

        // Restore horizontal scroll position
        if (this.bodyElement && this.bodyElement.scrollLeft !== currentScrollLeft) {
            this.bodyElement.scrollLeft = currentScrollLeft;
        }

        // Sync horizontal scroll to ensure proper alignment
        this.syncHorizontalScroll();

        // Persist as Custom when column order changes
        this.onUserCustomized && this.onUserCustomized('reorder');

        const columnName = column.headerName || column.field;
        this.announceToScreenReader(`Column ${columnName} moved from position ${fromIndex + 1} to position ${toIndex + 1}`);
    }

    /**
     * Process and prepare data
     */
    processData() {
        // Invalidate unique-values cache on every data/filters recompute
        this._uniqueCache = Object.create(null);
        this._presetDateRangeCache = Object.create(null);
        this._todayContextCache = null;
        // Apply filters using index arrays
        this.applyFilters();
        // Apply sorting using index arrays
        this.applySorting();
        // Update virtual scrolling metrics
        this.updateVirtualScrolling();
    }

    /**
     * Apply current filters to data
     */
    applyFilters() {
        const filters = Object.entries(this.filterState);
        if (!filters.length) {
            this.filteredIdx = this.data.map((_, i) => i);
            this._activeFilterDescriptors = new Map();
            return;
        }

        const descriptors = this._buildFilterDescriptors(filters);
        if (!descriptors.length) {
            this.filteredIdx = this.data.map((_, i) => i);
            return;
        }

        const data = this.data;
        const len = data.length;
        const out = [];
        const start = (typeof performance !== 'undefined' && typeof performance.now === 'function') ? performance.now() : Date.now();

        for (let i = 0; i < len; i++) {
            const row = data[i];
            let pass = true;
            for (let j = 0; j < descriptors.length; j++) {
                if (!this._matchesPreparedFilter(row, descriptors[j], i)) {
                    pass = false;
                    break;
                }
            }
            if (pass) {
                out.push(i);
            }
        }

        this.filteredIdx = out;

        const end = (typeof performance !== 'undefined' && typeof performance.now === 'function') ? performance.now() : Date.now();
        this._lastFilterDuration = end - start;
        if (this._debugDateFiltering) {
            this._logDateDebug('applyFilters metrics', {
                evaluated: len,
                passed: out.length,
                durationMs: this._lastFilterDuration
            });
        }
    }

    _ensureActiveFilterDescriptors() {
        if (!this._activeFilterDescriptors || this._activeFilterDescriptors.size === 0) {
            const entries = Object.entries(this.filterState || {});
            if (!entries.length) {
                this._activeFilterDescriptors = new Map();
                return this._activeFilterDescriptors;
            }
            this._buildFilterDescriptors(entries);
        }
        return this._activeFilterDescriptors;
    }

    _getDescriptorContext(field) {
        const descriptorMap = this._ensureActiveFilterDescriptors();
        if (!descriptorMap || descriptorMap.size === 0) {
            return { descriptorMap: null, fieldDescriptor: null, otherDescriptors: [] };
        }

        let fieldDescriptor = null;
        const otherDescriptors = [];
        descriptorMap.forEach((descriptor, key) => {
            if (key === field) {
                fieldDescriptor = descriptor;
            } else if (descriptor && descriptor.isActive) {
                otherDescriptors.push(descriptor);
            }
        });

        return { descriptorMap, fieldDescriptor, otherDescriptors };
    }

    _buildFilterDescriptors(filterEntries) {
        const descriptors = [];
        const map = new Map();
        for (let i = 0; i < filterEntries.length; i++) {
            const [field, filter] = filterEntries[i];
            if (!filter) continue;
            const descriptor = this._prepareFilterDescriptor(field, filter);
            if (!descriptor || !descriptor.isActive) continue;
            descriptors.push(descriptor);
            if (field) {
                map.set(field, descriptor);
            }
        }
        this._activeFilterDescriptors = map;
        return descriptors;
    }

    _prepareFilterDescriptor(field, filter) {
        const column = field ? this.getColumn(field) : null;
        const type = this._normalizeFilterType(filter?.type, field, column);
        let logic = (filter?.logicOperator || 'AND').toUpperCase();
        if (logic !== 'OR') {
            logic = 'AND';
        }

        const descriptor = {
            field,
            type,
            filter,
            operator: filter?.operator || null,
            secondOperator: filter?.secondOperator || null,
            logicOperator: logic,
            hasFirstCondition: false,
            hasSecondCondition: false,
            firstCondition: null,
            secondCondition: null,
            checkedValuesPresent: Array.isArray(filter?.checkedValues),
            checkedValuesLength: Array.isArray(filter?.checkedValues) ? filter.checkedValues.length : 0,
            checkedSet: null,
            checkboxOnly: false,
            dateCache: null,
            isActive: false
        };

        if (descriptor.checkedValuesPresent && descriptor.checkedValuesLength > 0) {
            descriptor.checkedSet = new Set(filter.checkedValues.map(value => this._stringifyFilterValue(value)));
        }

        if (descriptor.checkedValuesPresent) {
            const op = descriptor.operator;
            descriptor.checkboxOnly = descriptor.type === 'checkbox' || !op || op === 'showAll' || op === 'in';
        }

        if (field && descriptor.type === 'date') {
            descriptor.dateCache = this._ensureDateColumnCache(field);
        }

        descriptor.hasFirstCondition = this._hasActiveCondition(descriptor.type, descriptor.operator, filter?.value);
        descriptor.hasSecondCondition = this._hasActiveCondition(descriptor.type, descriptor.secondOperator, filter?.secondValue);

        if (descriptor.type === 'date') {
            if (descriptor.hasFirstCondition) {
                descriptor.firstCondition = this._normalizeDateCondition(descriptor.operator, filter?.value);
                if (!descriptor.firstCondition || descriptor.firstCondition.skip) {
                    descriptor.hasFirstCondition = false;
                }
            }
            if (descriptor.hasSecondCondition) {
                descriptor.secondCondition = this._normalizeDateCondition(descriptor.secondOperator, filter?.secondValue);
                if (!descriptor.secondCondition || descriptor.secondCondition.skip) {
                    descriptor.hasSecondCondition = false;
                }
            }
        } else {
            if (descriptor.hasFirstCondition) {
                descriptor.firstCondition = {
                    operator: descriptor.operator,
                    value: filter?.value
                };
            }
            if (descriptor.hasSecondCondition) {
                descriptor.secondCondition = {
                    operator: descriptor.secondOperator,
                    value: filter?.secondValue
                };
            }
        }

        descriptor.isActive = descriptor.checkedValuesPresent || descriptor.hasFirstCondition || descriptor.hasSecondCondition;
        return descriptor;
    }

    _hasActiveCondition(type, operator, rawValue) {
        if (!operator || operator === 'showAll' || operator === 'pleaseSelect') {
            return false;
        }
        if (type === 'date' && DATE_PRESET_OPERATORS.has(operator)) {
            return true;
        }
        if (operator === 'inRange') {
            if (rawValue && typeof rawValue === 'object') {
                return this._hasFilterValue(rawValue.fromValue) || this._hasFilterValue(rawValue.toValue);
            }
            return false;
        }
        if (operator === 'blank' || operator === 'notBlank') {
            return true;
        }
        return this._hasFilterValue(rawValue);
    }

    _normalizeFilterType(type, field, column) {
        let normalized = type;
        if (!normalized) {
            if (column) {
                normalized = this.getFilterType(column);
            } else if (field) {
                const colType = this.getColumnType(field);
                switch (colType) {
                    case 'numeric':
                    case 'percentage':
                        normalized = 'number';
                        break;
                    case 'date':
                        normalized = 'date';
                        break;
                    case 'text-special':
                        normalized = 'checkbox';
                        break;
                    default:
                        normalized = 'text';
                }
            } else {
                normalized = 'text';
            }
        }
        switch (normalized) {
            case 'numeric':
            case 'number':
            case 'percentage':
                return 'number';
            case 'date':
                return 'date';
            case 'boolean':
                return 'boolean';
            case 'set':
            case 'checkbox':
                return 'checkbox';
            default:
                return 'text';
        }
    }

    _normalizeDateCondition(operator, rawValue) {
        if (!operator || operator === 'showAll' || operator === 'pleaseSelect') {
            return { skip: true };
        }

        if (operator === 'blank' || operator === 'notBlank') {
            return { operator, mode: 'blank' };
        }

        if (DATE_PRESET_OPERATORS.has(operator)) {
            return {
                operator,
                mode: 'preset',
                range: this._getPresetDateRange(operator)
            };
        }

        if (operator === 'inRange') {
            let fromValue = null;
            let toValue = null;

            if (rawValue && typeof rawValue === 'object') {
                fromValue = rawValue.fromValue ?? null;
                toValue = rawValue.toValue ?? null;
            }

            const fromDate = fromValue ? this.parseDate(fromValue) : null;
            const toDate = toValue ? this.parseDate(toValue) : null;

            const fromValid = fromDate && !Number.isNaN(fromDate.getTime());
            const toValid = toDate && !Number.isNaN(toDate.getTime());

            const condition = {
                operator,
                mode: 'range',
                hasFrom: fromValid,
                hasTo: toValid,
                fromDayStart: fromValid ? this._getDayStartTimestamp(fromDate) : null,
                toDayEnd: toValid ? this._getDayEndTimestamp(toDate) : null
            };

            if (fromValid && toValid && condition.fromDayStart > condition.toDayEnd) {
                condition.fromDayStart = this._getDayStartTimestamp(toDate);
                condition.toDayEnd = this._getDayEndTimestamp(fromDate);
            }

            return condition;
        }

        const filterDate = this.parseDate(rawValue);
        if (!filterDate || Number.isNaN(filterDate.getTime())) {
            return { operator, mode: 'invalid', invalid: true };
        }

        return {
            operator,
            mode: 'compare',
            filterTime: filterDate.getTime(),
            filterDayStart: this._getDayStartTimestamp(filterDate),
            filterDayEnd: this._getDayEndTimestamp(filterDate)
        };
    }

    _evaluatePreparedCondition(descriptor, condition, rawValue, rowIndex) {
        if (!condition || condition.skip) {
            return true;
        }

        switch (descriptor.type) {
            case 'number':
                return this.applyNumberFilter(rawValue, condition.operator, condition.value);
            case 'boolean':
                return this.applyBooleanFilter(rawValue, condition.operator, condition.value);
            case 'date': {
                const cache = descriptor.field ? this._ensureDateColumnCache(descriptor.field) : descriptor.dateCache;
                descriptor.dateCache = cache;
                return this._evaluateDateCondition(cache, condition, rowIndex, rawValue);
            }
            default:
                return this.applyTextFilter(rawValue, condition.operator, condition.value);
        }
    }

    _evaluateDateCondition(cache, condition, rowIndex, rawValue) {
        if (!condition || condition.skip) {
            return true;
        }

        if (condition.invalid || condition.mode === 'invalid') {
            return false;
        }

        if (condition.mode === 'blank') {
            const isBlank = rawValue === null || rawValue === undefined || rawValue === '';
            return condition.operator === 'blank' ? isBlank : !isBlank;
        }

        let epochMs;
        let dayStart;

        if (cache && Number.isInteger(rowIndex) && rowIndex >= 0 && rowIndex < cache.valid.length) {
            if (!cache.valid[rowIndex]) {
                return false;
            }
            epochMs = cache.epochMs[rowIndex];
            dayStart = cache.dayStart[rowIndex];
        } else {
            const date = rawValue instanceof Date ? rawValue : this.parseDate(rawValue);
            if (!date || Number.isNaN(date.getTime())) {
                return false;
            }
            epochMs = date.getTime();
            dayStart = this._getDayStartTimestamp(date);
        }

        switch (condition.mode) {
            case 'preset':
                if (!condition.range) {
                    return true;
                }
                return dayStart >= condition.range.start && dayStart <= condition.range.end;
            case 'compare': {
                const op = condition.operator;
                switch (op) {
                    case 'equals':
                        return dayStart === condition.filterDayStart;
                    case 'notEquals':
                        return dayStart !== condition.filterDayStart;
                    case 'lessThan':
                        return epochMs < condition.filterTime;
                    case 'lessThanOrEqual':
                        return epochMs <= condition.filterDayEnd;
                    case 'greaterThan':
                        return epochMs > condition.filterTime;
                    case 'greaterThanOrEqual':
                        return epochMs >= condition.filterDayStart;
                    default:
                        window.SnapLogger?.warn?.('Unknown date filter operator', op);
                        return true;
                }
            }
            case 'range': {
                const { hasFrom, hasTo, fromDayStart, toDayEnd } = condition;
                if (hasFrom && hasTo) {
                    return dayStart >= fromDayStart && dayStart <= toDayEnd;
                }
                if (hasFrom) {
                    return dayStart >= fromDayStart;
                }
                if (hasTo) {
                    return dayStart <= toDayEnd;
                }
                return true;
            }
            default:
                return true;
        }
    }

    _stringifyFilterValue(value) {
        return String(value ?? '');
    }

    _matchesPreparedFilter(row, descriptor, rowIndex, valueOverride, options = {}) {
        if (!descriptor || !descriptor.isActive) {
            return true;
        }

        const value = valueOverride !== undefined ? valueOverride : (row ? row[descriptor.field] : undefined);
        const ignoreCheckbox = options.ignoreCheckbox === true;
        let passesCheckboxFilter = true;

        if (descriptor.checkedValuesPresent && !ignoreCheckbox) {
            if (descriptor.checkedValuesLength === 0) {
                passesCheckboxFilter = false;
            } else if (descriptor.checkedSet) {
                passesCheckboxFilter = descriptor.checkedSet.has(this._stringifyFilterValue(value));
            }
            if (descriptor.checkboxOnly) {
                return passesCheckboxFilter;
            }
            if (!passesCheckboxFilter) {
                return false;
            }
        } else if (descriptor.checkedValuesPresent && ignoreCheckbox) {
            passesCheckboxFilter = true;
            if (descriptor.checkboxOnly) {
                return true;
            }
        } else if (descriptor.checkboxOnly) {
            return true;
        }

        let result = true;

        if (descriptor.hasFirstCondition) {
            result = this._evaluatePreparedCondition(descriptor, descriptor.firstCondition, value, rowIndex);
        }

        if (descriptor.hasSecondCondition) {
            const secondResult = this._evaluatePreparedCondition(descriptor, descriptor.secondCondition, value, rowIndex);
            result = descriptor.logicOperator === 'OR' ? (result || secondResult) : (result && secondResult);
        }

        return passesCheckboxFilter && result;
    }

    matchesFilter(value, filter, field = null, rowIndex = null, row = null) {
        if (!filter) return true;

        let descriptor = null;

        if (field) {
            const active = this._ensureActiveFilterDescriptors();
            descriptor = active?.get(field) || null;
        }

        if (!descriptor) {
            descriptor = this._prepareFilterDescriptor(field, filter);
            if (!descriptor || !descriptor.isActive) {
                return true;
            }
        }

        const record = row || ((Number.isInteger(rowIndex) && this.data && rowIndex >= 0 && rowIndex < this.data.length) ? this.data[rowIndex] : null);
        return this._matchesPreparedFilter(record, descriptor, rowIndex, value);
    }

    _ensureDateColumnCache(field) {
        if (!field || !this.data || !this.data.length) {
            return null;
        }

        const column = this.getColumn(field);
        if (!column) {
            return null;
        }

        const filterType = this.getFilterType(column);
        if (filterType !== 'date') {
            return null;
        }

        if (!this._dateColumnCache) {
            this._dateColumnCache = new Map();
        }

        let cache = this._dateColumnCache.get(field);
        const length = this.data.length;

        if (!cache || cache.length !== length) {
            cache = this._buildDateColumnCache(field);
            this._dateColumnCache.set(field, cache);
            return cache;
        }

        if (cache.dirtyRows && cache.dirtyRows.size) {
            this._rebuildDirtyDateRows(field, cache);
        }

        return cache;
    }

    _buildDateColumnCache(field) {
        const length = this.data ? this.data.length : 0;
        const epochMs = new Float64Array(length);
        const dayStart = new Float64Array(length);
        const valid = new Uint8Array(length);

        for (let i = 0; i < length; i++) {
            this._populateDateCacheRow(field, i, epochMs, dayStart, valid);
        }

        return {
            epochMs,
            dayStart,
            valid,
            length,
            dirtyRows: new Set()
        };
    }

    _rebuildDirtyDateRows(field, cache) {
        if (!cache || !cache.dirtyRows || cache.dirtyRows.size === 0) {
            return;
        }

        const { epochMs, dayStart, valid, dirtyRows } = cache;
        for (const rowIndex of dirtyRows) {
            if (rowIndex >= 0 && rowIndex < epochMs.length) {
                this._populateDateCacheRow(field, rowIndex, epochMs, dayStart, valid);
            }
        }
        dirtyRows.clear();
    }

    _populateDateCacheRow(field, rowIndex, epochMsArr, dayStartArr, validArr) {
        const row = this.data && rowIndex < this.data.length ? this.data[rowIndex] : null;
        if (!row) {
            epochMsArr[rowIndex] = NaN;
            dayStartArr[rowIndex] = NaN;
            validArr[rowIndex] = 0;
            return;
        }

        const rawValue = row[field];

        if (rawValue === null || rawValue === undefined || rawValue === '') {
            epochMsArr[rowIndex] = NaN;
            dayStartArr[rowIndex] = NaN;
            validArr[rowIndex] = 0;
            return;
        }

        let date = rawValue instanceof Date ? rawValue : this._parseDateUncached(rawValue);
        if (!date || Number.isNaN(date.getTime())) {
            epochMsArr[rowIndex] = NaN;
            dayStartArr[rowIndex] = NaN;
            validArr[rowIndex] = 0;
            return;
        }

        const time = date.getTime();
        epochMsArr[rowIndex] = time;
        dayStartArr[rowIndex] = this._getDayStartTimestamp(date);
        validArr[rowIndex] = 1;
    }

    _markDateIndexDirty(field, rowIndex) {
        if (!this._dateColumnCache || !this._dateColumnCache.size) {
            return;
        }
        if (!field || !Number.isInteger(rowIndex) || rowIndex < 0) {
            return;
        }
        const cache = this._dateColumnCache.get(field);
        if (!cache) {
            return;
        }
        if (!cache.dirtyRows) {
            cache.dirtyRows = new Set();
        }
        cache.dirtyRows.add(rowIndex);
    }

    /**
     * Debounced filter application for performance
     */
    applyFiltersDebounced(immediate = false) {
        // Clear existing timer
        if (this.filterDebounceTimer) {
            clearTimeout(this.filterDebounceTimer);
            this.filterDebounceTimer = null;
        }

        if (immediate) {
            // Apply immediately
            this.processData();
            this.render();
        } else {
            // Apply after delay
            this.filterDebounceTimer = setTimeout(() => {
                this.processData();
                this.render();
                this.filterDebounceTimer = null;
            }, this.filterDebounceDelay);
        }
    }

    /**
     * Debounced filter application for single filter changes (like old grid)
     */
    applyFilterDebounced() {
        // Clear existing timer
        if (this.filterDebounceTimer) {
            clearTimeout(this.filterDebounceTimer);
        }

        // Set new timer
        this.filterDebounceTimer = setTimeout(() => {
            this.processData();
            this.render();
            this.filterDebounceTimer = null;
        }, this.filterDebounceDelay);
    }

    /**
     * Refresh active menu target reference after re-rendering
     * This fixes the issue where filtering causes menu positioning to break
     */
    refreshActiveMenuTarget() {
        if (!this.activeMenu) {
            return; // No active menu to refresh
        }

        // Get the field name from the menu
        const fieldName = this.activeMenu.getAttribute('data-field');
        if (!fieldName) {
            console.warn('⚠️ Active menu has no field name, cannot refresh target');
            return;
        }

        // Find the new header cell for this field after re-rendering
        const newHeaderCell = this.container.querySelector(`.snap-grid-header-cell[data-field="${fieldName}"]`);
        if (newHeaderCell) {
            const previousTarget = this.activeMenuTarget;

            if (previousTarget && previousTarget !== newHeaderCell) {
                previousTarget.classList.remove('menu-active');
            }

            this.activeMenuTarget = newHeaderCell;
            newHeaderCell.classList.add('menu-active');
            console.log('🔄 Refreshed active menu target for field:', fieldName);

            const repositionSuppressed = this.suppressMenuReposition && this._repositionPhase !== 'after-render';

            if (repositionSuppressed) {
                console.log('⏸️ Skipping menu reposition (suppressed), pointer updated');
                return;
            }

            // Reposition the menu with the new target
            try {
                this.positionMenuWithCollisionDetection(this.activeMenu, this.activeMenuTarget);
            } catch (error) {
                console.warn('Error repositioning menu after target refresh:', error);
            }
        } else {
            console.warn('⚠️ Could not find new header cell for field:', fieldName);
            const repositionSuppressed = this.suppressMenuReposition && this._repositionPhase !== 'after-render';
            if (!repositionSuppressed) {
                // If target column is hidden/not found, position menu on the left side
                this.positionMenuOnLeftSide();
            }
        }
    }


    /**
     * Apply single condition to value
     */
    applySingleCondition(value, type, operator, filterValue) {
        if (type === 'date' && operator === 'inRange' && this._debugDateFiltering) {
            this._logDateDebug('applySingleCondition inRange', {
                value,
                operator,
                filterValue
            });
        }

        switch (type) {
            case 'text':
                return this.applyTextFilter(value, operator, filterValue);
            case 'number':
                return this.applyNumberFilter(value, operator, filterValue);
            case 'date':
                return this.applyDateFilter(value, operator, filterValue);
            case 'boolean':
                return this.applyBooleanFilter(value, operator, filterValue);
            default:
                return true;
        }
    }

    /**
     * Apply text filter
     */
    applyTextFilter(value, operator, filterValue) {
        const str = String(value ?? '').toLowerCase();
        const filter = String(filterValue || '').toLowerCase();

        /* debug: applyTextFilter */

        let result;
        switch (operator) {
            case 'contains':
                result = str.includes(filter);
                break;
            case 'notContains':
                result = !str.includes(filter);
                break;
            case 'equals':
                result = str === filter;
                break;
            case 'notEquals':
                result = str !== filter;
                break;
            case 'startsWith':
                result = str.startsWith(filter);
                break;
            case 'endsWith':
                result = str.endsWith(filter);
                break;
            case 'blank':
                result = !str || str.trim() === '';
                break;
            case 'notBlank':
                result = str && str.trim() !== '';
                break;
            default:
                window.SnapLogger?.warn('Unknown text filter operator', operator);
                result = true;
        }

        /* debug: text filter result */
        return result;
    }

    /**
     * Apply number filter
     */
    applyNumberFilter(value, operator, filterValue) {
        // Helper: robust numeric parsing (handles currency strings like "$19.34", commas, etc.)
        const toNum = (v) => {
            if (v === null || v === undefined || v === '') return NaN;
            if (typeof v === 'number') return v;
            if (typeof v === 'string') {
                const cleaned = v.replace(/[^0-9.-]/g, '');
                return cleaned === '' || cleaned === '-' || cleaned === '.' ? NaN : Number(cleaned);
            }
            return Number(v);
        };

        // Handle blank/not blank first
        if (operator === 'blank') {
            return value === null || value === undefined || value === '';
        }
        if (operator === 'notBlank') {
            return value !== null && value !== undefined && value !== '';
        }

        const num = toNum(value);
        if (isNaN(num)) {
            // If cell value cannot be parsed as number, it cannot satisfy numeric filters
            return false;
        }

        // In-range is special: filterValue is an object, so don't coerce it to Number
        if (operator === 'inRange') {
            let from = NaN, to = NaN;
            if (filterValue && typeof filterValue === 'object') {
                from = toNum(filterValue.fromValue);
                to = toNum(filterValue.toValue);
            }
            // Both bounds provided
            if (!isNaN(from) && !isNaN(to)) return num >= from && num <= to;
            // Only lower bound
            if (!isNaN(from)) return num >= from;
            // Only upper bound
            if (!isNaN(to)) return num <= to;
            // No valid bounds -> do not filter out
            return true;
        }

        // For other operators, parse the filterValue as a number
        const filter = toNum(filterValue);
        /* debug: applyNumberFilter */

        if (isNaN(filter)) {
            window.SnapLogger?.warn('Invalid numeric filter value', filterValue);
            return true; // Don't filter out if filter value is invalid
        }

        switch (operator) {
            case 'equals':
                return num === filter;
            case 'notEquals':
                return num !== filter;
            case 'lessThan':
                return num < filter;
            case 'lessThanOrEqual':
                return num <= filter;
            case 'greaterThan':
                return num > filter;
            case 'greaterThanOrEqual':
                return num >= filter;
            default:
                window.SnapLogger?.warn('Unknown number filter operator', operator);
                return true;
        }
    }

    _resetDateCaches() {
        if (this._dateParseCache) {
            this._dateParseCache.clear();
        } else {
            this._dateParseCache = new Map();
        }
        this._presetDateRangeCache = Object.create(null);
        this._todayContextCache = null;
        this._invalidateAllDateColumns();
    }

    _invalidateAllDateColumns() {
        if (this._dateColumnCache) {
            this._dateColumnCache.clear();
        } else {
            this._dateColumnCache = new Map();
        }
        this._activeFilterDescriptors = new Map();
        this._dateCacheRevision = (this._dateCacheRevision + 1) >>> 0;
    }

    /**
     * Parse date from various formats with caching to avoid repeated work
     */
    parseDate(value) {
        if (!value && value !== 0) return null;

        if (value instanceof Date) {
            return value;
        }

        const cacheKey = this._buildDateCacheKey(value);
        if (cacheKey && this._dateParseCache.has(cacheKey)) {
            return this._dateParseCache.get(cacheKey);
        }

        const parsedDate = this._parseDateUncached(value);

        if (cacheKey) {
            this._dateParseCache.set(cacheKey, parsedDate);
        }

        return parsedDate;
    }

    _buildDateCacheKey(value) {
        if (value instanceof Date) return null;
        if (typeof value === 'number') {
            return `n:${value}`;
        }
        if (typeof value === 'string') {
            return `s:${value.trim()}`;
        }
        if (value && typeof value.valueOf === 'function') {
            const primitive = value.valueOf();
            if (typeof primitive === 'number') {
                return `n:${primitive}`;
            }
            if (typeof primitive === 'string') {
                return `s:${primitive.trim()}`;
            }
        }
        return null;
    }

    _parseDateUncached(value) {
        if (typeof value === 'number') {
            const fromMillis = new Date(value);
            return isNaN(fromMillis.getTime()) ? null : fromMillis;
        }

        const str = String(value).trim();
        if (!str) return null;

        // Try standard Date constructor first
        let date = new Date(str);
        if (!isNaN(date.getTime())) {
            return date;
        }

        // Try parsing common formats manually
        // Format: M/D/YYYY or MM/DD/YYYY
        const slashMatch = str.match(/^(\d{1,2})\/(\d{1,2})\/(\d{4})$/);
        if (slashMatch) {
            const [, month, day, year] = slashMatch;
            date = new Date(parseInt(year, 10), parseInt(month, 10) - 1, parseInt(day, 10));
            if (!isNaN(date.getTime())) {
                return date;
            }
        }

        // Format: YYYY-MM-DD
        const dashMatch = str.match(/^(\d{4})-(\d{1,2})-(\d{1,2})$/);
        if (dashMatch) {
            const [, year, month, day] = dashMatch;
            date = new Date(parseInt(year, 10), parseInt(month, 10) - 1, parseInt(day, 10));
            if (!isNaN(date.getTime())) {
                return date;
            }
        }

        // Format: MMM D, YYYY or MMMM D, YYYY (e.g., "Dec 21, 2024" or "December 21, 2024")
        const monthMap = {
            jan: 0, january: 0,
            feb: 1, february: 1,
            mar: 2, march: 2,
            apr: 3, april: 3,
            may: 4,
            jun: 5, june: 5,
            jul: 6, july: 6,
            aug: 7, august: 7,
            sep: 8, sept: 8, september: 8,
            oct: 9, october: 9,
            nov: 10, november: 10,
            dec: 11, december: 11
        };
        const monthNameMatch = str.match(/^([A-Za-z]{3,9})\s+(\d{1,2}),\s*(\d{4})$/);
        if (monthNameMatch) {
            const [, monStr, dayStr, yearStr] = monthNameMatch;
            const monKey = monStr.toLowerCase();
            if (Object.prototype.hasOwnProperty.call(monthMap, monKey)) {
                date = new Date(parseInt(yearStr, 10), monthMap[monKey], parseInt(dayStr, 10));
                if (!isNaN(date.getTime())) {
                    return date;
                }
            }
        }

        // Format: D MMM YYYY or DD MMM YYYY (e.g., "21 Dec 2024")
        const dayMonthNameMatch = str.match(/^(\d{1,2})\s+([A-Za-z]{3,9})\s+(\d{4})$/);
        if (dayMonthNameMatch) {
            const [, dayStr, monStr, yearStr] = dayMonthNameMatch;
            const monKey = monStr.toLowerCase();
            if (Object.prototype.hasOwnProperty.call(monthMap, monKey)) {
                date = new Date(parseInt(yearStr, 10), monthMap[monKey], parseInt(dayStr, 10));
                if (!isNaN(date.getTime())) {
                    return date;
                }
            }
        }

        // Handle timestamps (numbers stored as strings)
        const num = Number(str);
        if (!isNaN(num)) {
            date = new Date(num);
            if (!isNaN(date.getTime())) {
                return date;
            }
        }

        return null;
    }

    _getDayStartTimestamp(date) {
        return Date.UTC(date.getFullYear(), date.getMonth(), date.getDate());
    }

    _getDayEndTimestamp(date) {
        return this._getDayStartTimestamp(date) + MS_PER_DAY - 1;
    }

    _datesAreSameDay(a, b) {
        return a.getFullYear() === b.getFullYear() &&
            a.getMonth() === b.getMonth() &&
            a.getDate() === b.getDate();
    }

    _getTodayContext() {
        const now = this.getPacificDate();
        const key = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}`;
        if (this._todayContextCache && this._todayContextCache.key === key) {
            return this._todayContextCache;
        }

        const start = this._getDayStartTimestamp(now);
        this._todayContextCache = {
            key,
            start,
            end: start + MS_PER_DAY - 1,
            year: now.getFullYear(),
            month: now.getMonth(),
            date: now.getDate(),
            dayOfWeek: now.getDay()
        };

        return this._todayContextCache;
    }

    _getPresetDateRange(operator) {
        const todayContext = this._getTodayContext();
        const cacheKey = `${operator}|${todayContext.key}`;
        if (this._presetDateRangeCache[cacheKey]) {
            return this._presetDateRangeCache[cacheKey];
        }

        const { start: todayStart, end: todayEnd, year, month, date, dayOfWeek } = todayContext;
        let range = null;

        switch (operator) {
            case 'today':
                range = { start: todayStart, end: todayEnd };
                break;
            case 'yesterday': {
                const start = todayStart - MS_PER_DAY;
                range = { start, end: start + MS_PER_DAY - 1 };
                break;
            }
            case 'last7Days':
                range = { start: todayStart - (6 * MS_PER_DAY), end: todayEnd };
                break;
            case 'last30Days':
                range = { start: todayStart - (29 * MS_PER_DAY), end: todayEnd };
                break;
            case 'last90Days':
                range = { start: todayStart - (89 * MS_PER_DAY), end: todayEnd };
                break;
            case 'last6Months': {
                const marker = new Date(year, month, date);
                marker.setMonth(marker.getMonth() - 6);
                const start = this._getDayStartTimestamp(marker);
                range = { start, end: todayEnd };
                break;
            }
            case 'thisWeek': {
                const start = todayStart - (dayOfWeek * MS_PER_DAY);
                range = { start, end: todayEnd };
                break;
            }
            case 'lastWeek': {
                const thisWeekStart = todayStart - (dayOfWeek * MS_PER_DAY);
                const start = thisWeekStart - (7 * MS_PER_DAY);
                range = { start, end: start + (7 * MS_PER_DAY) - 1 };
                break;
            }
            case 'thisMonth':
            case 'currentMonth': {
                const monthStart = new Date(year, month, 1);
                const nextMonthStart = new Date(year, month + 1, 1);
                range = {
                    start: this._getDayStartTimestamp(monthStart),
                    end: this._getDayStartTimestamp(nextMonthStart) - 1
                };
                break;
            }
            case 'lastMonth': {
                const startDate = new Date(year, month - 1, 1);
                const nextStart = new Date(year, month, 1);
                range = {
                    start: this._getDayStartTimestamp(startDate),
                    end: this._getDayStartTimestamp(nextStart) - 1
                };
                break;
            }
            case 'thisYear':
            case 'currentYear': {
                const startDate = new Date(year, 0, 1);
                const nextStart = new Date(year + 1, 0, 1);
                range = {
                    start: this._getDayStartTimestamp(startDate),
                    end: this._getDayStartTimestamp(nextStart) - 1
                };
                break;
            }
            case 'lastYear': {
                const startDate = new Date(year - 1, 0, 1);
                const nextStart = new Date(year, 0, 1);
                range = {
                    start: this._getDayStartTimestamp(startDate),
                    end: this._getDayStartTimestamp(nextStart) - 1
                };
                break;
            }
            default:
                range = null;
        }

        if (range) {
            this._presetDateRangeCache[cacheKey] = range;
        }

        return range;
    }

    _logDateDebug(message, payload) {
        if (!this._debugDateFiltering) return;
        if (window.SnapLogger?.debug) {
            window.SnapLogger.debug(message, payload);
        } else {
            console.debug(message, payload);
        }
    }

    /**
     * Apply date filter
     */
    applyDateFilter(value, operator, filterValue, field = null, rowIndex = null) {
        if (!operator) {
            return true;
        }

        const condition = this._normalizeDateCondition(operator, filterValue);
        const cache = field ? this._ensureDateColumnCache(field) : null;
        return this._evaluateDateCondition(cache, condition, rowIndex, value);
    }

    /**
     * Apply predefined date filter (today, yesterday, etc.)
     */
    applyPredefinedDateFilter(value, operator) {
        const cellDate = this.parseDate(value);
        if (!cellDate || isNaN(cellDate.getTime())) return false;

        const range = this._getPresetDateRange(operator);
        if (!range) return true;

        const cellDayStart = this._getDayStartTimestamp(cellDate);
        return cellDayStart >= range.start && cellDayStart <= range.end;
    }

    /**
     * Apply boolean filter
     */
    applyBooleanFilter(value, operator, filterValue) {
        const boolValue = Boolean(value);
        const filterBool = Boolean(filterValue);

        switch (operator) {
            case 'equals':
                return boolValue === filterBool;
            case 'notEquals':
                return boolValue !== filterBool;
            default:
                return true;
        }
    }

    /**
     * Apply current sorting to filtered data
     */
    applySorting() {
        // Start from filtered index array
        const sortEntries = Object.entries(this.sortState);

        // Capture previous mapping to preserve selection across sort
        const prevSortedIdx = this.sortedIdx.slice();
        const prevSelectedIdx = new Set(this.selectedRows);
        const prevSelectedDataIdx = Array.from(prevSelectedIdx).map(i => prevSortedIdx[i]).filter(i => i != null);
        const prevAnchorDataIdx = (this.lastSelectedRowIndex != null) ? prevSortedIdx[this.lastSelectedRowIndex] : null;

        if (!sortEntries.length) {
            this.sortedIdx = this.filteredIdx.slice();
            // Remap selection to new indices (filtered order)
            if (prevSelectedDataIdx.length) {
                const pos = new Map(this.sortedIdx.map((di, i) => [di, i]));
                this.selectedRows = new Set(prevSelectedDataIdx.map(di => pos.get(di)).filter(i => i != null));
                // Remap anchor
                this.lastSelectedRowIndex = (prevAnchorDataIdx != null && pos.has(prevAnchorDataIdx)) ? pos.get(prevAnchorDataIdx) : null;
            }
            return;
        }
        const data = this.data;
        const idx = this.filteredIdx.slice();
        const comparator = (ia, ib) => {
            const a = data[ia];
            const b = data[ib];
            for (let k = 0; k < sortEntries.length; k++) {
                const [field, direction] = sortEntries[k];
                const aVal = a[field];
                const bVal = b[field];
                let cmp = 0;
                if (aVal === bVal) cmp = 0;
                else if (aVal == null) cmp = 1; // nulls last
                else if (bVal == null) cmp = -1;
                else if (typeof aVal === 'number' && typeof bVal === 'number') cmp = aVal - bVal;
                else {
                    // Check if this is a date column and handle date sorting properly
                    const columnType = this.getColumnType(field);
                    if (columnType === 'date') {
                        const dateA = this.parseDate(aVal);
                        const dateB = this.parseDate(bVal);
                        if (dateA && dateB) {
                            cmp = dateA.getTime() - dateB.getTime();
                        } else if (dateA && !dateB) {
                            cmp = -1; // valid date comes before invalid
                        } else if (!dateA && dateB) {
                            cmp = 1; // invalid date comes after valid
                        } else {
                            cmp = String(aVal).localeCompare(String(bVal)); // both invalid, fallback to string
                        }
                    } else {
                        cmp = String(aVal).localeCompare(String(bVal));
                    }
                }
                if (cmp !== 0) {
                    return direction === 'desc' ? -cmp : cmp;
                }
            }
            return 0;
        };
        idx.sort(comparator);
        this.sortedIdx = idx;

        // Remap selection and anchor to new indices based on underlying data indices
        if (prevSelectedDataIdx.length) {
            const pos = new Map(this.sortedIdx.map((di, i) => [di, i]));
            this.selectedRows = new Set(prevSelectedDataIdx.map(di => pos.get(di)).filter(i => i != null));
            this.lastSelectedRowIndex = (prevAnchorDataIdx != null && pos.has(prevAnchorDataIdx)) ? pos.get(prevAnchorDataIdx) : null;
            this.updateSelectionState();
        }
    }

    /**
     * Main render method
     */
    render() {
        this.renderStartTime = performance.now();

        // Keep Actions column always pinned to right edge
        // Never auto-fit columns - preserve original widths
        // This ensures Actions stays at right: 0 regardless of column count

        if (this.options.showHeader) {
            this.renderHeader();
        }

        this.renderRows();

        // Update header/footer stats if present
        this.updateHeaderFooterStats();

        // Keep header horizontally aligned with body after any re-render (e.g., sort/filter)
        this.syncHorizontalScroll();

        // Refresh active menu target after header alignment so menu positioning uses
        // the correct horizontal offsets, especially after filtering with scrolled views
        this.refreshActiveMenuTarget();

        this.lastRenderDuration = performance.now() - this.renderStartTime;
        if (this.lastRenderDuration > 16) { // More than one frame
            console.warn(`⚠️ SnapGrid render took ${this.lastRenderDuration.toFixed(2)}ms`);
        }
    }

    /**
     * If center columns are narrower than the space between pinned columns,
     * expand them proportionally to fill the gap. Returns true if a re-render
     * was triggered by sizing.
     */
    autoFitColumnsIfNeeded() {
        if (!this.bodyElement || !Array.isArray(this.processedColumns)) return false;

        const viewportW = this.bodyElement.clientWidth || 0;
        if (viewportW <= 0) return false;

        let leftPinned = 0, rightPinned = 0, centerWidth = 0, centerCount = 0;
        this.processedColumns.forEach(c => {
            const w = c.hide ? 0 : (c.width || 0);
            if (c.pinned === 'left') leftPinned += w;
            else if (c.pinned === 'right') rightPinned += w;
            else { centerWidth += w; if (!c.hide) centerCount++; }
        });

        // Do not auto-fit when 0 or 1 center column is visible.
        // With a single center column, preserve its natural/original width
        // and leave remaining space empty between left pinned and right pinned.
        if (centerCount <= 1) return false;

        const available = Math.max(0, viewportW - leftPinned - rightPinned - this.getScrollbarWidth());
        if (available > centerWidth + 1) {
            this._autoFittingColumns = true;
            const fitted = this.sizeColumnsToFit();
            this._autoFittingColumns = false;
            return !!fitted; // sizeColumnsToFit triggers render
        }
        return false;
    }

    /**
     * Render the header row
     */
    renderHeader() {
        if (!this.headerElement) return;

        this.headerElement.innerHTML = '';
        const scroller = document.createElement('div');
        scroller.className = 'snap-grid-header-scroller';
        const headerRow = document.createElement('div');
        headerRow.className = 'snap-grid-header-row';
        headerRow.setAttribute('role', 'row');

        // Compute pinned offsets (left/right) for header rendering
        this.computePinnedOffsets();

        this.processedColumns.forEach((column, index) => {
            const headerCell = document.createElement('div');
            headerCell.className = 'snap-grid-header-cell';
            headerCell.dataset.field = column.field;
            headerCell.dataset.index = index;

            // Add pinned class
            if (column.pinned) {
                headerCell.classList.add(`pinned-${column.pinned}`);
                // Apply offset so pinned columns don't overlap
                if (column.pinned === 'left') {
                    // Guard against undefined maps during early render
                    const off = (this.pinnedLeftOffsets && this.pinnedLeftOffsets[index]) || 0;
                    headerCell.style.left = off + 'px';
                } else if (column.pinned === 'right') {
                    const off = (this.pinnedRightOffsets && this.pinnedRightOffsets[index]) || 0;
                    // For Actions column, always position flush against right edge
                    if (column.field === 'actions') {
                        headerCell.style.right = '0px';
                    } else {
                        // For other pinned-right columns, apply exact offset without extra gap
                        headerCell.style.right = `${off}px`;
                    }
                }
            }

            // Add draggable attributes for non-pinned columns
            if (this.options.columnDragging && !column.pinned) {
                headerCell.classList.add('draggable');
            }

            // Add accessibility attributes
            this.addHeaderAccessibility(headerCell, column, index);

            // Set width
            if (column.width) {
                const w = (column.hide ? 0 : column.width);
                headerCell.style.width = `${w}px`;
                headerCell.style.minWidth = `${w}px`;
            }

            // Hidden columns: keep DOM for indexing/drag, but not visible
            if (column.hide) {
                headerCell.style.display = 'none';
            }

            // Header content
            const headerContent = document.createElement('div');
            headerContent.className = 'snap-grid-header-content';

            // Use custom header renderer if available
            if (column.headerRenderer && typeof column.headerRenderer === 'function') {
                const customHeader = column.headerRenderer();
                if (typeof customHeader === 'string') {
                    headerContent.innerHTML = customHeader;
                } else {
                    headerContent.appendChild(customHeader);
                }
            } else {
                const headerText = document.createElement('span');
                headerText.className = 'snap-grid-header-text';
                headerText.textContent = column.headerName || column.field;

                // Add draggable attribute to header text for non-pinned columns
                if (this.options.columnDragging && !column.pinned) {
                    headerText.draggable = true;
                    headerText.classList.add('draggable');
                }

                headerContent.appendChild(headerText);
            }

            // Sort indicator (skip for checkbox/preview/actions)
            if (this.options.sortable && column.sortable !== false && !['checkbox','preview','actions'].includes(column.field)) {
                const sortIcon = document.createElement('span');
                sortIcon.className = 'snap-grid-sort-icon';

                const sortDirection = this.sortState[column.field];
                if (sortDirection) {
                    sortIcon.classList.add(`sort-${sortDirection}`);
                    const iconName = sortDirection === 'asc' ? 'ascending-ic.svg' : 'descending-ic.svg';
                    sortIcon.innerHTML = `<img src="assets/${iconName}" alt="${sortDirection === 'asc' ? 'Ascending' : 'Descending'}" class="sort-icon-img">`;
                }

                headerContent.appendChild(sortIcon);
            }

            // Column menu button (only if column allows filtering/sorting and not special fields)
            if (((this.options.filterable && column.filterable !== false) || (this.options.sortable && column.sortable !== false))
                && !['checkbox','preview','actions'].includes(column.field)) {
                const menuContainer = document.createElement('div');
                menuContainer.className = 'snap-grid-column-menu-container';

                const menuButton = document.createElement('button');
                menuButton.className = 'snap-grid-column-menu-btn';
                menuButton.innerHTML = '<img src="assets/menu-dots-ic.svg" alt="Menu" width="10" height="10">';
                menuButton.setAttribute('aria-label', `Column menu for ${column.headerName || column.field}`);
                menuButton.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.showColumnMenu(headerCell, column);
                });

                menuContainer.appendChild(menuButton);

                if (this.isFilterActive(column.field)) {
                    menuContainer.appendChild(this.createFilterIndicator());
                }

                headerContent.appendChild(menuContainer);
            }

            headerCell.appendChild(headerContent);

            // Resize handle
            if (this.options.resizable && column.resizable !== false) {
                const resizeHandle = document.createElement('div');
                resizeHandle.className = 'snap-grid-resize-handle';
                headerCell.appendChild(resizeHandle);
            }

            headerRow.appendChild(headerCell);
        });

        // Always use viewport width to keep Actions pinned to right edge
        // Columns use their original widths, empty space fills the gap
        const viewportWidth = (this.bodyElement?.clientWidth || this.container?.clientWidth || 1200);
        const totalColumnsWidth = this.getTotalColumnsWidth();
        headerRow.style.width = `${Math.max(totalColumnsWidth, viewportWidth)}px`;
        // Ensure the header scroller itself stretches to container width so pinned-right remains at edge
        scroller.style.minWidth = `${viewportWidth}px`;
        scroller.appendChild(headerRow);
        this.headerElement.appendChild(scroller);
    }

    /**
     * Render visible rows
     */
    renderRows() {
        if (!this.viewportElement) return;
        // Compute pinned and visible column ranges once per render
        this.computePinnedOffsets();
        this.computeVisibleColumns();

        this.viewportElement.innerHTML = '';

        const fragment = document.createDocumentFragment();

        for (let i = this.visibleStartIndex; i < this.visibleEndIndex; i++) {
            const dataIndex = this.sortedIdx[i];
            const rowData = this.data[dataIndex];
            if (!rowData) continue;

            const row = this.createRow(rowData, i);
            fragment.appendChild(row);
        }

        this.viewportElement.appendChild(fragment);

        // Update viewport position for virtual scrolling without transforms
        if (this.options.virtualScrolling) {
            const scale = this._scrollScale || 1;
            const offsetVirtual = this.visibleStartIndex * this.options.rowHeight;
            const offsetY = Math.floor(offsetVirtual / scale);
            this.viewportElement.style.transform = '';
            this.viewportElement.style.paddingTop = `${offsetY}px`;
        } else {
            this.viewportElement.style.paddingTop = '';
        }

        // Ensure viewport content width and scrollbar widths are accurate
        this.updateHorizontalMetrics();
    }

    /**
     * Update counts and info pills in header/footer
     */
    updateHeaderFooterStats() {
        // Header loaded info
        if (this.controls?.loadedInfo) {
            const shown = this.sortedIdx.length;
            // Use expected total during chunked loading, otherwise use current data length
            const total = this.expectedTotalRecords !== null ? this.expectedTotalRecords : this.data.length;

            // Clear existing content and create new structure with text and icon
            this.controls.loadedInfo.innerHTML = '';

            // Create text span
            const textSpan = document.createElement('span');
            textSpan.textContent = `${shown.toLocaleString()} / ${total.toLocaleString()}`;

            // Create icon
            const icon = document.createElement('img');
            icon.src = 'assets/data-cell-ic.svg';
            icon.className = 'loaded-info-icon';
            icon.alt = 'Data cell icon';

            // Append both elements (icon first for left side positioning)
            this.controls.loadedInfo.appendChild(icon);
            this.controls.loadedInfo.appendChild(textSpan);
        }

        // Footer stats
        if (this.footer) {
            const total = this.data.length;
            const filtered = this.filteredIdx.length;
            const selected = this.selectedRows.size;
            this.footer.rows.textContent = total.toLocaleString();
            this.footer.filtered.textContent = filtered.toLocaleString();
            this.footer.selected.textContent = selected.toLocaleString();
        }
    }

    /**
     * Create a single row element
     */
    createRow(rowData, rowIndex) {

        const row = document.createElement('div');
        row.className = 'snap-grid-row';
        row.dataset.index = rowIndex;
        row.setAttribute('role', 'row');
        row.setAttribute('aria-rowindex', rowIndex + 2); // +2 because header is row 1

        // Row selection state
        if (this.selectedRows.has(rowIndex)) {
            row.classList.add('selected');
            row.setAttribute('aria-selected', 'true');
        } else {
            row.setAttribute('aria-selected', 'false');
        }

        // Row height and width - match header width calculation
        row.style.height = `${this.options.rowHeight}px`;
        const viewportWidth = (this.bodyElement?.clientWidth || this.container?.clientWidth || 1200);
        const totalRowWidth = this.getTotalColumnsWidth();
        row.style.width = `${Math.max(totalRowWidth, viewportWidth)}px`;

        this.processedColumns.forEach((column, columnIndex) => {
            // Skip non-pinned columns that are outside the horizontal viewport
            if (!column.pinned && this._colVisibleFlags && this._colVisibleFlags[columnIndex] === false) {
                return; // skip creating this cell
            }

            const cell = document.createElement('div');
            cell.className = 'snap-grid-cell';
            cell.dataset.field = column.field;
            cell.dataset.row = rowIndex;
            cell.dataset.column = columnIndex;

            // Add pinned class
            // Hard-enforce Actions as pinned-right to avoid any drift
            if (column.field === 'actions') {
                cell.classList.add('pinned-right');
                cell.style.right = '0px';
            } else if (column.pinned) {
                cell.classList.add(`pinned-${column.pinned}`);
                if (column.pinned === 'left') {
                    const off = (this.pinnedLeftOffsets && this.pinnedLeftOffsets[columnIndex]) || 0;
                    cell.style.left = off + 'px';
                } else if (column.pinned === 'right') {
                    const off = (this.pinnedRightOffsets && this.pinnedRightOffsets[columnIndex]) || 0;
                    // For other pinned-right columns, apply exact offset without extra gap
                    cell.style.right = `${off}px`;
                }
            }

            // Add accessibility attributes
            this.addCellAccessibility(cell, column, rowData, rowIndex, columnIndex);

            // Set width to match header
            const w = (column.hide ? 0 : column.width);
            cell.style.width = `${w}px`;
            cell.style.minWidth = `${w}px`;

            // Hidden columns: keep DOM for indexing, but not visible
            if (column.hide) {
                cell.style.display = 'none';
            }

            // Cell content
            const cellValue = rowData[column.field];
            const cellContent = this.renderCellContent(cellValue, column, rowData, rowIndex);

            // Create cell content wrapper for proper ellipsis handling
            const cellContentWrapper = document.createElement('div');
            cellContentWrapper.className = 'snap-grid-cell-content';

            if (typeof cellContent === 'string') {
                cellContentWrapper.innerHTML = cellContent;
            } else {
                cellContentWrapper.appendChild(cellContent);
            }

            cell.appendChild(cellContentWrapper);

            // Cell editing
            if (this.options.editable && column.editable !== false) {
                cell.classList.add('editable');
            }

            row.appendChild(cell);
        });

        return row;
    }

    /**
     * Sum of column widths for horizontal overflow
     */
    getTotalColumnsWidth() {
        return this.processedColumns.reduce((sum, col) => sum + (col.hide ? 0 : (col.width || 0)), 0);
    }

    /**
     * Keep header horizontally synced with body scroll
     */
    syncHorizontalScroll() {
        if (!this.headerElement || !this.bodyElement) return;
        const scroller = this.headerElement.querySelector('.snap-grid-header-scroller');
        if (scroller) {
            const x = this.bodyElement?.scrollLeft || 0;

            // DON'T apply transform to the entire scroller - this moves pinned columns too
            // Instead, apply transform only to non-pinned header cells
            const allHeaderCells = this.headerElement.querySelectorAll('.snap-grid-header-cell');
            allHeaderCells.forEach(el => {
                if (el.classList.contains('pinned-left') || el.classList.contains('pinned-right')) {
                    // Pinned columns should stay fixed - no transform
                    el.style.transform = '';
                } else {
                    // Non-pinned columns should scroll with content
                    el.style.transform = `translateX(${-x}px)`;
                }
            });
        }
    }
    /**
     * Compute which columns are visible in the current horizontal viewport
     * Always include pinned columns. Add small buffer columns around edges.
     */
    computeVisibleColumns() {
        if (!this.bodyElement || !this.processedColumns) return;
        // Simplify for correctness: render all non-hidden columns; pinned columns remain sticky via CSS
        // This avoids any header/body desync while we stabilize column virtualization later.
        this._colVisibleFlags = this.processedColumns.map(col => !col.hide);
    }


    /**
     * Reconcile horizontal sizes: compute total width, compare to measured header width,
     * and apply the larger value to viewport and rows to guarantee full scroll range.
     */
    updateHorizontalMetrics() {
        if (!this.viewportElement) return;
        const rawTotal = this.getTotalColumnsWidth();
        const minViewportWidth = (this.bodyElement?.clientWidth || this.container?.clientWidth || rawTotal);
        const total = Math.max(rawTotal, minViewportWidth);
        // Also enforce min width on header scroller for consistency
        const scroller = this.headerElement?.querySelector?.('.snap-grid-header-scroller');
        if (scroller) scroller.style.minWidth = `${minViewportWidth}px`;

        // Apply to viewport
        this.viewportElement.style.width = `${total}px`;

        // No dedicated horizontal scrollbar to update

        // Apply to existing rows
        const rows = this.viewportElement.querySelectorAll('.snap-grid-row');
        rows.forEach(r => { r.style.width = `${total}px`; });

        this.totalColumnsWidth = total;

        // Update vertical scrollbar gutter offset (so pinned-right is never overlapped)
        const sbw = this.getScrollbarWidth();
        if (this.container) {
            this.container.style.setProperty('--vscroll-offset', `${sbw}px`);
        }
    }

    /**
     * Compute sticky offsets for pinned columns
     */
    computePinnedOffsets() {
        this.pinnedLeftOffsets = {};
        this.pinnedRightOffsets = {};

        let left = 0;
        for (let i = 0; i < this.processedColumns.length; i++) {
            const col = this.processedColumns[i];
            if (col.pinned === 'left') {
                this.pinnedLeftOffsets[i] = left;
                if (!col.hide) left += (col.width || 0);
            }
        }

        let right = 0;
        for (let i = this.processedColumns.length - 1; i >= 0; i--) {
            const col = this.processedColumns[i];
            if (col.pinned === 'right') {
                this.pinnedRightOffsets[i] = right;
                if (!col.hide) right += (col.width || 0);
            }
        }
    }

    /**
     * Column helpers and API methods
     */
    getColumn(field) {
        return this.processedColumns.find(c => c.field === field) || null;
    }

    /**
     * Calculate minimum width needed for header text
     */
    calculateMinHeaderWidth(column) {
        // Special handling for checkbox column - just enough for checkbox + padding
        if (column.field === 'checkbox') {
            return 46; // 16px checkbox + 15px padding each side
        }

        // Special handling for preview column - fixed square size
        if (column.field === 'preview') {
            return 60; // 32px square + 14px padding each side
        }

        // Special handling for actions column - fixed width for action buttons
        if (column.field === 'actions') {
            return 96; // Fixed width for action buttons
        }

        // Create a temporary element to measure text width
        const tempElement = document.createElement('div');
        tempElement.style.position = 'absolute';
        tempElement.style.visibility = 'hidden';
        tempElement.style.whiteSpace = 'nowrap';
        tempElement.style.fontFamily = 'Amazon Ember, Arial, sans-serif';
        tempElement.style.fontSize = '14px';
        tempElement.style.fontWeight = '500'; // Medium weight for headers
        tempElement.style.padding = '0 12px'; // Account for cell padding

        // Set the header text
        const headerText = column.headerName || column.field || '';
        tempElement.textContent = headerText;

        // Add to DOM to measure
        document.body.appendChild(tempElement);
        const textWidth = tempElement.offsetWidth;
        document.body.removeChild(tempElement);

        // Add extra space for sort indicators, resize handles, gap, etc.
        const extraSpace = 48; // Space for sort icon + resize handle + 8px gap + margins
        const minWidth = textWidth + extraSpace;

        // Set reasonable bounds
        const absoluteMin = 80; // Never go below this
        const absoluteMax = 300; // Don't auto-expand beyond this

        return Math.max(absoluteMin, Math.min(minWidth, absoluteMax));
    }

    getColumnIndex(field) {
        return this.processedColumns.findIndex(c => c.field === field);
    }

    setColumnPinned(field, pinned) {
        const col = this.getColumn(field);
        if (!col) return false;
        // Actions column must remain pinned-right per UX spec
        if (field === 'actions') {
            col.pinned = 'right';
            this.sortColumnsByPinned();
            this.render();
            return true;
        }
        // Checkbox and Preview must always remain pinned-left and cannot be unpinned or pinned-right
        if (field === 'checkbox' || field === 'preview') {
            col.pinned = 'left';
            this.sortColumnsByPinned();
            this.render();
            return true;
        }
        col.pinned = this.sanitizePinned(pinned);
        this.sortColumnsByPinned();
        this.render();
        // Persist as Custom when pinning changes
        this.onUserCustomized && this.onUserCustomized('pin');
        return true;
    }

    setColumnWidth(field, width) {
        const col = this.getColumn(field);
        if (!col) return false;

        // Calculate minimum width based on header text
        const minWidth = this.calculateMinHeaderWidth(col);
        const w = Math.max(minWidth, Math.floor(Number(width) || 0));
        col.width = w;
        this.render();
        // Persist as Custom when width changes
        this.onUserCustomized && this.onUserCustomized('resize');
        return true;
    }

    moveColumnByField(field, toIndex) {
        const fromIndex = this.getColumnIndex(field);
        if (fromIndex < 0) return false;
        // Prevent moving into pinned area implicitly; keep within same pinned group
        const source = this.processedColumns[fromIndex];
        // Build list of indices for same pinned group
        const group = this.processedColumns
            .map((c, i) => ({ c, i }))
            .filter(x => (x.c.pinned || null) === (source.pinned || null))
            .map(x => x.i);
        const min = Math.min(...group);
        const max = Math.max(...group);
        const clamped = Math.max(min, Math.min(max, toIndex));
        this.moveColumn(fromIndex, clamped);
        return true;
    }

    setColumnVisible(field, visible) {
        const col = this.getColumn(field);
        if (!col) return false;
        const prevHidden = !!col.hide;
        col.hide = visible === false;
        this.render();
        // If visibility changed and we are on Default, switch to Custom
        if (prevHidden !== col.hide && this.currentLayoutType === 'default') {
            this.switchToCustomLayout();
        }
        // Persist as Custom when visibility changes
        this.onUserCustomized && this.onUserCustomized('visibility');
        return true;
    }

    applyColumnState(params = {}) {
        const { state = [], defaultState = {} } = params;
        const byId = new Map(this.processedColumns.map(c => [c.field, c]));
        state.forEach(s => {
            const col = byId.get(s.colId || s.field || s.colKey);
            if (!col) return;
            if (s.pinned !== undefined) col.pinned = this.sanitizePinned(s.pinned);
            if (s.width != null) col.width = Math.max(20, Number(s.width) || col.width || 100);
            if (s.hide !== undefined) col.hide = !!s.hide;
        });
        // Apply defaults for any missing
        this.processedColumns.forEach(col => {
            if (!(state || []).some(s => (s.colId || s.field || s.colKey) === col.field)) {
                if (defaultState.pinned !== undefined) col.pinned = this.sanitizePinned(defaultState.pinned);
                if (defaultState.width != null) col.width = Math.max(20, Number(defaultState.width) || col.width || 100);
                if (defaultState.hide !== undefined) col.hide = !!defaultState.hide;
            }
        });
        // Enforce Actions pinned-right regardless of incoming state
        const actionsCol = byId.get('actions') || this.getColumn('actions');
        if (actionsCol) actionsCol.pinned = 'right';
        // Enforce non-unpinnable left pins
        const checkboxCol = byId.get('checkbox') || this.getColumn('checkbox');
        if (checkboxCol) checkboxCol.pinned = 'left';
        const previewCol = byId.get('preview') || this.getColumn('preview');
        if (previewCol) previewCol.pinned = 'left';
        this.sortColumnsByPinned();
        this.render();
        return true;
    }

    getColumnState() {
        return this.processedColumns.map(c => ({
            colId: c.field,
            width: c.width,
            pinned: c.pinned || null,
            hide: !!c.hide
        }));
    }

    /**
     * Grid API methods
     */
    setRowData(data) {
        this.data = Array.isArray(data) ? [...data] : [];
        this._resetDateCaches();
        this.processData();
        this.render();
    }

    /**
     * Load data in chunks for large datasets
     * @param {number} totalRecords - Total number of records to load
     * @param {Function} dataGenerator - Function to generate data chunks
     * @param {Object} options - Loading options
     * @returns {Promise} Loading promise
     */
    async loadDataInChunks(totalRecords, dataGenerator, options = {}) {
        // Import ChunkedDataLoader if not already available
        if (typeof ChunkedDataLoader === 'undefined') {
            throw new Error('ChunkedDataLoader not available. Please include chunked-data-loader.js');
        }

        const loader = new ChunkedDataLoader({
            chunkSize: options.chunkSize || 10000,
            delayBetweenChunks: options.delayBetweenChunks || 16,
            maxMemoryChunks: options.maxMemoryChunks || 5,
            enableMemoryManagement: options.enableMemoryManagement || false // Disable by default for large datasets
        });

        // Set expected total for display
        this.expectedTotalRecords = totalRecords;

        // Show loading state
        this.showLoadingState(true);

        try {
            const data = await loader.loadData(totalRecords, dataGenerator, {
                onProgress: (progress, totalRecords, loadedRecords, currentChunk, totalChunks) => {
                    this.updateLoadingProgress(progress, totalRecords, loadedRecords, currentChunk, totalChunks);
                    options.onProgress?.(progress, totalRecords, loadedRecords, currentChunk, totalChunks);
                },
                onChunkLoaded: (chunkData, chunkIndex, allData) => {
                    // Update grid with current data
                    this.data = [...allData];
                    this._invalidateAllDateColumns();
                    this.processData();
                    this.render();
                    options.onChunkLoaded?.(chunkData, chunkIndex, allData);
                },
                onComplete: (finalData) => {
                    this.hideLoadingState();
                    // Clear expected total since loading is complete
                    this.expectedTotalRecords = null;
                    options.onComplete?.(finalData);
                },
                onError: (error) => {
                    this.hideLoadingState();
                    // Clear expected total on error
                    this.expectedTotalRecords = null;
                    options.onError?.(error);
                },
                onCancel: (data) => {
                    this.hideLoadingState();
                    // Clear expected total on cancel
                    this.expectedTotalRecords = null;
                    options.onCancel?.(data);
                }
            });

            return data;
        } catch (error) {
            this.hideLoadingState();
            // Clear expected total on error
            this.expectedTotalRecords = null;
            throw error;
        }
    }

    /**
     * Show loading state with progress indicator
     * @private
     */
    showLoadingState(show = true) {
        if (!this.container) return;

        if (show) {
            // Create loading overlay if it doesn't exist
            if (!this.loadingOverlay) {
                this.loadingOverlay = document.createElement('div');
                this.loadingOverlay.className = 'snap-grid-loading-overlay';
                this.loadingOverlay.innerHTML = `
                    <div class="snap-grid-loading-content">
                        <div class="snap-grid-loading-spinner"></div>
                        <div class="snap-grid-loading-text">Loading data...</div>
                        <div class="snap-grid-loading-progress">
                            <div class="snap-grid-progress-bar">
                                <div class="snap-grid-progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="snap-grid-progress-text">0% (0 / 0 records)</div>
                        </div>
                        <button class="snap-grid-cancel-loading" onclick="this.closest('.snap-grid-loading-overlay').dataset.cancelled='true'">
                            Cancel Loading
                        </button>
                    </div>
                `;
            }

            this.container.style.position = 'relative';
            this.container.appendChild(this.loadingOverlay);
            this.loadingOverlay.style.display = 'flex';
        } else if (this.loadingOverlay) {
            this.loadingOverlay.style.display = 'none';
        }
    }

    /**
     * Hide loading state
     * @private
     */
    hideLoadingState() {
        if (this.loadingOverlay) {
            this.loadingOverlay.style.display = 'none';
        }
    }

    /**
     * Update loading progress
     * @private
     */
    updateLoadingProgress(progress, totalRecords, loadedRecords, currentChunk, totalChunks) {
        if (!this.loadingOverlay) return;

        const progressFill = this.loadingOverlay.querySelector('.snap-grid-progress-fill');
        const progressText = this.loadingOverlay.querySelector('.snap-grid-progress-text');
        const loadingText = this.loadingOverlay.querySelector('.snap-grid-loading-text');

        if (progressFill) {
            progressFill.style.width = `${progress}%`;
        }

        if (progressText) {
            progressText.textContent = `${progress.toFixed(1)}% (${loadedRecords.toLocaleString()} / ${totalRecords.toLocaleString()} records)`;
        }

        if (loadingText) {
            loadingText.textContent = `Loading data... (Chunk ${currentChunk}/${totalChunks})`;
        }
    }

    /**
     * Check if loading is cancelled
     * @private
     */
    isLoadingCancelled() {
        return this.loadingOverlay?.dataset.cancelled === 'true';
    }

    refreshCells(params = {}) {
        // Minimal: re-render rows (fast with virtualization)
        this.renderRows();
        return true;
    }

    redrawRows(params = {}) {
        // Minimal: re-render rows
        this.renderRows();
        return true;
    }

    ensureColumnVisible(field) {
        const index = this.getColumnIndex(field);
        if (index < 0 || !this.bodyElement) return false;
        const col = this.processedColumns[index];
        if (col.hide) return false;
        if (col.pinned) return true; // already visible

        // Compute cumulative x of the target column (excluding hidden columns)
        let x = 0;
        let leftPinnedWidth = 0;
        let rightPinnedWidth = 0;
        for (let i = 0; i < this.processedColumns.length; i++) {
            const c = this.processedColumns[i];
            const w = c.hide ? 0 : (c.width || 0);
            if (c.pinned === 'left') leftPinnedWidth += w;
            if (c.pinned === 'right') rightPinnedWidth += w;
        }
        for (let i = 0; i < this.processedColumns.length; i++) {
            const c = this.processedColumns[i];
            if (c.pinned) continue; // center scroll only accounts for non-pinned
            if (i === index) break;
            x += (c.hide ? 0 : (c.width || 0));
        }

        const viewportW = this.bodyElement.clientWidth;
        const current = this.bodyElement.scrollLeft || 0;
        const colWidth = col.width || 0;

        const viewLeft = current + leftPinnedWidth;
        const viewRight = current + viewportW - rightPinnedWidth;
        const colLeft = leftPinnedWidth + x;
        const colRight = colLeft + colWidth;

        if (colLeft < viewLeft) {
            // scroll so left edge aligns
            const newLeft = colLeft - leftPinnedWidth;
            this.bodyElement.scrollLeft = Math.max(0, newLeft);
        } else if (colRight > viewRight) {
            // scroll so right edge is visible
            const newLeft = (colRight - viewportW) + rightPinnedWidth;
            this.bodyElement.scrollLeft = Math.max(0, newLeft);
        }
        return true;
    }

    ensureIndexVisible(index) {
        if (!this.bodyElement) return false;
        const rowHeight = this.options.rowHeight;
        const viewportH = this.bodyElement.clientHeight;
        const scale = this._scrollScale || 1;
        const currentTop = this.bodyElement.scrollTop || 0;
        const virtualCurrentTop = currentTop * scale;
        const rowTopVirtual = index * rowHeight;
        const rowBottomVirtual = rowTopVirtual + rowHeight;
        if (rowTopVirtual < virtualCurrentTop) {
            this.bodyElement.scrollTop = Math.floor(rowTopVirtual / scale);
        } else if (rowBottomVirtual > virtualCurrentTop + viewportH * scale) {
            this.bodyElement.scrollTop = Math.floor((rowBottomVirtual - viewportH * scale) / scale);
        }
        return true;
    }

    sizeColumnsToFit() {
        if (!this.bodyElement) return false;
        const viewportW = this.bodyElement.clientWidth;
        // Calculate total pinned widths
        let leftPinned = 0, rightPinned = 0;
        const centerCols = [];
        this.processedColumns.forEach(c => {
            const w = c.hide ? 0 : (c.width || 0);
            if (c.pinned === 'left') leftPinned += w;
            else if (c.pinned === 'right') rightPinned += w;
            else centerCols.push(c);
        });
        const available = Math.max(0, viewportW - leftPinned - rightPinned - this.getScrollbarWidth());
        const current = centerCols.reduce((s, c) => s + (c.hide ? 0 : (c.width || 0)), 0);
        if (available <= 0 || current <= 0 || !centerCols.length) return false;
        const ratio = available / current;
        centerCols.forEach(c => { if (!c.hide) c.width = Math.max(20, Math.floor((c.width || 0) * ratio)); });
        this.render();
        return true;
    }

    /**
     * Measure vertical scrollbar width in body viewport
     */
    getScrollbarWidth() {
        if (!this.bodyElement) return 0;
        return Math.max(0, this.bodyElement.offsetWidth - this.bodyElement.clientWidth);
    }

    /**
     * Render cell content with custom renderers
     */
    renderCellContent(value, column, rowData, rowIndex) {
        // Custom cell renderer
        if (column.cellRenderer && typeof column.cellRenderer === 'function') {
            return column.cellRenderer(value, column, rowData, rowIndex);
        }

        // Type-based rendering
        switch (column.type) {
            case 'number':
                return this.formatNumber(value);
            case 'currency':
                return this.formatCurrency(value);
            case 'date':
                return this.formatDate(value);
            case 'boolean':
                return this.formatBoolean(value);
            case 'status':
                return this.renderStatusCell(value);
            default:
                return this.escapeHtml(String(value || ''));
        }
    }

    /**
     * Render status with colored dot + same-colored text
     */
    renderStatusCell(value) {
        const { className, label } = this.getStatusStyle(value);
        const safe = this.escapeHtml(String(label || ''));
        return `<span class="snap-grid-status ${className}"><span class="dot"></span><span class="text">${safe}</span></span>`;
    }

    /**
     * Map raw status value to a color class
     */
    getStatusStyle(value) {
        const raw = String(value || '').trim();
        const v = raw.toLowerCase();
        // Status mapping as confirmed by user
        const blue = ['processing', 'auto-uploaded', 'translating'];
        const amber = ['under review', 'timed out', 'locked'];
        const red = ['declined', 'rejected', 'removed'];
        const green = ['live'];
        const gray = ['draft'];

        let className = 'status-gray';
        if (blue.includes(v)) className = 'status-blue';
        else if (amber.includes(v)) className = 'status-amber';
        else if (red.includes(v)) className = 'status-red';
        else if (green.includes(v)) className = 'status-green';
        else if (gray.includes(v)) className = 'status-gray';

        return { className: className, label: raw };
    }

    /**
     * Format number values
     */
    formatNumber(value) {
        if (value == null || value === '') return '';
        const num = Number(value);
        return isNaN(num) ? String(value) : num.toLocaleString();
    }

    /**
     * Format currency values
     */
    formatCurrency(value) {
        if (value == null || value === '') return '';
        const num = Number(value);
        return isNaN(num) ? String(value) : new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(num);
    }

    /**
     * Format date values
     */
    formatDate(value) {
        if (!value) return '';
        const date = new Date(value);
        return isNaN(date.getTime()) ? String(value) : date.toLocaleDateString();
    }

    /**
     * Format boolean values
     */
    formatBoolean(value) {
        if (value == null) return '';
        return value ? '✓' : '✗';
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Handle checkbox cell clicks
     */
    handleCheckboxClick(event) {
        const checkboxCell = event.target.closest('.snap-grid-checkbox-cell');
        if (!checkboxCell) return;

        const rowIndex = parseInt(checkboxCell.dataset.rowIndex);

        if (event.shiftKey && this.lastSelectedRowIndex != null) {
            // Shift + click: select a continuous range between the last anchor and current row
            this.selectRowRange(this.lastSelectedRowIndex, rowIndex, true);
        } else {
            // Normal click toggles the single row
            this.toggleRowSelection(rowIndex);
        }

        // Update range anchor
        this.lastSelectedRowIndex = rowIndex;
        event.stopPropagation();
    }

    /**
     * Handle header checkbox clicks
     */
    handleHeaderCheckboxClick(event) {
        this.toggleAllRowsSelection();
        event.stopPropagation();
    }

    /**
     * Toggle row selection
     */
    toggleRowSelection(rowIndex) {
        if (this.selectedRows.has(rowIndex)) {
            this.selectedRows.delete(rowIndex);
        } else {
            this.selectedRows.add(rowIndex);
        }

        this.updateSelectionState();
        this.render();

        if (this.options.onSelectionChanged) {
            this.options.onSelectionChanged(this.getSelectedData());
        }
    }

    /**
     * Toggle all rows selection
     */
    toggleAllRowsSelection() {
        if (this.allRowsSelected) {
            this.selectedRows.clear();
        } else {
            this.selectedRows.clear();


            for (let i = 0; i < this.sortedIdx.length; i++) {
                this.selectedRows.add(i);
            }
        }

        this.updateSelectionState();
        this.render();

        if (this.options.onSelectionChanged) {
            this.options.onSelectionChanged(this.getSelectedData());
        }
    }

    /**
     * Update selection state
     */
    updateSelectionState() {
        const totalRows = this.sortedIdx.length;
        const selectedCount = this.selectedRows.size;

        this.allRowsSelected = selectedCount === totalRows && totalRows > 0;
        this.indeterminateSelection = selectedCount > 0 && selectedCount < totalRows;

        // Show/Hide Delete + Export buttons to match old grid behavior
        const show = selectedCount > 0;
        if (this.controls?.deleteBtn) this.controls.deleteBtn.style.display = show ? 'flex' : 'none';
        if (this.controls?.exportBtn) this.controls.exportBtn.style.display = show ? 'flex' : 'none';
        // If selection is cleared, also close any open Delete/Export dropdown menus
        if (!show) {
            const dd = this.controls?.deleteDropdown;
            if (dd) {
                dd.classList.remove('focused');
                const dm = dd.querySelector?.('.dropdown-menu');
                if (dm) dm.classList.add('hidden');
            }
            const ed = this.controls?.exportDropdown;
            if (ed) {
                ed.classList.remove('focused');
                const em = ed.querySelector?.('.dropdown-menu');
                if (em) em.classList.add('hidden');
            }
        }
        // Show/Hide Shift+Click hint in footer
        if (this.footer?.hint) this.footer.hint.style.display = show ? 'flex' : 'none';
    }

    /**
     * Select a continuous range of rows between two display indices (inclusive)
     * If select=true, rows are added to selection; if false, rows are removed
     */
    selectRowRange(fromIndex, toIndex, select = true) {
        const start = Math.min(fromIndex, toIndex);
        const end = Math.max(fromIndex, toIndex);
        for (let i = start; i <= end; i++) {
            if (select) this.selectedRows.add(i);
            else this.selectedRows.delete(i);
        }
        this.updateSelectionState();
        this.render();
        if (this.options.onSelectionChanged) {
            this.options.onSelectionChanged(this.getSelectedData());
        }
    }
    /**
     * Handle header cell clicks for sorting and menu
     */
    handleHeaderClick(event) {
        const headerCell = event.target.closest('.snap-grid-header-cell');
        if (!headerCell) return;

        const field = headerCell.dataset.field;
        const column = this.processedColumns.find(col => col.field === field);

        if (!column) return;

        // Skip non-interactive columns
        if (['checkbox','preview','actions'].includes(column.field)) return;

        // Check if menu button was clicked
        if (event.target.closest('.snap-grid-column-menu-btn')) {
            this.showColumnMenu(headerCell, column);
            return;
        }

        // Handle sorting on header text click
        if (column.sortable === false) return;

        // Toggle sort direction
        const currentSort = this.sortState[field];
        if (currentSort === 'asc') {
            this.sortState[field] = 'desc';
        } else if (currentSort === 'desc') {
            delete this.sortState[field];
        } else {
            this.sortState[field] = 'asc';
        }

        // Clear other sorts (single column sort for now)
        Object.keys(this.sortState).forEach(key => {
            if (key !== field) {
                delete this.sortState[key];
            }
        });

        this.processData();
        this.render();

        // Callback
        if (this.options.onSort) {
            this.options.onSort(field, this.sortState[field]);
        }
    }

    /**
     * Handle row clicks for selection
     */
    handleRowClick(event) {
        const row = event.target.closest('.snap-grid-row');
        if (!row) return;

        const rowIndex = parseInt(row.dataset.index);

        if (this.options.selectable) {
            if (event.shiftKey && this.lastSelectedRowIndex != null) {
                // Shift + click on row selects range as well
                this.selectRowRange(this.lastSelectedRowIndex, rowIndex, true);
            } else {
                // Toggle selection via the central method so checkbox UI updates
                this.toggleRowSelection(rowIndex);
            }
        }

        // Update range anchor
        this.lastSelectedRowIndex = rowIndex;

        // Callback
        if (this.options.onRowClick) {
            const rowData = this.data[this.sortedIdx[rowIndex]];
            this.options.onRowClick(rowData, rowIndex, event);
        }
    }

    /**
     * Handle cell clicks for editing
     */
    handleCellClick(event) {
        const cell = event.target.closest('.snap-grid-cell');
        if (!cell || !cell.classList.contains('editable')) return;

        const field = cell.dataset.field;
        const rowIndex = parseInt(cell.dataset.row);
        const column = this.options.columns.find(col => col.field === field);

        if (!column || column.editable === false) return;

        this.startCellEdit(cell, field, rowIndex);
    }

    /**
     * Start editing a cell
     */
    startCellEdit(cell, field, rowIndex) {
        const currentValue = this.data[this.sortedIdx[rowIndex]][field];

        // Create input element
        const input = document.createElement('input');
        input.type = 'text';
        input.value = currentValue || '';
        input.className = 'snap-grid-cell-editor';

        // Replace cell content
        cell.innerHTML = '';
        cell.appendChild(input);
        cell.classList.add('editing');

        // Focus and select
        input.focus();
        input.select();

        // Save on blur or enter
        const saveEdit = () => {
            const newValue = input.value;
            this.finishCellEdit(cell, field, rowIndex, newValue);
        };

        // Cancel on escape
        const cancelEdit = () => {
            this.cancelCellEdit(cell, field, rowIndex);
        };

        input.addEventListener('blur', saveEdit);
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveEdit();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
    }

    /**
     * Finish cell editing
     */
    finishCellEdit(cell, field, rowIndex, newValue) {
        const oldValue = this.data[this.sortedIdx[rowIndex]][field];

        // Update data
        this.data[this.sortedIdx[rowIndex]][field] = newValue;

        // Update original data array
        const originalIndex = this.sortedIdx[rowIndex];
        if (originalIndex !== -1) {
            this.data[originalIndex][field] = newValue;
            this._markDateIndexDirty(field, originalIndex);
        }

        // Re-render cell
        const column = this.options.columns.find(col => col.field === field);
        const cellContent = this.renderCellContent(newValue, column, this.data[this.sortedIdx[rowIndex]], rowIndex);

        cell.innerHTML = '';
        if (typeof cellContent === 'string') {
            cell.innerHTML = cellContent;
        } else {
            cell.appendChild(cellContent);
        }

        cell.classList.remove('editing');

        // Callback
        if (this.options.onCellEdit) {
            this.options.onCellEdit(field, newValue, oldValue, this.data[this.sortedIdx[rowIndex]], rowIndex);
        }
    }

    /**
     * Cancel cell editing
     */
    cancelCellEdit(cell, field, rowIndex) {
        const column = this.options.columns.find(col => col.field === field);
        const currentValue = this.data[this.sortedIdx[rowIndex]][field];
        const cellContent = this.renderCellContent(currentValue, column, this.data[this.sortedIdx[rowIndex]], rowIndex);

        cell.innerHTML = '';
        if (typeof cellContent === 'string') {
            cell.innerHTML = cellContent;
        } else {
            cell.appendChild(cellContent);
        }

        cell.classList.remove('editing');
    }

    /**
     * Handle arrow key navigation
     */
    handleArrowKeys(event) {
        const focusedCell = document.activeElement.closest('.snap-grid-cell');
        if (!focusedCell) return;

        const row = focusedCell.closest('.snap-grid-row');
        const rowIndex = parseInt(row.dataset.index);
        const columnIndex = parseInt(focusedCell.dataset.column);

        let newRowIndex = rowIndex;
        let newColumnIndex = columnIndex;

        switch (event.key) {
            case 'ArrowUp':
                newRowIndex = Math.max(0, rowIndex - 1);
                break;
            case 'ArrowDown':
                newRowIndex = Math.min(this.sortedIdx.length - 1, rowIndex + 1);
                break;
            case 'ArrowLeft':
                newColumnIndex = Math.max(0, columnIndex - 1);
                break;
            case 'ArrowRight':
                newColumnIndex = Math.min(this.options.columns.length - 1, columnIndex + 1);
                break;
        }

        // Find and focus the new cell
        const newCell = document.querySelector(
            `.snap-grid-cell[data-row="${newRowIndex}"][data-column="${newColumnIndex}"]`
        );

        if (newCell) {
            newCell.focus();
            newCell.scrollIntoView({ block: 'nearest', inline: 'nearest' });
        }

        event.preventDefault();
    }

    /**
     * Handle enter key
     */
    handleEnterKey(event) {
        const focusedCell = document.activeElement.closest('.snap-grid-cell');
        if (focusedCell && focusedCell.classList.contains('editable')) {
            const field = focusedCell.dataset.field;
            const rowIndex = parseInt(focusedCell.dataset.row);
            this.startCellEdit(focusedCell, field, rowIndex);
            event.preventDefault();
        }
    }

    /**
     * Handle escape key
     */
    handleEscapeKey(event) {
        const editingCell = document.querySelector('.snap-grid-cell.editing');
        if (editingCell) {
            const field = editingCell.dataset.field;
            const rowIndex = parseInt(editingCell.dataset.row);
            this.cancelCellEdit(editingCell, field, rowIndex);
            event.preventDefault();
        }
    }

    /**
     * Set up column resizing
     */
    setupColumnResize() {
        let isResizing = false;
        let currentColumn = null;
        let startX = 0;
        let startWidth = 0;
        let currentField = null;
        this._resizeRaf = null;

        this.container.addEventListener('mousedown', (e) => {
            const resizeHandle = e.target.closest('.snap-grid-resize-handle');
            if (!resizeHandle) return;

            isResizing = true;
            currentColumn = resizeHandle.closest('.snap-grid-header-cell');
            currentField = currentColumn?.dataset?.field || null;
            startX = e.clientX;
            startWidth = currentColumn.offsetWidth;

            document.body.style.cursor = 'col-resize';
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isResizing || !currentColumn) return;

            const diff = e.clientX - startX;

            // Calculate minimum width based on header text
            const field = currentField;
            const column = this.getColumn(field);
            const minWidth = column ? this.calculateMinHeaderWidth(column) : 80;
            const newWidth = Math.max(minWidth, startWidth + diff);

            // Update column width on the live DOM and state
            if (column) {
                column.width = newWidth;
                this.updateColumnWidthDOM(field, newWidth);
                // Throttle expensive metrics updates to animation frames
                if (!this._resizeRaf) {
                    this._resizeRaf = requestAnimationFrame(() => {
                        this._resizeRaf = null;
                        this.updateHorizontalMetrics();
                        this.syncHorizontalScroll();
                    });
                }
            }
        });

        document.addEventListener('mouseup', () => {
            if (isResizing) {
                isResizing = false;
                const savedLeft = this.scrollLeft || 0;
                currentColumn = null;
                currentField = null;
                document.body.style.cursor = '';
                // Re-render to recompute pinned offsets and ensure full consistency
                this.render();
                // Restore horizontal scroll position to body
                if (this.bodyElement && this.bodyElement.scrollLeft !== savedLeft) {
                    this.bodyElement.scrollLeft = savedLeft;
                }
                this.syncHorizontalScroll();
                // Persist as Custom when width changes via drag
                this.onUserCustomized && this.onUserCustomized('resize');
            }
        });
    }

    /**
     * Update header and visible body cells for a given column field to a new width.
     * Avoids full re-render during drag for smooth UX.
     */
    updateColumnWidthDOM(field, width) {
        // Get the column to calculate minimum width
        const column = this.getColumn(field);
        const minWidth = column ? this.calculateMinHeaderWidth(column) : 80;
        const w = Math.max(minWidth, Math.floor(Number(width) || 0));

        // Header cell
        const headerCell = this.headerElement?.querySelector(`.snap-grid-header-cell[data-field="${field}"]`);
        if (headerCell) {
            headerCell.style.width = `${w}px`;
            headerCell.style.minWidth = `${w}px`;
        }
        // Body cells (only visible rows)
        if (this.viewportElement) {
            const cells = this.viewportElement.querySelectorAll(`.snap-grid-cell[data-field="${field}"]`);
            cells.forEach(cell => {
                cell.style.width = `${w}px`;
                cell.style.minWidth = `${w}px`;
            });
        }
        // No extra pinned offset recalculation needed; full render handles consistency
    }

    /**
     * Show column menu
     */
    showColumnMenu(headerCell, column) {
        // Remove existing menus
        document.querySelectorAll('.snap-grid-column-menu').forEach(menu => menu.remove());

        const menu = document.createElement('div');
        menu.className = 'snap-grid-column-menu';

        // Sort options
        if (this.options.sortable && column.sortable !== false) {
            const sortAsc = document.createElement('div');
            sortAsc.className = 'snap-grid-menu-item';
            sortAsc.innerHTML = '<span class="snap-grid-menu-item-icon"><img src="assets/ascending-ic.svg" alt="Ascending" class="menu-icon-img"></span>Sort Ascending';
            sortAsc.addEventListener('click', () => {
                this.setSort(column.field, 'asc');
                menu.remove();
            });
            menu.appendChild(sortAsc);

            const sortDesc = document.createElement('div');
            sortDesc.className = 'snap-grid-menu-item';
            sortDesc.innerHTML = '<span class="snap-grid-menu-item-icon"><img src="assets/descending-ic.svg" alt="Descending" class="menu-icon-img"></span>Sort Descending';
            sortDesc.addEventListener('click', () => {
                this.setSort(column.field, 'desc');
                menu.remove();
            });
            menu.appendChild(sortDesc);

            const clearSort = document.createElement('div');
            clearSort.className = 'snap-grid-menu-item';
            clearSort.innerHTML = '<span class="snap-grid-menu-item-icon">✕</span>Clear Sort';
            clearSort.addEventListener('click', () => {
                this.setSort(column.field, null);
                menu.remove();
            });
            menu.appendChild(clearSort);

            const divider = document.createElement('div');
            divider.className = 'snap-grid-menu-divider';
            menu.appendChild(divider);
        }

        // Filter options
        if (this.options.filterable && column.filterable !== false) {
            const filterContainer = document.createElement('div');
            filterContainer.style.padding = '8px 16px';

            const filterInput = document.createElement('input');
            filterInput.type = 'text';
            filterInput.className = 'snap-grid-filter-input';
            filterInput.placeholder = `Filter ${column.headerName || column.field}...`;
            filterInput.value = this.filterState[column.field]?.value || '';

            filterInput.addEventListener('keyup', (e) => {
                if (e.key === 'Enter') {
                    this.setFilter(column.field, filterInput.value);
                    menu.remove();
                }
            });

            filterContainer.appendChild(filterInput);
            menu.appendChild(filterContainer);

            const clearFilter = document.createElement('div');
            clearFilter.className = 'snap-grid-menu-item';
            clearFilter.innerHTML = '<span class="snap-grid-menu-item-icon">✕</span>Clear Filter';
            clearFilter.addEventListener('click', () => {
                this.setFilter(column.field, '');
                menu.remove();
            });
            menu.appendChild(clearFilter);
        }

        // Position and show menu
        headerCell.style.position = 'relative';
        headerCell.appendChild(menu);

        // Close menu when clicking outside (but not on datepicker popup)
        setTimeout(() => {
            document.addEventListener('click', function closeMenu(e) {
                // Don't close if clicking on the menu itself
                if (menu.contains(e.target)) {
                    return;
                }

                // Don't close if clicking on a datepicker popup or any datepicker-related element
                const datepickerPopup = e.target.closest('.snap-datepicker-popup');
                const datepickerElement = e.target.closest('.snap-datepicker');
                const datepickerDropdown = e.target.closest('.snap-datepicker-dropdown');

                if (datepickerPopup || datepickerElement || datepickerDropdown) {
                    return;
                }

                // Also check if the target has any datepicker-related classes
                const targetClasses = e.target.className || '';
                if (typeof targetClasses === 'string' && targetClasses.includes('snap-datepicker')) {
                    return;
                }

                // Close the menu and remove any associated datepicker popups
                const existingPicker = document.querySelector('.snap-datepicker-popup');
                if (existingPicker) {
                    existingPicker.remove();
                }

                menu.remove();
                document.removeEventListener('click', closeMenu);
            });
        }, 0);
    }

    /**
     * Create ARIA live region for screen reader announcements
     */
    createAriaLiveRegion() {
        this.ariaLiveRegion = document.createElement('div');
        this.ariaLiveRegion.className = 'snap-grid-sr-only';
        this.ariaLiveRegion.setAttribute('aria-live', 'polite');
        this.ariaLiveRegion.setAttribute('aria-atomic', 'true');
        this.container.appendChild(this.ariaLiveRegion);
    }

    /**
     * Announce message to screen readers
     */
    announceToScreenReader(message) {
        if (this.ariaLiveRegion) {
            this.ariaLiveRegion.textContent = message;

            // Clear after announcement
            setTimeout(() => {
                this.ariaLiveRegion.textContent = '';
            }, 1000);
        }
    }

    /**
     * Add accessibility attributes to header cells
     */
    addHeaderAccessibility(headerCell, column, columnIndex) {
        headerCell.setAttribute('role', 'columnheader');
        headerCell.setAttribute('tabindex', '0');
        headerCell.setAttribute('aria-sort', 'none');

        if (this.sortState[column.field]) {
            const direction = this.sortState[column.field] === 'asc' ? 'ascending' : 'descending';
            headerCell.setAttribute('aria-sort', direction);
        }

        // Add column index for screen readers
        headerCell.setAttribute('aria-colindex', columnIndex + 1);

        // Add keyboard instructions
        if (this.options.sortable && column.sortable !== false) {
            headerCell.setAttribute('aria-label',
                `${column.headerName || column.field}, column ${columnIndex + 1}. Click to sort.`);
        }
    }

    /**
     * Add accessibility attributes to data cells
     */
    addCellAccessibility(cell, column, rowData, rowIndex, columnIndex) {
        cell.setAttribute('role', 'gridcell');
        cell.setAttribute('tabindex', '-1');
        cell.setAttribute('aria-colindex', columnIndex + 1);
        cell.setAttribute('aria-rowindex', rowIndex + 2); // +2 because header is row 1

        // Add cell description for screen readers
        const value = rowData[column.field];
        const description = `${column.headerName || column.field}: ${this.formatValueForScreenReader(value, column)}`;
        cell.setAttribute('aria-label', description);

        // Mark editable cells
        if (this.options.editable && column.editable !== false) {
            cell.setAttribute('aria-describedby', 'grid-edit-instructions');
        }
    }

    /**
     * Format value for screen reader announcement
     */
    formatValueForScreenReader(value, column) {
        if (value == null || value === '') return 'empty';

        switch (column.type) {
            case 'currency':
                return `${value} dollars`;
            case 'date':
                const date = new Date(value);
                return isNaN(date.getTime()) ? String(value) : date.toLocaleDateString();
            case 'boolean':
                return value ? 'yes' : 'no';
            default:
                return String(value);
        }
    }

    // ========================================================================
    // PUBLIC API METHODS
    // ========================================================================

    /**
     * Update grid data
     */
    updateData(newData) {
        this.data = [...newData];
        this._resetDateCaches();
        this.processData();
        this.render();

        // Announce data update to screen readers
        this.announceToScreenReader(`Grid updated with ${newData.length} rows`);
    }

    /**
     * Get all data
     */
    getData() {
        return [...this.data];
    }

    /**
     * Get filtered and sorted data
     */
    getDisplayedData() {
        return this.sortedIdx.map(i => this.data[i]);
    }

    /**
     * Get selected row data
     */
    getSelectedData() {
        return Array.from(this.selectedRows).map(index => this.data[this.sortedIdx[index]]);
    }

    /**
     * Clear all selections
     */
    clearSelection() {
        this.selectedRows.clear();
        this.lastSelectedRowIndex = null; // Reset range anchor
        document.querySelectorAll('.snap-grid-row.selected').forEach(row => {
            row.classList.remove('selected');
        });
        // Update selection-related UI immediately
        this.updateSelectionState();
        this.updateHeaderFooterStats();
    }

    /**
     * Set column definitions
     */
    setColumns(columns) {
        this.options.columns = columns;
        this.render();
    }

    /**
     * Get column definitions
     */
    getColumns() {
        return [...this.options.columns];
    }

    // REMOVED: Duplicate setFilter function - consolidated into single implementation at line 4445

    // REMOVED: setFilterDebounced - redundant with main setFilter function
    // Use setFilter() directly - it already handles immediate application efficiently

    /**
     * Clear all filters - comprehensive state cleanup
     */
    clearFilters() {
        // Clear any current selection when clearing all filters
        if (this.selectedRows && this.selectedRows.size > 0) {
            this.clearSelection();
        }
        // Clear all filter-related state objects
        this.filterState = {};
        this.checkboxState = {};
        this._checkboxAllValueCounts = {};
        this._checkboxAllValues = Object.create(null);

        // Clear temporary state if it exists
        if (this.tempCheckboxState) {
            this.tempCheckboxState = {};
        }

        // Reset filter dropdown text to "Filters"
        this._resetFilterDropdownText();

        // Apply changes immediately
        this.processData();
        this.render();

        // Update any open filter menus to reflect cleared state
        this._updateOpenFilterMenusAfterClear();

        // Persist as Custom when clearing filters if not part of returning to default or suppressed
        if (this.currentLayoutType !== 'default' && !this._suppressCustomPersist) {
            this.onUserCustomized && this.onUserCustomized('filter');
        }

        // Update Clear Filters button state
        this.updateFilterState();
    }

    /**
     * Reset filter dropdown text to "Filters" (internal helper)
     */
    _resetFilterDropdownText() {
        const filtersDropdown = this.elements?.filtersDropdown?.querySelector('.snap-dropdown');
        if (filtersDropdown) {
            const headerSpan = filtersDropdown.querySelector('.dropdown-header span');
            if (headerSpan) {
                headerSpan.textContent = 'Filters';

            }

            // Clear selection highlighting from all dropdown items
            const dropdownItems = filtersDropdown.querySelectorAll('.dropdown-item');
            dropdownItems.forEach(item => {
                item.classList.remove('selected');
            });

        }
    }

    /**
     * Update open filter menus after clearing filters (internal helper)
     */
    _updateOpenFilterMenusAfterClear() {
        // Find all open column menus and reset their checkbox states
        const openMenus = document.querySelectorAll('.snap-grid-column-menu');
        openMenus.forEach(menu => {
            const checkboxLists = menu.querySelectorAll('.filter-checkbox-list');
            checkboxLists.forEach(list => {
                // Reset all checkboxes to checked state (default state)
                const checkboxIcons = list.querySelectorAll('.checkbox-icon');
                checkboxIcons.forEach(icon => {
                    icon.src = './assets/checkbox-ic.svg';
                    icon.alt = 'Checked';
                });

                // Reset any filter inputs in the menu
                const filterInputs = menu.querySelectorAll('.filter-input-with-icon, input[type="text"], input[type="date"]');
                filterInputs.forEach(input => {
                    input.value = '';
                });

                // Reset dropdowns to default state
                const dropdowns = menu.querySelectorAll('.custom-dropdown');
                dropdowns.forEach(dropdown => {
                    const firstOption = dropdown.querySelector('.dropdown-item');
                    if (firstOption) {
                        const defaultValue = firstOption.getAttribute('data-value') || 'showAll';
                        this.setCustomDropdownValue(dropdown, defaultValue);
                    }
                });
            });
        });
    }

    /**
     * Set sort for a column
     */
    setSort(field, direction) {
        if (direction && ['asc', 'desc'].includes(direction)) {
            this.sortState[field] = direction;
        } else {
            delete this.sortState[field];
        }

        this.processData();
        this.render();

        if (this.options.onSort) {
            this.options.onSort(field, direction);
        }

        // Persist as Custom once for sorting operations
        this.onUserCustomized && this.onUserCustomized('sort');
    }

    /**
     * Clear all sorting
     */
    clearSort() {
        this.sortState = {};
        this.processData();
        this.render();
    }

    /**
     * Resize the grid (call after container size changes)
     */
    resize() {
        this.calculateVisibleRange();
        this.render();
    }

    /**
     * Refresh the grid
     */
    refresh() {
        this.processData();
        this.render();
    }

    /**
     * Destroy the grid and clean up
     */
    destroy() {
        // Remove event listeners
        window.removeEventListener('resize', this.handleResize.bind(this));

        // Clear container
        if (this.container) {
            this.container.innerHTML = '';
            this.container.className = '';
        }

        // Clear references
        this.container = null;
        this.gridElement = null;
        this.headerElement = null;
        this.bodyElement = null;
        this.viewportElement = null;
        this.scrollContainer = null;

        console.log('🗑️ SnapGrid destroyed');
    }

    /**
     * Export data to CSV
     */
    exportToCsv(filename = 'grid-data.csv') {
        // Export only selected rows (safety: require a selection)
        const selected = this.getSelectedData();
        if (!selected || selected.length === 0) {
            console.warn('No rows selected for CSV export');
            return;
        }

        const headers = this.options.columns.map(col => col.headerName || col.field);
        const rows = selected.map(row => this.options.columns.map(col => (row[col.field] ?? '')));

        const csvContent = [headers, ...rows]
            .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
            .join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        URL.revokeObjectURL(link.href);
    }

    /**
     * Get grid statistics
     */
    getStats() {
        return {
            totalRows: this.data.length,
            filteredRows: this.filteredIdx.length,
            displayedRows: this.sortedIdx.length,
            selectedRows: this.selectedRows.size,
            lastRenderDuration: this.lastRenderDuration,
            visibleRange: {
                start: this.visibleStartIndex,
                end: this.visibleEndIndex
            }
        };
    }

    // ==========================================================================
    // Column Menu Functionality
    // ==========================================================================

    /**
     * Show column menu with tabbed interface
     */
    showColumnMenu(headerCell, column) {
        // Add error checking
        if (!headerCell) {
            console.error('showColumnMenu: headerCell parameter is undefined');
            return;
        }
        if (!column) {
            console.error('showColumnMenu: column parameter is undefined');
            return;
        }

        // Remove existing menu
        this.hideColumnMenu();

        const menu = document.createElement('div');
        menu.className = 'snap-grid-column-menu tabbed-menu';
        menu.setAttribute('role', 'menu');
        menu.setAttribute('aria-label', `Column menu for ${column.headerName || column.field}`);
        menu.setAttribute('data-field', column.field);

        // Create tab header
        const tabHeader = document.createElement('div');
        tabHeader.className = 'menu-tab-header';

        // Tab buttons with icons
        const filterTab = this.createTabButton('filter', 'filters', 'Filter');
        const managementTab = this.createTabButton('management', 'column-man', 'Column Management');
        const visibilityTab = this.createTabButton('visibility', 'show-hide-col', 'Show/Hide Columns');

        tabHeader.appendChild(filterTab);
        tabHeader.appendChild(managementTab);
        tabHeader.appendChild(visibilityTab);

        // Create tab content container
        const tabContent = document.createElement('div');
        tabContent.className = 'menu-tab-content';

        // Create tab panels
        const filterPanel = this.createFilterTab(column);
        const managementPanel = this.createManagementTab(column);
        const visibilityPanel = this.createVisibilityTab();

        tabContent.appendChild(filterPanel);
        tabContent.appendChild(managementPanel);
        tabContent.appendChild(visibilityPanel);

        menu.appendChild(tabHeader);
        menu.appendChild(tabContent);

        // Prevent menu from closing when clicking inside it
        menu.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Track active menu for scroll repositioning
        const targetElement = headerCell;

        this.activeMenu = menu;
        this.activeMenuTarget = targetElement;

        // Append to grid container for proper relative positioning
        this.container.appendChild(menu);
        this.currentColumnMenu = menu;
        this.activeDialogs = this.activeDialogs || new Set();
        this.activeDialogs.add(menu);

        // Position menu after it's added to DOM for accurate positioning
        try {
            this.positionMenuWithCollisionDetection(menu, targetElement);
        } catch (error) {
            console.error('Error positioning menu:', error);
            // Fallback positioning
            menu.style.position = 'fixed';
            menu.style.top = '100px';
            menu.style.left = '100px';
            menu.style.zIndex = '1001';
        }

        // Ensure menu is properly positioned after being added to DOM
        // This helps with accurate positioning calculations
        requestAnimationFrame(() => {
            if (this.activeMenu === menu && this.activeMenuTarget) {
                this.positionMenuWithCollisionDetection(menu, this.activeMenuTarget);
            }
        });

        // Set up tab switching
        this.setupTabSwitching(menu);

        // Set default active tab
        this.setActiveTab(menu, 'filter');

        // Add menu-active class to header cell
        headerCell.classList.add('menu-active');

        // Close menu when clicking outside (with small delay to prevent immediate closure)
        setTimeout(() => {
            const handleOutsideClick = (e) => {
                // Don't close if clicking on the menu itself or the menu button
                if (menu.contains(e.target) || headerCell.contains(e.target)) {
                    return;
                }

                // Don't close if clicking on a datepicker popup or any datepicker-related element
                const datepickerPopup = e.target.closest('.snap-datepicker-popup');
                const datepickerElement = e.target.closest('.snap-datepicker');
                const datepickerDropdown = e.target.closest('.snap-datepicker-dropdown');

                if (datepickerPopup || datepickerElement || datepickerDropdown) {
                    console.log('Datepicker click detected, keeping menu open');
                    return;
                }

                // Also check if the target has any datepicker-related classes
                const targetClasses = e.target.className || '';
                if (typeof targetClasses === 'string' && targetClasses.includes('snap-datepicker')) {
                    console.log('Datepicker class click detected, keeping menu open');
                    return;
                }

                console.log('Outside click detected, closing menu');
                this.hideColumnMenu();
                document.removeEventListener('click', handleOutsideClick);
                document.removeEventListener('keydown', handleEscapeKey);
            };

            const handleEscapeKey = (e) => {
                if (e.key === 'Escape') {
                    this.hideColumnMenu();
                    document.removeEventListener('click', handleOutsideClick);
                    document.removeEventListener('keydown', handleEscapeKey);
                }
            };

            document.addEventListener('click', handleOutsideClick);
            document.addEventListener('keydown', handleEscapeKey);
        }, 100);
    }

    /**
     * Hide column menu - exact copy from old grid
     */
    hideColumnMenu() {
        // Remove any associated datepicker popups first
        const existingPicker = document.querySelector('.snap-datepicker-popup');
        if (existingPicker) {
            existingPicker.remove();
        }

        if (this.currentColumnMenu) {
            if (this.activeDialogs) {
                this.activeDialogs.delete(this.currentColumnMenu);
            }
            this.currentColumnMenu.remove();
            this.currentColumnMenu = null;
        }

        // Clear active menu tracking
        this.activeMenu = null;
        this.activeMenuTarget = null;

        // Remove menu-active class from all header cells
        const activeHeaders = this.container.querySelectorAll('.snap-grid-header-cell.menu-active');
        activeHeaders.forEach(header => header.classList.remove('menu-active'));
    }

    /**
     * Position menu with collision detection during scrolling - exact copy from old grid
     */
    positionMenuWithCollisionDetection(menu, targetElement) {
        // Ensure menu has proper positioning styles
        menu.style.position = 'absolute';
        menu.style.zIndex = '1001';

        // Check if target element is valid and visible
        if (!targetElement || !targetElement.getBoundingClientRect) {
            console.warn('⚠️ Invalid target element for menu positioning, falling back to left side');
            this.positionMenuOnLeftSide();
            return;
        }

        const rect = targetElement.getBoundingClientRect();

        // Check if target element is not visible (hidden column)
        if (rect.width === 0 && rect.height === 0) {
            console.warn('⚠️ Target element is not visible (hidden column), falling back to left side');
            this.positionMenuOnLeftSide();
            return;
        }
        const containerRect = this.container.getBoundingClientRect();
        const menuRect = menu.getBoundingClientRect();
        const menuWidth = menuRect.width || 281; // Fallback to CSS width if not rendered yet
        const menuHeight = menuRect.height || 400; // Fallback to CSS height if not rendered yet
        const padding = 8;

        // Get pinned column boundaries in viewport coordinates
        const pinnedLeftWidth = this.calculateTotalPinnedWidth ? this.calculateTotalPinnedWidth('left') : 0;
        const pinnedRightWidth = this.calculateTotalPinnedWidth ? this.calculateTotalPinnedWidth('right') : 0;
        const containerWidth = this.container.offsetWidth;

        // Calculate pinned boundaries in viewport coordinates
        const leftPinnedEnd = containerRect.left + pinnedLeftWidth;
        const rightPinnedStart = containerRect.right - pinnedRightWidth;

        // Calculate scroll-adjusted positions
        const scrollLeft = this.container.scrollLeft || 0;
        const scrollTop = this.container.scrollTop || 0;

        const targetRelativeToContainer = {
            left: rect.left - containerRect.left + scrollLeft,
            right: rect.right - containerRect.left + scrollLeft,
            top: rect.top - containerRect.top + scrollTop,
            bottom: rect.bottom - containerRect.top + scrollTop,
            width: rect.width
        };

        // Determine original menu position preference
        const containerCenter = containerWidth / 2;
        const columnCenter = targetRelativeToContainer.left + (targetRelativeToContainer.width / 2);
        const isLeftSide = columnCenter < containerCenter;

        let left;

        if (isLeftSide) {
            // Prefer right side of column
            left = targetRelativeToContainer.right;

            // Check collision with right pinned columns using viewport coordinates
            const menuRightEdge = containerRect.left + left + menuWidth - scrollLeft;
            if (menuRightEdge > rightPinnedStart && pinnedRightWidth > 0) {
                // Stop at right pinned boundary (convert back to container coordinates)
                left = rightPinnedStart - containerRect.left + scrollLeft - menuWidth;
            }
        } else {
            // Prefer left side of column (account for 1.5px border)
            left = targetRelativeToContainer.left - menuWidth + 1.5;

            // Check collision with left pinned columns using viewport coordinates
            const menuLeftEdge = containerRect.left + left - scrollLeft;
            if (menuLeftEdge < leftPinnedEnd && pinnedLeftWidth > 0) {
                // Stop at left pinned boundary (convert back to container coordinates)
                left = leftPinnedEnd - containerRect.left + scrollLeft;
            }
        }

        // Ensure menu stays within container bounds
        if (left < 0) left = 0;
        if (left + menuWidth > containerWidth) left = containerWidth - menuWidth;

        // Vertical positioning
        let top = targetRelativeToContainer.bottom + 2;

        if (top + menuHeight > this.container.offsetHeight - padding) {
            top = targetRelativeToContainer.top - menuHeight - 2;
            if (top < padding) {
                top = padding;
            }
        }

        // Apply positioning
        menu.style.left = `${left}px`;
        menu.style.top = `${top}px`;
    }

    /**
     * Position menu on the left side when target column is not found
     */
    positionMenuOnLeftSide() {
        if (!this.activeMenu) {
            return;
        }

        // Ensure menu has proper positioning styles
        this.activeMenu.style.position = 'absolute';
        this.activeMenu.style.zIndex = '1001';

        const containerRect = this.container.getBoundingClientRect();
        const menuRect = this.activeMenu.getBoundingClientRect();
        const menuWidth = menuRect.width || 281; // Fallback to CSS width if not rendered yet
        const menuHeight = menuRect.height || 400; // Fallback to CSS height if not rendered yet
        const padding = 8;

        // Get pinned column boundaries
        const pinnedLeftWidth = this.calculateTotalPinnedWidth ? this.calculateTotalPinnedWidth('left') : 0;
        const containerWidth = this.container.offsetWidth;

        // Calculate scroll-adjusted positions
        const scrollLeft = this.container.scrollLeft || 0;
        const scrollTop = this.container.scrollTop || 0;

        // Position on the left side, respecting pinned columns
        const leftPinnedEnd = containerRect.left + pinnedLeftWidth;
        let left = Math.max(0, leftPinnedEnd - containerRect.left + scrollLeft);

        // Ensure menu stays within container bounds
        if (left + menuWidth > containerWidth) {
            left = containerWidth - menuWidth;
        }

        // Position vertically at the same level as the header row
        const headerRow = this.container.querySelector('.snap-grid-header-row');
        let top = padding; // Default fallback

        if (headerRow) {
            const headerRect = headerRow.getBoundingClientRect();
            const containerRect = this.container.getBoundingClientRect();
            top = headerRect.bottom - containerRect.top + scrollTop + 2; // 2px gap below header
        }

        // Apply positioning
        this.activeMenu.style.left = `${left}px`;
        this.activeMenu.style.top = `${top}px`;

        console.log('📍 Positioned menu on left side due to missing target column');
    }

    /**
     * Create tab button for menu
     */
    createTabButton(tabId, iconName, label) {
        const button = document.createElement('button');
        button.className = 'menu-tab-btn';
        button.setAttribute('data-tab', tabId);
        button.setAttribute('aria-label', label);

        // Create icon element
        const icon = document.createElement('img');
        icon.className = 'tab-icon';
        icon.src = `assets/${iconName}-inactive-ic.svg`;
        icon.alt = label;
        icon.draggable = false;

        button.appendChild(icon);
        return button;
    }

    /**
     * Create filter tab content - exact copy from old grid
     */
    createFilterTab(column) {
        const panel = document.createElement('div');
        panel.className = 'menu-tab-panel';
        panel.setAttribute('data-tab-panel', 'filter');

        // Get existing filter state for restoration
        const existingFilter = this.filterState[column.field];
        let normalizedDateOperator = null;

        // Get column type using the new type system
        const columnType = this.getColumnType(column.field, column);

        // Declare applyFilter function early so it can be used by createLogicToggle
        let applyFilter;

        // Determine UI layout based on column type
        const isTextSpecial = columnType === 'text-special';
        const isDateOnly = columnType === 'date';
        const isNumericOnly = columnType === 'numeric' || columnType === 'percentage';
        const isFixed = columnType === 'fixed';

        // First dropdown (full width) - hidden for checkbox-only columns, shown for date-only columns
        const filterOptions = this.getFilterTypeOptions(column);

        // Get the first option value correctly from the options array
        const defaultOperator = filterOptions.length > 0 ? filterOptions[0].value : 'showAll';

        const firstDropdown = this.createCustomDropdown(
            filterOptions,
            defaultOperator
        );
        firstDropdown.classList.add('filter-type-dropdown');
        if (isTextSpecial || isFixed) {
            firstDropdown.style.display = 'none';
        }

        // First filter input (full width with filter icon) - hidden for text-special, date-only, and fixed columns
        const firstFilterWrapper = document.createElement('div');
        firstFilterWrapper.className = 'filter-input-wrapper';
        if (isTextSpecial || isDateOnly || isFixed) {
            firstFilterWrapper.style.display = 'none';
        }

        const firstFilterIcon = document.createElement('img');
        firstFilterIcon.src = 'assets/filters-active-ic.svg';
        firstFilterIcon.className = 'filter-icon';
        firstFilterIcon.alt = 'Filter';

        const firstInput = document.createElement('input');
        firstInput.type = 'text';
        firstInput.className = 'filter-input-with-icon';
        firstInput.placeholder = 'Filter..';

        firstFilterWrapper.appendChild(firstFilterIcon);
        firstFilterWrapper.appendChild(firstInput);

        // Restore first filter state if it exists
        if (existingFilter && existingFilter.value) {
            firstInput.value = existingFilter.value;
        }

        // AND/OR Logic toggles (initially hidden, always hidden for checkbox-only columns)
        const logicSection = document.createElement('div');
        logicSection.className = 'filter-logic-section';
        logicSection.style.display = 'none';

        // Second dropdown (initially hidden, always hidden for checkbox-only columns)
        const secondDropdown = this.createCustomDropdown(
            filterOptions,
            defaultOperator
        );
        secondDropdown.classList.add('filter-type-dropdown');
        secondDropdown.style.display = 'none';

        // Restore first dropdown state if it exists
        if (existingFilter && existingFilter.operator) {
            let operatorToRestore = existingFilter.operator;

            if (isDateOnly) {
                const hasRangeBounds = existingFilter.operator === 'greaterThanOrEqual' &&
                    existingFilter.secondOperator === 'lessThanOrEqual' &&
                    (existingFilter.logicOperator || '').toUpperCase() === 'AND';

                if (hasRangeBounds) {
                    operatorToRestore = 'inRange';
                } else if (['equals', 'notEquals', 'lessThan', 'greaterThan'].includes(existingFilter.operator)) {
                    operatorToRestore = existingFilter.operator;
                } else if (DATE_PRESET_OPERATORS.has(existingFilter.operator)) {
                    operatorToRestore = existingFilter.operator;
                } else if (['greaterThanOrEqual', 'lessThanOrEqual'].includes(existingFilter.operator)) {
                    // Map inclusive comparisons back to their dropdown equivalents
                    operatorToRestore = existingFilter.operator === 'greaterThanOrEqual' ? 'greaterThan' : 'lessThan';
                }
            }

            normalizedDateOperator = isDateOnly ? operatorToRestore : null;
            this.setCustomDropdownValue(firstDropdown, operatorToRestore);
        }

        // Restore second dropdown state if it exists
        if (existingFilter && existingFilter.secondOperator) {
            this.setCustomDropdownValue(secondDropdown, existingFilter.secondOperator);
        }

        // Second filter input (initially hidden, always hidden for checkbox-only columns)
        const secondFilterWrapper = document.createElement('div');
        secondFilterWrapper.className = 'filter-input-wrapper';
        secondFilterWrapper.style.display = 'none';

        const secondFilterIcon = document.createElement('img');
        secondFilterIcon.src = 'assets/filters-active-ic.svg';
        secondFilterIcon.className = 'filter-icon';
        secondFilterIcon.alt = 'Filter';

        const secondInput = document.createElement('input');
        secondInput.type = 'text';
        secondInput.className = 'filter-input-with-icon';
        secondInput.placeholder = 'Filter..';

        secondFilterWrapper.appendChild(secondFilterIcon);
        secondFilterWrapper.appendChild(secondInput);

        // Restore second filter state if it exists
        if (existingFilter && existingFilter.secondValue) {
            secondInput.value = existingFilter.secondValue;
        }

        // Create separate dedicated inputs for inRange mode
        const rangeFromWrapper = document.createElement('div');
        rangeFromWrapper.className = 'filter-input-wrapper range-input-wrapper';
        rangeFromWrapper.style.display = 'none';

        const rangeFromIcon = document.createElement('img');
        rangeFromIcon.src = 'assets/filters-active-ic.svg';
        rangeFromIcon.className = 'filter-icon';
        rangeFromIcon.alt = 'Filter';

        const rangeFromInput = document.createElement('input');
        rangeFromInput.type = 'text';
        rangeFromInput.className = 'filter-input-with-icon';
        rangeFromInput.placeholder = 'From (min value)';

        rangeFromWrapper.appendChild(rangeFromIcon);
        rangeFromWrapper.appendChild(rangeFromInput);

        const rangeToWrapper = document.createElement('div');
        rangeToWrapper.className = 'filter-input-wrapper range-input-wrapper';
        rangeToWrapper.style.display = 'none';

        const rangeToIcon = document.createElement('img');
        rangeToIcon.src = 'assets/filters-active-ic.svg';
        rangeToIcon.className = 'filter-icon';
        rangeToIcon.alt = 'Filter';

        const rangeToInput = document.createElement('input');
        rangeToInput.type = 'text';
        rangeToInput.className = 'filter-input-with-icon';
        rangeToInput.placeholder = 'To (max value)';

        rangeToWrapper.appendChild(rangeToIcon);
        rangeToWrapper.appendChild(rangeToInput);

        // Show second filter and logic section if there's existing data
        if (existingFilter && (existingFilter.secondValue || existingFilter.secondOperator)) {
            logicSection.style.display = 'flex';
            secondDropdown.style.display = 'block';
            secondFilterWrapper.style.display = 'flex';
        }

        // Handle inRange restoration for existing filters
        if (existingFilter && existingFilter.operator === 'inRange' && existingFilter.value && typeof existingFilter.value === 'object') {
            rangeFromInput.value = existingFilter.value.fromValue || '';
            rangeToInput.value = existingFilter.value.toValue || '';
        }

        // Define applyFilter function here so it can be used by createLogicToggle
        applyFilter = () => {
            const firstOperator = this.getCustomDropdownValue(firstDropdown);
            console.log('🎯 Applying filter with operator:', firstOperator, 'for field:', column.field);

            if (columnType === 'date') {
                // For date-only columns, handle dropdown selection
                if (firstOperator === 'inRange') {
                    // Date range: use From/To date inputs (exact copy from old grid)
                    const fromDate = firstInput.value;
                    const toDate = secondInput.value;

                    if (fromDate || toDate) {
                        // Re-express inRange as two single-bound conditions that we know work well
                        const filterType = this.getFilterType(column);
                        let filterConfig;
                        if (fromDate && toDate) {
                            filterConfig = {
                                type: filterType,
                                operator: 'greaterThanOrEqual',
                                value: fromDate,
                                secondOperator: 'lessThanOrEqual',
                                secondValue: toDate,
                                logicOperator: 'AND'
                            };
                        } else if (fromDate) {
                            filterConfig = {
                                type: filterType,
                                operator: 'greaterThanOrEqual',
                                value: fromDate
                            };
                        } else {
                            filterConfig = {
                                type: filterType,
                                operator: 'lessThanOrEqual',
                                value: toDate
                            };
                        }

                        console.log('📅 Applying date range via bounds:', filterConfig);
                        this.setFilter(column.field, filterConfig, 'contains');

                        // Debug: Check if filter was actually set
                        const appliedFilter = this.filterState[column.field];
                        console.log('📅 Filter after setting:', appliedFilter);
                    } else {
                        console.log('📅 Clearing date range filter - no dates provided');
                        this.clearFilter(column.field);
                    }
                } else if (['equals', 'notEquals', 'lessThan', 'greaterThan'].includes(firstOperator)) {
                    // Date comparison operators: use single date input
                    const dateValue = firstInput.value;

                    if (dateValue) {
                        this.setFilter(column.field, {
                            type: this.getFilterType(column),
                            operator: firstOperator,
                            value: dateValue
                        }, 'contains');
                    } else {
                        this.clearFilter(column.field);
                    }
                } else if (firstOperator && firstOperator !== 'showAll') {
                    // Predefined date range (Today, Yesterday, etc.)
                    this.setFilter(column.field, {
                        type: this.getFilterType(column),
                        operator: firstOperator,
                        value: null // No input value needed for preset date ranges
                    }, 'contains');
                } else {
                    // Clear filter for "Show All" (showAll) or empty operator
                    if (firstInput) firstInput.value = '';
                    if (secondInput) secondInput.value = '';
                    console.log('🧹 Clearing filter for field:', column.field, 'operator:', firstOperator);
                    console.log('🧹 Filter state before clear:', JSON.stringify(this.filterState[column.field]));
                    this.clearFilter(column.field);
                    console.log('🧹 Filter state after clear:', JSON.stringify(this.filterState[column.field]));
                }
                return;
            }

            // For other column types, use the existing logic
            // Use dedicated range inputs for inRange mode, regular inputs otherwise
            let firstValue, secondValue;
            if (firstOperator === 'inRange' && (columnType === 'numeric' || columnType === 'percentage')) {
                firstValue = rangeFromInput.value;
                secondValue = rangeToInput.value;
            } else {
                firstValue = firstInput.value;
                secondValue = secondInput.value;
            }
            const secondOperator = this.getCustomDropdownValue(secondDropdown);

            // Get logic operator (AND/OR)
            const andToggle = logicSection.querySelector('[data-logic="AND"] .logic-checkbox');
            const orToggle = logicSection.querySelector('[data-logic="OR"] .logic-checkbox');

            // Check which toggle is actually checked
            const isAndChecked = andToggle && andToggle.src.includes('checkbox-ic.svg');
            const isOrChecked = orToggle && orToggle.src.includes('checkbox-ic.svg');

            // Default to AND if neither is checked
            const logicOperator = isOrChecked ? 'OR' : 'AND';

            // Validation logic matching old grid
            const hasValidFirstFilter = firstOperator && firstOperator !== 'showAll' &&
                (firstValue.length > 0);
            const hasValidSecondFilter = secondOperator && secondOperator !== 'showAll' &&
                (secondValue.length > 0);

            let shouldApplyFilter = false;
            let filterConfig = {
                type: this.getFilterType(column),
                operator: firstOperator,
                value: firstValue,
                secondOperator: null,
                secondValue: null,
                logicOperator: null
            };

            // SPECIAL CASE: Numeric "In range" should IGNORE second operator and logic (exact copy from old grid)
            if (firstOperator === 'inRange' && (columnType === 'numeric' || columnType === 'percentage')) {
                filterConfig.value = { fromValue: firstValue, toValue: secondValue };
                filterConfig.secondOperator = null;
                filterConfig.secondValue = null;
                filterConfig.logicOperator = null;

                shouldApplyFilter = (firstValue.length > 0 || secondValue.length > 0);

                console.log('🔢 Range filter config (numeric-only):', filterConfig.value);
            } else {
                // Combined columns: either text filter can trigger
                if (hasValidFirstFilter) {
                    shouldApplyFilter = true;
                    console.log('✅ First text filter active');
                }

                if (hasValidSecondFilter) {
                    shouldApplyFilter = true;
                    filterConfig.secondOperator = secondOperator;
                    filterConfig.secondValue = secondValue;
                    filterConfig.logicOperator = logicOperator;
                    console.log('✅ Second text filter active');
                }
            }

            if (shouldApplyFilter) {
                console.log('🎯 Applying filter:', filterConfig);
                this.setFilter(column.field, filterConfig, 'contains');
            } else {
                console.log('🗑️ Clearing filter - no valid conditions');
                this.clearFilter(column.field);
            }

            // Immediate checkbox regeneration like the old grid
            // This ensures checkbox lists show only values available in current filtered data
            this.regenerateAllCheckboxLists(column.field);
        };

        // Create AND/OR toggles after all inputs and applyFilter are defined
        const andToggle = this.createLogicToggle('AND', true, logicSection, applyFilter, firstInput, secondInput, firstDropdown, secondDropdown);
        const orToggle = this.createLogicToggle('OR', false, logicSection, applyFilter, firstInput, secondInput, firstDropdown, secondDropdown);
        const separator = document.createElement('span');
        separator.textContent = '/';
        separator.className = 'logic-separator';

        logicSection.appendChild(andToggle);
        logicSection.appendChild(separator);
        logicSection.appendChild(orToggle);

        // Restore logic operator state if it exists
        if (existingFilter && existingFilter.logicOperator) {
            const andCheckbox = andToggle.querySelector('.logic-checkbox');
            const orCheckbox = orToggle.querySelector('.logic-checkbox');

            if (existingFilter.logicOperator === 'OR') {
                andCheckbox.src = 'assets/uncheckedbox-ic.svg';
                andCheckbox.alt = 'Unchecked';
                orCheckbox.src = 'assets/checkbox-ic.svg';
                orCheckbox.alt = 'Checked';
            } else {
                andCheckbox.src = 'assets/checkbox-ic.svg';
                andCheckbox.alt = 'Checked';
                orCheckbox.src = 'assets/uncheckedbox-ic.svg';
                orCheckbox.alt = 'Unchecked';
            }
        }

        // Divider
        const divider = document.createElement('div');
        divider.className = 'filter-divider';

        // Search input with icon - need wrapper for proper positioning
        const searchWrapper = document.createElement('div');
        searchWrapper.className = 'search-input-wrapper';

        const searchIcon = document.createElement('img');
        searchIcon.src = 'assets/search-ic.svg';
        searchIcon.className = 'search-icon';
        searchIcon.alt = 'Search';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'search-input';
        searchInput.placeholder = 'Search..';

        searchWrapper.appendChild(searchIcon);
        searchWrapper.appendChild(searchInput);

        // Checkbox list container
        const checkboxList = document.createElement('div');
        checkboxList.className = 'filter-checkbox-list';

        // Assemble panel components based on column type (matching old version logic)
        if (columnType === 'text-special') {
            // text-special: Only search input and checkbox list
            panel.appendChild(searchWrapper);
            panel.appendChild(checkboxList);

            // Get unique values and create checkbox list (async for performance)
            let regenerateCheckboxList = () => {};
            this.createCheckboxList(column, checkboxList, searchInput).then(fn => {
                regenerateCheckboxList = fn;
            }).catch(error => {
                console.error('Error creating checkbox list:', error);
            });
        } else if (columnType === 'fixed') {
            // fixed: No filter components at all
            const noFilterMessage = document.createElement('div');
            noFilterMessage.className = 'no-filter-message';
            noFilterMessage.textContent = 'No filtering available for this column';
            panel.appendChild(noFilterMessage);
        } else if (columnType === 'date') {
            // date: Dropdown + conditional inputs (no checkbox list)
            panel.appendChild(firstDropdown);
            panel.appendChild(firstFilterWrapper);
            panel.appendChild(secondFilterWrapper);

            // Match old-grid behavior: show date inputs based on selected operator
            const updateDateUIForOperator = (op) => {
                if (op === 'inRange') {
                    firstFilterWrapper.style.display = 'flex';
                    secondFilterWrapper.style.display = 'flex';
                    firstInput.type = 'date';
                    secondInput.type = 'date';
                    firstInput.placeholder = 'From date';
                    secondInput.placeholder = 'To date';
                } else if (['equals','notEquals','lessThan','greaterThan'].includes(op)) {
                    firstFilterWrapper.style.display = 'flex';
                    secondFilterWrapper.style.display = 'none';
                    firstInput.type = 'date';
                    firstInput.placeholder = 'Select date';
                } else {
                    // Presets or Please Select
                    firstFilterWrapper.style.display = 'none';
                    secondFilterWrapper.style.display = 'none';
                }
            };

            const currentOp = normalizedDateOperator || this.getCustomDropdownValue(firstDropdown);
            if (normalizedDateOperator) {
                if (normalizedDateOperator === 'inRange') {
                    const fromValue = typeof existingFilter?.value === 'object' ? existingFilter?.value?.fromValue : existingFilter?.value;
                    const toValue = typeof existingFilter?.value === 'object' ? existingFilter?.value?.toValue : existingFilter?.secondValue;
                    firstInput.value = fromValue || existingFilter?.value || '';
                    secondInput.value = toValue || existingFilter?.secondValue || '';
                } else if (['equals','notEquals','lessThan','greaterThan'].includes(normalizedDateOperator)) {
                    firstInput.value = existingFilter?.value || '';
                } else if (DATE_PRESET_OPERATORS.has(normalizedDateOperator)) {
                    firstInput.value = '';
                    secondInput.value = '';
                }

                updateDateUIForOperator(normalizedDateOperator);
            } else {
                updateDateUIForOperator(currentOp);
            }

            // Helper: ensure custom datepicker library is loaded once (old-grid approach)
            const ensureDatepickerLibLoaded = async () => {
                if (window.SnapDatepicker) return true;
                const loadScript = (src) => new Promise((resolve, reject) => {
                    const s = document.createElement('script');
                    s.src = src;
                    s.onload = resolve;
                    s.onerror = reject;
                    document.body.appendChild(s);
                });
                const loadCSS = (href) => {
                    if (document.querySelector(`link[href*="${href}"]`)) return;
                    const l = document.createElement('link');
                    l.rel = 'stylesheet';
                    l.href = href;
                    document.head.appendChild(l);
                };
                const tryLoad = async () => {
                    // Try root-relative, then relative from this page, then parent-relative (for test pages)
                    const cssPaths = ['/components/datepicker/snap-datepicker.css', 'components/datepicker/snap-datepicker.css', '../datepicker/snap-datepicker.css'];
                    const jsPaths = ['/components/datepicker/snap-datepicker.js', 'components/datepicker/snap-datepicker.js', '../datepicker/snap-datepicker.js'];
                    cssPaths.forEach(loadCSS);
                    for (const jsPath of jsPaths) {
                        try {
                            if (!window.SnapDatepicker) await loadScript(jsPath);
                            if (window.SnapDatepicker) return true;
                        } catch (e) {
                            // try next path
                        }
                    }
                    return !!window.SnapDatepicker;
                };
                try {
                    const ok = await tryLoad();
                    if (!ok) throw new Error('SnapDatepicker not available');
                    return true;
                } catch (e) {
                    console.warn('Failed to load SnapDatepicker', e);
                    return false;
                }
            };

            // Setup click interception for custom datepicker (old-grid approach)
            const setupDatepickerInterception = async () => {
                const ok = await ensureDatepickerLibLoaded();
                if (!ok) return;

                // Add click listener to panel to intercept date input calendar icon clicks
                if (!panel._datepickerInterceptionBound) {
                    panel.addEventListener('click', (e) => {
                        const dateInput = e.target;
                        if (dateInput.type === 'date' && dateInput.closest('.snap-grid')) {
                            // Only intercept if click is on the calendar icon area (right side of input)
                            const rect = dateInput.getBoundingClientRect();
                            const clickX = e.clientX;
                            const iconAreaStart = rect.right - 30; // Calendar icon is usually in last 30px

                            if (clickX >= iconAreaStart) {
                                e.preventDefault();
                                e.stopPropagation();

                                // Create and show custom datepicker
                                this.openCustomDatepicker(dateInput, e);
                            }
                        }
                    }, true);
                    panel._datepickerInterceptionBound = true;
                }
            };

            // Update UI on operator change; apply immediately only for presets to avoid menu jump
            firstDropdown.addEventListener('change', async (e) => {
                const op = (e && e.detail && e.detail.value) ? e.detail.value : this.getCustomDropdownValue(firstDropdown);
                updateDateUIForOperator(op);
                // For presets, apply immediately; for inputs, just show inputs and setup interception
                const presetOps = ['today','yesterday','last7Days','last30Days','last90Days','last6Months','thisWeek','lastWeek','thisMonth','lastMonth','thisYear','lastYear','currentMonth','currentYear'];
                if (presetOps.includes(op) || op === 'showAll') {
                    // Suppress menu reposition for preset filters to prevent menu jumping
                    console.log('🎯 Setting menu reposition suppression for preset:', op);
                    this.suppressMenuReposition = true;
                    applyFilter();
                    // Clear suppression after a short delay to allow filter to complete
                    setTimeout(() => {
                        this.suppressMenuReposition = false;
                        console.log('✅ Menu reposition suppression cleared');
                    }, 100);
                } else {
                    // Setup click interception for custom datepicker
                    await setupDatepickerInterception();

                    // If there are existing date values, apply filter immediately
                    const hasFirstDate = firstInput.value && firstInput.value.trim() !== '';
                    const hasSecondDate = secondInput.value && secondInput.value.trim() !== '';

                    if (hasFirstDate || hasSecondDate) {
                        console.log('🎯 Dropdown changed with existing dates, applying filter:', {
                            operator: op,
                            firstDate: firstInput.value,
                            secondDate: secondInput.value
                        });
                        // Suppress menu reposition when applying filter from dropdown change
                        this.suppressMenuReposition = true;
                        applyFilter();
                        setTimeout(() => {
                            this.suppressMenuReposition = false;
                        }, 100);
                    }
                }
            });

            // Apply on date input changes and Enter key (native date inputs)
            const applyOnChange = () => {
                // Suppress menu reposition when applying filter from date input
                this.suppressMenuReposition = true;
                applyFilter();
                setTimeout(() => {
                    this.suppressMenuReposition = false;
                }, 100);
            };
            firstInput.addEventListener('change', applyOnChange);
            secondInput.addEventListener('change', applyOnChange);
            firstInput.addEventListener('keydown', (e) => { if (e.key === 'Enter') applyOnChange(); });
            secondInput.addEventListener('keydown', (e) => { if (e.key === 'Enter') applyOnChange(); });

            // Initial setup of datepicker interception if needed
            (async () => {
                const op0 = normalizedDateOperator || existingFilter?.operator || currentOp;
                const needsInput = op0 === 'inRange' || ['equals','notEquals','lessThan','greaterThan'].includes(op0);
                if (needsInput) {
                    await setupDatepickerInterception();
                }
            })();
        } else {
            // text, numeric, text-numeric: Full filter UI
            panel.appendChild(firstDropdown);
            panel.appendChild(firstFilterWrapper);
            panel.appendChild(rangeFromWrapper);  // Add range inputs
            panel.appendChild(rangeToWrapper);
            panel.appendChild(logicSection);
            panel.appendChild(secondDropdown);
            panel.appendChild(secondFilterWrapper);

            // Declare regenerateCheckboxList function for use by filter inputs
            let regenerateCheckboxList = () => {};

            // Only add divider, search input and checkbox list for non-numeric columns
            // Skip for numeric, percentage, currency, and text-numeric columns
            if (!isNumericOnly && columnType !== 'numeric' && columnType !== 'text-numeric') {
                panel.appendChild(divider);
                panel.appendChild(searchWrapper);
                panel.appendChild(checkboxList);

                // Get unique values and create checkbox list
                this.createCheckboxList(column, checkboxList, searchInput).then(fn => { regenerateCheckboxList = fn; }).catch(err => console.error('Error creating checkbox list:', err));
            }

            // Function to toggle second filter visibility (with separate range inputs)
            const toggleSecondFilter = (show) => {
                if (isTextSpecial || isDateOnly || isFixed) return; // Don't show second filter for special column types

                const currentOperator = this.getCustomDropdownValue(firstDropdown);

                if (currentOperator === 'inRange' && isNumericOnly) {
                    // InRange mode: show dedicated range inputs, hide everything else
                    firstFilterWrapper.style.display = 'none';
                    rangeFromWrapper.style.display = 'flex';
                    rangeToWrapper.style.display = 'flex';
                    logicSection.style.display = 'none';
                    secondDropdown.style.display = 'none';
                    secondFilterWrapper.style.display = 'none';
                } else {
                    // Regular mode: hide range inputs, show regular inputs
                    firstFilterWrapper.style.display = 'flex';
                    rangeFromWrapper.style.display = 'none';
                    rangeToWrapper.style.display = 'none';

                    // Show logic section and second filter when there's a valid first filter
                    if (show && firstInput.value.length > 0) {
                        logicSection.style.display = 'flex';
                        secondDropdown.style.display = 'block';
                        secondFilterWrapper.style.display = 'flex';
                    } else if (!show) {
                        logicSection.style.display = 'none';
                        secondDropdown.style.display = 'none';
                        secondFilterWrapper.style.display = 'none';
                        secondInput.value = '';
                    }
                }
            };

            // Handle dropdown changes for special cases (consolidated)
            firstDropdown.addEventListener('change', async (e) => {
                const operator = e.detail.value;

                // Handle UI changes for all operators using toggleSecondFilter
                if (isNumericOnly && operator === 'inRange') {
                    toggleSecondFilter(true); // Show range inputs
                    applyFilter();
                    return;
                }

                if (isDateOnly) {
                    const needsInput = operator === 'inRange' || ['equals','notEquals','lessThan','greaterThan'].includes(operator);
                    const isPreset = !needsInput && operator !== 'showAll';

                    if (operator === 'inRange') {
                        logicSection.style.display = 'none';
                        secondDropdown.style.display = 'none';
                        firstFilterWrapper.style.display = 'flex';
                        secondFilterWrapper.style.display = 'flex';
                        firstInput.type = 'date';
                        secondInput.type = 'date';
                        firstInput.placeholder = 'From date';
                        secondInput.placeholder = 'To date';
                    } else if (needsInput) {
                        firstFilterWrapper.style.display = 'flex';
                        secondFilterWrapper.style.display = 'none';
                        logicSection.style.display = 'none';
                        secondDropdown.style.display = 'none';
                        firstInput.type = 'date';
                    } else {
                        // Presets or showAll: hide inputs
                        firstFilterWrapper.style.display = 'none';
                        secondFilterWrapper.style.display = 'none';
                        logicSection.style.display = 'none';
                        secondDropdown.style.display = 'none';
                    }

                    // Setup custom datepicker interception; only apply for presets to avoid menu jump
                    if (needsInput) {
                        if (typeof setupDatepickerInterception === 'function') {
                            await setupDatepickerInterception();
                        }
                        // Do not call applyFilter here; wait for user selection
                        return;
                    }

                    // For preset operators, suppress menu reposition to prevent jumping
                    this.suppressMenuReposition = true;
                    setTimeout(() => {
                        this.suppressMenuReposition = false;
                    }, 100);

                    // For presets or clearing
                    applyFilter();
                    return;
                }

                // Non-date columns: default behavior
                // Use toggleSecondFilter to properly handle UI state for all operators
                const hasValue = firstInput.value.length > 0;
                toggleSecondFilter(hasValue);
                applyFilter();
            });

            // Add event listeners for range inputs
            rangeFromInput.addEventListener('input', () => {
                applyFilter();
            });

            rangeToInput.addEventListener('input', () => {
                applyFilter();
            });

            rangeFromInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    applyFilter();
                }
            });

            rangeToInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    applyFilter();
                }
            });

            // Show second filter when first filter has content (for non-date columns)
            if (!isDateOnly) {
                firstInput.addEventListener('input', (e) => {
                    const hasValue = e.target.value.length > 0;
                    toggleSecondFilter(hasValue);
                });
            }

            // Initialize UI state based on existing filter
            if (existingFilter && existingFilter.operator === 'inRange') {
                toggleSecondFilter(true);
            }

            // Apply filter on Enter key (immediate)
            firstInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    applyFilter(); // Apply immediately on Enter
                }
            });

            secondInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    applyFilter(); // Apply immediately on Enter
                }
            });

            // Apply filter with debouncing for large datasets performance
            let filterTimeout;
            const debouncedApplyFilter = () => {
                clearTimeout(filterTimeout);
                filterTimeout = setTimeout(() => {
                    applyFilter();
                    // Regenerate current checkbox list to reflect filtered data (like old grid)
                    if (typeof regenerateCheckboxList === 'function') {
                        setTimeout(() => regenerateCheckboxList(), 100);
                    }
                }, 300); // 300ms debounce for performance
            };

            firstInput.addEventListener('input', debouncedApplyFilter);
            secondInput.addEventListener('input', debouncedApplyFilter);

            // Add event listeners for dropdown changes to trigger filter application
            if (columnType !== 'date') {
                firstDropdown.addEventListener('change', debouncedApplyFilter);
            }
            secondDropdown.addEventListener('change', debouncedApplyFilter);
        }

        return panel;
    }

    /**
     * Open custom datepicker for date input (old-grid approach)
     */
    openCustomDatepicker(dateInput, event) {
        // Remove any existing datepicker
        const existingPicker = document.querySelector('.snap-datepicker-popup');
        if (existingPicker) {
            existingPicker.remove();
        }

        // Create wrapper for the datepicker (no border since dropdown has its own)
        const wrapper = document.createElement('div');
        wrapper.className = 'snap-datepicker-popup';
        wrapper.style.cssText = 'position: fixed; z-index: 10000; background: transparent;';

        // Position near the input
        const rect = dateInput.getBoundingClientRect();
        wrapper.style.left = rect.left + 'px';
        wrapper.style.top = (rect.bottom + 4) + 'px';

        document.body.appendChild(wrapper);

        // Create the datepicker with calendar-only mode and year range
        const currentYear = new Date().getFullYear();
        const datepicker = new window.SnapDatepicker(wrapper, {
            value: dateInput.value,
            placeholder: dateInput.placeholder || 'Select date',
            closeOnSelect: true,
            yearRange: [2015, currentYear], // Limit years from 2015 to current year
            yearOrder: 'desc' // Show newest years first
        });

        // Hide the input part and calendar icon, show only the calendar
        setTimeout(() => {
            // Hide the input field
            const pickerInput = wrapper.querySelector('.snap-datepicker-input');
            if (pickerInput) {
                pickerInput.style.display = 'none';
            }

            // Hide the calendar icon
            const pickerIcon = wrapper.querySelector('.snap-datepicker-icon');
            if (pickerIcon) {
                pickerIcon.style.display = 'none';
            }

            // Show only the calendar dropdown
            const dropdown = wrapper.querySelector('.snap-datepicker-dropdown');
            if (dropdown) {
                dropdown.style.display = 'block';
                dropdown.style.position = 'static';
                dropdown.style.boxShadow = 'none';
                dropdown.style.border = 'none';
            }
        }, 10);

        // Update the original input when date is selected
        datepicker.on('change', (value) => {
            dateInput.value = value;
            // Trigger change event on the original input
            const changeEvent = new Event('change', { bubbles: true });
            dateInput.dispatchEvent(changeEvent);

            // Remove the popup
            wrapper.remove();
        });

        // Close on outside click (but not on menu clicks) and on month/year dropdown changes
        setTimeout(() => {
            document.addEventListener('click', function closeOnOutside(e) {
                if (!wrapper.contains(e.target) && !e.target.closest('.snap-grid-column-menu')) {
                    wrapper.remove();
                    document.removeEventListener('click', closeOnOutside);
                }
            });

            // Also close when month/year dropdowns change (they close the menu but leave datepicker open)
            const monthDropdown = wrapper.querySelector('.snap-datepicker-month-select');
            const yearDropdown = wrapper.querySelector('.snap-datepicker-year-select');

            if (monthDropdown) {
                monthDropdown.addEventListener('change', () => {
                    // Check if the column menu is still visible after a short delay
                    setTimeout(() => {
                        const menu = document.querySelector('.snap-grid-column-menu');
                        if (!menu || menu.style.display === 'none') {
                            wrapper.remove();
                        }
                    }, 50);
                });
            }

            if (yearDropdown) {
                yearDropdown.addEventListener('change', () => {
                    // Check if the column menu is still visible after a short delay
                    setTimeout(() => {
                        const menu = document.querySelector('.snap-grid-column-menu');
                        if (!menu || menu.style.display === 'none') {
                            wrapper.remove();
                        }
                    }, 50);
                });
            }
        }, 100);

        // Force open the calendar
        datepicker.open();
    }

    /**
     * Create column management tab content
     */
    createManagementTab(column) {
        const panel = document.createElement('div');
        panel.className = 'menu-tab-panel';
        panel.setAttribute('data-tab-panel', 'management');

        // Sort Ascending option
        const sortAscOption = this.createMenuOptionWithIcon('Sort Ascending', 'assets/ascending-ic.svg', () => {
            this.sortColumn(column.field, 'asc');
        });
        panel.appendChild(sortAscOption);

        // Sort Descending option
        const sortDescOption = this.createMenuOptionWithIcon('Sort Descending', 'assets/descending-ic.svg', () => {
            this.sortColumn(column.field, 'desc');
        });
        panel.appendChild(sortDescOption);

        // First divider
        const divider1 = document.createElement('div');
        divider1.className = 'column-management-divider';
        panel.appendChild(divider1);

        // Pin Column option with submenu
        const pinContainer = document.createElement('div');
        pinContainer.className = 'pin-column-container';

        const pinOption = document.createElement('div');
        pinOption.className = 'menu-option pin-column-option';

        const pinMain = document.createElement('div');
        pinMain.className = 'pin-column-main';

        const pinIcon = document.createElement('img');
        pinIcon.src = 'assets/pin-col-ic.svg';
        pinIcon.alt = 'Pin';
        pinIcon.className = 'menu-option-icon';

        const pinText = document.createElement('span');
        pinText.textContent = 'Pin Column';

        const pinArrow = document.createElement('img');
        pinArrow.src = 'assets/arrow-ic.svg';
        pinArrow.alt = 'Arrow';
        pinArrow.className = 'pin-arrow';

        pinMain.appendChild(pinIcon);
        pinMain.appendChild(pinText);
        pinOption.appendChild(pinMain);
        pinOption.appendChild(pinArrow);

        // Pin submenu
        const pinSubmenu = document.createElement('div');
        pinSubmenu.className = 'pin-submenu hidden';

        const currentPinState = this.getColumnPinState(column.field);

        // Pin Left option
        const pinLeftOption = document.createElement('div');
        pinLeftOption.className = 'pin-option';
        const pinLeftCheck = document.createElement('img');
        pinLeftCheck.src = 'assets/checked-option-ic.svg';
        pinLeftCheck.className = `check-icon ${currentPinState === 'left' ? '' : 'hidden'}`;
        const pinLeftText = document.createElement('span');
        pinLeftText.textContent = 'Pin Left';
        pinLeftOption.appendChild(pinLeftCheck);
        pinLeftOption.appendChild(pinLeftText);
        pinLeftOption.addEventListener('click', () => {
            this.pinColumn(column.field, 'left');
            this.updatePinSubmenu(column.field, pinSubmenu);
        });

        // Pin Right option
        const pinRightOption = document.createElement('div');
        pinRightOption.className = 'pin-option';
        const pinRightCheck = document.createElement('img');
        pinRightCheck.src = 'assets/checked-option-ic.svg';
        pinRightCheck.className = `check-icon ${currentPinState === 'right' ? '' : 'hidden'}`;
        const pinRightText = document.createElement('span');
        pinRightText.textContent = 'Pin Right';
        pinRightOption.appendChild(pinRightCheck);
        pinRightOption.appendChild(pinRightText);
        pinRightOption.addEventListener('click', () => {
            this.pinColumn(column.field, 'right');
            this.updatePinSubmenu(column.field, pinSubmenu);
        });

        // Don't Pin option
        const dontPinOption = document.createElement('div');
        dontPinOption.className = 'pin-option';
        const dontPinCheck = document.createElement('img');
        dontPinCheck.src = 'assets/checked-option-ic.svg';
        dontPinCheck.className = `check-icon ${currentPinState === 'none' ? '' : 'hidden'}`;
        const dontPinText = document.createElement('span');
        dontPinText.textContent = "Don't Pin";
        dontPinOption.appendChild(dontPinCheck);
        dontPinOption.appendChild(dontPinText);
        dontPinOption.addEventListener('click', () => {
            this.unpinColumn(column.field);
            this.updatePinSubmenu(column.field, pinSubmenu);
        });

        pinSubmenu.appendChild(pinLeftOption);
        pinSubmenu.appendChild(pinRightOption);
        pinSubmenu.appendChild(dontPinOption);

        // Pin option hover/click behavior
        pinMain.addEventListener('mouseenter', () => {
            pinSubmenu.classList.remove('hidden');
        });
        pinContainer.addEventListener('mouseleave', () => {
            pinSubmenu.classList.add('hidden');
        });

        pinContainer.appendChild(pinOption);
        pinContainer.appendChild(pinSubmenu);
        panel.appendChild(pinContainer);

        // Second divider
        const divider2 = document.createElement('div');
        divider2.className = 'column-management-divider';
        panel.appendChild(divider2);

        // Autosize This Column option
        const autosizeThisOption = this.createMenuOptionWithIcon('Autosize This Column', 'assets/autoresize-ic.svg', () => {
            this.autosizeColumn(column.field);
        });
        panel.appendChild(autosizeThisOption);

        // Autosize All Columns option
        const autosizeAllOption = this.createMenuOptionWithIcon('Autosize All Columns', 'assets/autoresize-ic.svg', () => {
            this.autosizeAllColumns();
        });
        panel.appendChild(autosizeAllOption);

        // Third divider
        const divider3 = document.createElement('div');
        divider3.className = 'column-management-divider';
        panel.appendChild(divider3);

        // Reset Columns option (no icon)
        const resetOption = document.createElement('div');
        resetOption.className = 'menu-option reset-option';
        const resetText = document.createElement('span');
        resetText.textContent = 'Reset Columns';
        resetOption.appendChild(resetText);
        resetOption.addEventListener('click', () => {
            this.resetColumns();
        });
        panel.appendChild(resetOption);

        return panel;
    }

    /**
     * Sort column
     */
    sortColumn(field, direction) {
        // Clear existing sort state
        this.sortState = {};

        // Set new sort state
        this.sortState[field] = direction;

        // Process and render data
        this.processData();
        this.render();
    }

    /**
     * Create column visibility tab content
     */
    createVisibilityTab() {
        const panel = document.createElement('div');
        panel.className = 'menu-tab-panel';
        panel.setAttribute('data-tab-panel', 'visibility');

        // Search input with icon (matching Filter tab style) - need wrapper for proper positioning
        const searchWrapper = document.createElement('div');
        searchWrapper.className = 'search-input-wrapper';

        const searchIcon = document.createElement('img');
        searchIcon.src = './assets/search-ic.svg';
        searchIcon.className = 'search-icon';
        searchIcon.alt = 'Search';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'search-input';
        searchInput.placeholder = 'Search..';

        searchWrapper.appendChild(searchIcon);
        searchWrapper.appendChild(searchInput);

        // Add stopPropagation to search input
        searchInput.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        searchInput.addEventListener('keydown', (e) => {
            e.stopPropagation();
        });

        // Column list container
        const columnList = document.createElement('div');
        columnList.className = 'column-list';

        // Add drag and drop support to the column list container
        columnList.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.dataTransfer.dropEffect = 'move';

            // Update drag indicator position based on mouse position
            const dragIndicator = columnList.querySelector('.drag-indicator');
            if (dragIndicator) {
                const afterElement = this.getDragAfterElement(columnList, e.clientY);
                this.updateDragIndicatorPosition(dragIndicator, columnList, afterElement);
            }
        });

        columnList.addEventListener('drop', (e) => {
            e.preventDefault();
            e.stopPropagation();
            const draggedField = e.dataTransfer.getData('text/plain');

            if (draggedField && this.reorderColumnByIndex) {
                try {
                    // Don't allow dropping fixed columns
                    if (this.isFixedColumn(draggedField)) {
                        return;
                    }

                    // Calculate insertion position based on drop location
                    const afterElement = this.getDragAfterElement(columnList, e.clientY);
                    const validRange = this.getValidInsertionRange();

                    let insertionIndex;
                    if (afterElement == null) {
                        // Insert at the end, but respect valid range
                        insertionIndex = Math.min(validRange.maxIndex, this.processedColumns.length);
                    } else {
                        // Insert before the afterElement
                        const afterField = afterElement.getAttribute('data-field');
                        insertionIndex = this.processedColumns.findIndex(col => col.field === afterField);

                        // Validate that the insertion position is within valid range
                        if (insertionIndex < validRange.minIndex || insertionIndex > validRange.maxIndex) {
                            // Invalid drop position, don't perform the reorder
                            return;
                        }
                    }

                    this.reorderColumnByIndex(draggedField, insertionIndex);
                } catch (error) {
                    console.error('Error during column reordering:', error);
                    // Don't re-throw the error to prevent it from bubbling up
                }
            }

            // Always remove any existing drag indicator after drop
            const di = columnList.querySelector('.drag-indicator');
            if (di) di.remove();
        });

        // Add Select All checkbox as first item in the column list
        const selectAllItem = document.createElement('div');
        selectAllItem.className = 'column-item select-all-item';

        const selectAllCheckboxWrapper = document.createElement('div');
        selectAllCheckboxWrapper.className = 'checkbox-wrapper';

        const selectAllCheckbox = document.createElement('img');
        selectAllCheckbox.className = 'checkbox-icon';
        selectAllCheckbox.draggable = false;
        // Don't set initial src - let updateSelectAllStateInList set the correct state
        selectAllCheckbox.alt = 'Select All';

        const selectAllLabel = document.createElement('label');
        selectAllLabel.textContent = 'Select All';
        selectAllLabel.className = 'column-label';

        selectAllCheckboxWrapper.appendChild(selectAllCheckbox);
        selectAllItem.appendChild(selectAllCheckboxWrapper);
        selectAllItem.appendChild(selectAllLabel);

        // Add Select All functionality
        selectAllItem.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleSelectAllColumnsInList(selectAllCheckbox, columnList);
        });

        // Add Select All as first item in the list
        columnList.appendChild(selectAllItem);

        // Create column items (exclude fixed columns)
        this.processedColumns.forEach((column, index) => {
            if (['checkbox', 'preview', 'actions', 'marketplace'].includes(column.field)) {
                return; // Skip special columns
            }

            const item = this.createVisibilityColumnItem(column);
            item.setAttribute('data-column-index', index);
            columnList.appendChild(item);
        });

        // Initialize Select All state based on current column visibility (after all items are created)
        this.updateSelectAllStateInList(selectAllCheckbox, columnList);

        // Search functionality
        searchInput.addEventListener('input', (e) => {
            e.stopPropagation();
            const searchTerm = e.target.value.toLowerCase();
            const items = columnList.querySelectorAll('.column-item');

            items.forEach(item => {
                const label = item.querySelector('label').textContent.toLowerCase();
                item.style.display = label.includes(searchTerm) ? 'flex' : 'none';
            });
        });

        panel.appendChild(searchWrapper);
        panel.appendChild(columnList);

        return panel;
    }

    /**
     * Create a column item for the visibility tab (matching the original format)
     */
    createVisibilityColumnItem(column) {
        const item = document.createElement('div');
        item.className = 'column-item';
        item.setAttribute('data-field', column.field);

        const isFixed = this.isFixedColumn(column.field);
        item.draggable = !isFixed;
        if (isFixed) item.classList.add('fixed');

        // Create checkbox wrapper using the app's checkbox system
        const checkboxWrapper = document.createElement('div');
        checkboxWrapper.className = 'checkbox-wrapper';

        const checkboxImg = document.createElement('img');
        checkboxImg.className = 'checkbox-icon';
        checkboxImg.draggable = false;
        const isVisible = !column.hide;
        checkboxImg.src = isVisible ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
        checkboxImg.alt = isVisible ? 'Checked' : 'Unchecked';

        // Add grip handle for reordering
        const gripHandle = document.createElement('img');
        gripHandle.src = './assets/grip-ic.svg';
        gripHandle.className = 'grip-handle';
        gripHandle.alt = isFixed ? 'Reordering disabled' : 'Drag to reorder';
        gripHandle.draggable = false;
        if (isFixed) {
            gripHandle.style.opacity = '0.35';
            gripHandle.style.cursor = 'not-allowed';
        }

        const label = document.createElement('label');
        label.textContent = column.headerName || column.field;

        // Toggle functionality
        const toggleColumn = (e) => {
            e.stopPropagation();
            const currentlyVisible = !column.hide;
            if (currentlyVisible) {
                this.setColumnVisible(column.field, false);
                checkboxImg.src = './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = 'Unchecked';
            } else {
                this.setColumnVisible(column.field, true);
                checkboxImg.src = './assets/checkbox-ic.svg';
                checkboxImg.alt = 'Checked';
            }

            // Update Select All state in the visibility tab
            if (this.updateSelectAllStateInVisibilityTab) {
                this.updateSelectAllStateInVisibilityTab();
            }
        };

        // Add click listeners
        checkboxWrapper.addEventListener('click', toggleColumn);
        label.addEventListener('click', toggleColumn);

        // Add drag and drop event listeners only if not fixed
        if (!isFixed && this.addColumnDragListeners) {
            this.addColumnDragListeners(item, column);
        }

        checkboxWrapper.appendChild(checkboxImg);
        item.appendChild(checkboxWrapper);
        item.appendChild(gripHandle);
        item.appendChild(label);

        return item;
    }

    /**
     * Create menu option with icon
     */
    createMenuOptionWithIcon(text, iconSrc, onClick) {
        const option = document.createElement('div');
        option.className = 'menu-option-with-icon';
        option.addEventListener('click', onClick);

        const iconWrapper = document.createElement('div');
        iconWrapper.className = 'menu-option-icon-wrapper';

        const icon = document.createElement('img');
        icon.className = 'menu-option-icon';
        icon.src = iconSrc;
        icon.alt = text;

        const label = document.createElement('span');
        label.className = 'menu-option-label';
        label.textContent = text;

        iconWrapper.appendChild(icon);
        option.appendChild(iconWrapper);
        option.appendChild(label);

        return option;
    }

    /**
     * Setup tab switching functionality
     */
    setupTabSwitching(menu) {
        const tabButtons = menu.querySelectorAll('.menu-tab-btn');

        tabButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.stopPropagation();
                e.preventDefault();
                const tabId = button.getAttribute('data-tab');
                this.setActiveTab(menu, tabId);
            });
        });
    }

    /**
     * Set active tab
     */
    setActiveTab(menu, tabId) {
        // Update tab buttons
        const tabButtons = menu.querySelectorAll('.menu-tab-btn');
        tabButtons.forEach(btn => {
            const btnTabId = btn.getAttribute('data-tab');
            const icon = btn.querySelector('.tab-icon');

            if (btnTabId === tabId) {
                btn.classList.add('active');
                // Update icon to active version
                const iconName = this.getIconNameFromTab(btnTabId);
                icon.src = `assets/${iconName}-active-ic.svg`;
            } else {
                btn.classList.remove('active');
                // Update icon to inactive version
                const iconName = this.getIconNameFromTab(btn.getAttribute('data-tab'));
                icon.src = `assets/${iconName}-inactive-ic.svg`;
            }
        });

        // Update tab panels
        const tabPanels = menu.querySelectorAll('.menu-tab-panel');
        tabPanels.forEach(panel => {
            const panelTabId = panel.getAttribute('data-tab-panel');
            if (panelTabId === tabId) {
                panel.classList.add('active');
            } else {
                panel.classList.remove('active');
            }
        });
    }

    /**
     * Get icon name from tab ID
     */
    getIconNameFromTab(tabId) {
        const iconMap = {
            'filter': 'filters',
            'management': 'column-man',
            'visibility': 'show-hide-col'
        };
        return iconMap[tabId] || 'filters';
    }

    /**
     * Set filter for a column - unified function handling both simple and complex filters
     */
    setFilter(field, filterOrValue, filterType = null) {
        window.SnapLogger?.debug('setFilter called', { field, filterOrValue, filterType });

        // Clear any current selection when filters change (safe reset)
        if (this.selectedRows && this.selectedRows.size > 0) {
            this.clearSelection();
        }

        // Handle both signatures: setFilter(field, filterObject) and setFilter(field, value, type)
        let filterObject = null;

        if (typeof filterOrValue === 'object' && filterOrValue !== null) {
            // Complex filter object signature
            const fo = { ...filterOrValue };
            const columnTypeHint = this.getColumnType(field);
            if (fo && fo.operator && DATE_PRESET_OPERATORS.has(fo.operator) && columnTypeHint === 'date') {
                fo.type = 'date';
            }
            filterObject = fo;
        } else if (filterOrValue !== undefined && filterOrValue !== null && filterOrValue !== '') {
            // Simple value signature - convert to filter object
            filterObject = {
                type: filterType || 'text',
                operator: 'contains',
                value: filterOrValue
            };
        }

        if (filterObject && this._isFilterObjectActive(field, filterObject)) {
            this.filterState[field] = filterObject;
        } else {
            delete this.filterState[field];
        }

        // Apply immediately for responsive performance
        this.processData();
        this.render();

        // Regenerate checkbox lists for other open menus when any filter changes
        this.regenerateAllCheckboxLists(field);

        // Notify external listeners
        if (this.options.onFilter) {
            this.options.onFilter(field, filterOrValue, filterType);
        }

        // Persist/update Custom when filtering occurs (but NOT for Quick Filters)
        if (!this._suppressCustomPersist) {
            this.onUserCustomized && this.onUserCustomized('filter');
        }
        // Ensure dropdown reflects Custom visibility after filtering
        this.updateLayoutDropdown();

        // Update Clear Filters button and dropdown states
        this.updateFilterState();
    }

    _isFilterObjectActive(field, filterConfig) {
        if (!filterConfig) return false;

        const {
            operator,
            value,
            type,
            secondOperator,
            secondValue,
            checkedValues
        } = filterConfig;

        const normalizedType = type || this.getColumnType(field);
        const primaryOperator = operator || '';

        let hasFirst = false;
        if (primaryOperator && primaryOperator !== 'showAll' && primaryOperator !== 'pleaseSelect') {
            if (normalizedType === 'date' && DATE_PRESET_OPERATORS.has(primaryOperator)) {
                hasFirst = true;
            } else if (primaryOperator === 'inRange') {
                if (value && typeof value === 'object') {
                    const { fromValue, toValue } = value;
                    hasFirst = this._hasFilterValue(fromValue) || this._hasFilterValue(toValue);
                }
            } else if (primaryOperator === 'blank' || primaryOperator === 'notBlank') {
                hasFirst = true;
            } else {
                hasFirst = this._hasFilterValue(value);
            }
        }

        const secondaryOperator = secondOperator || '';
        let hasSecond = false;
        if (secondaryOperator && secondaryOperator !== 'showAll' && secondaryOperator !== 'pleaseSelect') {
            if (secondaryOperator === 'blank' || secondaryOperator === 'notBlank') {
                hasSecond = true;
            } else {
                hasSecond = this._hasFilterValue(secondValue);
            }
        }

        let hasCheckboxFilter = false;
        if (Array.isArray(checkedValues)) {
            hasCheckboxFilter = this._isCheckboxSelectionFiltered(field, checkedValues);
        } else if (normalizedType === 'checkbox' || primaryOperator === 'in') {
            hasCheckboxFilter = this._isCheckboxSelectionFiltered(field, this.checkboxState?.[field]);
        }

        return hasFirst || hasSecond || hasCheckboxFilter;
    }

    _hasFilterValue(val) {
        if (val === null || val === undefined) return false;
        if (typeof val === 'string') {
            return val.trim().length > 0;
        }
        if (typeof val === 'number') {
            return !Number.isNaN(val);
        }
        if (val instanceof Date) {
            return !Number.isNaN(val.getTime());
        }
        if (Array.isArray(val)) {
            return val.length > 0;
        }
        if (typeof val === 'object') {
            const entries = Object.values(val);
            return entries.some(entry => this._hasFilterValue(entry));
        }
        return true;
    }

    _isCheckboxSelectionFiltered(field, selection) {
        if (!selection) return false;
        const values = Array.isArray(selection) ? selection : Array.from(selection);
        if (values.length === 0) return true;

        const totalFromState = this._checkboxAllValueCounts ? this._checkboxAllValueCounts[field] : undefined;
        if (typeof totalFromState === 'number') {
            if (totalFromState <= 0) {
                return false;
            }
            return values.length < totalFromState;
        }

        const allValues = this._getUniqueValuesSyncCached(field, true, Number.MAX_SAFE_INTEGER);
        if (allValues.length === 0) {
            return false;
        }
        return values.length < allValues.length;
    }

    isFilterActive(field) {
        if (this._isFilterObjectActive(field, this.filterState[field])) {
            return true;
        }
        if (this.checkboxState && this.checkboxState[field]) {
            return this._isCheckboxSelectionFiltered(field, this.checkboxState[field]);
        }
        return false;
    }

    createFilterIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'snap-grid-filter-indicator';
        indicator.title = 'Filter applied';
        return indicator;
    }

    /**
     * Clear filter for a column (immediate - for internal use)
     */
    clearFilter(field) {
        console.log('🗑️ clearFilter called:', { field });

        // Clear selection on filter change to avoid index mismatch
        if (this.selectedRows && this.selectedRows.size > 0) {
            this.clearSelection();
        }

        // Clear both filter state and checkbox state for this field
        delete this.filterState[field];
        delete this.checkboxState[field];
        if (this._checkboxAllValueCounts) {
            delete this._checkboxAllValueCounts[field];
        }
        if (this._checkboxAllValues && this._checkboxAllValues[field]) {
            delete this._checkboxAllValues[field];
        }

        // Clear temporary state if it exists
        if (this.tempCheckboxState && this.tempCheckboxState[field]) {
            delete this.tempCheckboxState[field];
        }

        // Apply immediately for responsive performance
        this.processData();
        this.render();

        // Regenerate checkbox lists for other open menus when filter is cleared
        this.regenerateAllCheckboxLists(field);

        if (this.options.onFilter) {
            this.options.onFilter(field, null, null);
        }

        // Update Clear Filters button and dropdown states
        this.updateFilterState();

        // Update Custom layout in localStorage when filter is cleared
        if (this.currentLayoutType === 'custom' && !this._suppressCustomPersist) {
            this.saveOrUpdateCustomLayout();
        }
    }

    // REMOVED: clearFilterDebounced - redundant with main clearFilter function
    // Use clearFilter() directly - it already handles immediate application efficiently

    /**
     * Autosize column to fit content
     */
    autosizeColumn(field) {
        const column = this.processedColumns.find(col => col.field === field);
        if (!column) return;

        // Calculate minimum width based on header text
        const minWidth = this.calculateMinHeaderWidth(column);

        // Set to a reasonable autosize width (larger than min-width)
        const autosizeWidth = Math.max(minWidth, 200);
        column.width = autosizeWidth;

        this.render();
    }

    /**
     * Reset column to default state
     */
    resetColumn(field) {
        const column = this.processedColumns.find(col => col.field === field);
        if (!column) return;

        // Reset width and other properties
        column.width = 150; // Default width
        delete this.filterState[field];
        delete this.sortState[field];

        this.processData();
        this.render();
    }

    /**
     * Set column visibility
     */
    setColumnVisible(field, visible) {
        const column = this.processedColumns.find(col => col.field === field);
        if (!column) return false;

        const prevHidden = !!column.hide;
        column.hide = !visible;
        this.render();

        // Persist as Custom when visibility actually changed
        if (prevHidden !== column.hide) {
            this.onUserCustomized && this.onUserCustomized('visibility');
            if (typeof this.updateSelectAllStateInVisibilityTab === 'function') {
                this.updateSelectAllStateInVisibilityTab();
            }
        }
        return true;
    }

    /**
     * Column type mapping system - enhanced to support column.type property
     */
    getColumnType(field, column = null) {
        // First check if column object has explicit type property
        if (column && column.type) {
            switch (column.type) {
                case 'currency':
                    return 'numeric'; // Treat currency as numeric for filtering
                case 'number':
                    return 'numeric';
                case 'percentage':
                    return 'percentage';
                case 'date':
                    return 'date';
                case 'boolean':
                    return 'text-special';
                default:
                    // Fall through to field-based mapping
                    break;
            }
        }

        const columnTypes = {
            // text: Pure text columns
            'brand': 'text',
            'productTitle': 'text',
            'product_title': 'text',
            'title': 'text',

            // numeric: Pure numeric columns
            'price': 'numeric',
            'sales': 'numeric',
            'returns': 'numeric',
            'royalties': 'numeric',
            'bsr': 'numeric',
            'reviews': 'numeric',

            // percentage: Formatted percentage columns
            'returnRate': 'percentage',

            // text-numeric: Mixed content (can be filtered as text or numbers)
            'asin': 'text-numeric',
            'designId': 'text-numeric',
            'design_id': 'text-numeric',

            // text-special: Checkbox-only columns
            'marketplace': 'text-special',
            'productType': 'text-special',
            'product_type': 'text-special',
            'producttype': 'text-special',
            'status': 'text-special',
            'category': 'text-special',

            // date: Date columns
            'firstSold': 'date',
            'lastSold': 'date',
            'firstPublished': 'date',
            'lastUpdated': 'date',
            'firstsold': 'date',
            'lastsold': 'date',
            'firstpublished': 'date',
            'lastupdated': 'date',
            'first_sold': 'date',
            'last_sold': 'date',
            'first_published': 'date',
            'last_updated': 'date',
            'date': 'date',

            // fixed: Special UI columns (no filtering, no sorting, no resizing)
            'actions': 'fixed',
            'checkbox': 'fixed',
            'preview': 'fixed'
        };

        const fieldLower = field.toLowerCase();
        return columnTypes[fieldLower] || columnTypes[field] || 'text'; // Default to text
    }

    /**
     * Get filter type options based on column type - exact copy from old grid
     */
    getFilterTypeOptions(column) {
        const columnType = this.getColumnType(column.field, column);

        // Define filter options for each column type
        const filterOptionsByType = {
            text: [
                { value: 'contains', label: 'Contains' },
                { value: 'notContains', label: 'Not Contains' },
                { value: 'startsWith', label: 'Starts with' },
                { value: 'endsWith', label: 'Ends with' }
            ],

            numeric: [
                { value: 'equals', label: 'Equals' },
                { value: 'notEquals', label: 'Not equal' },
                { value: 'lessThan', label: 'Less than' },
                { value: 'lessThanOrEqual', label: 'Less than or equals' },
                { value: 'greaterThan', label: 'Greater than' },
                { value: 'greaterThanOrEqual', label: 'Greater than or equals' },
                { value: 'inRange', label: 'In range' }
            ],

            percentage: [
                { value: 'equals', label: 'Equals' },
                { value: 'notEquals', label: 'Not equal' },
                { value: 'lessThan', label: 'Less than' },
                { value: 'lessThanOrEqual', label: 'Less than or equals' },
                { value: 'greaterThan', label: 'Greater than' },
                { value: 'greaterThanOrEqual', label: 'Greater than or equals' },
                { value: 'inRange', label: 'In range' }
            ],

            'text-numeric': [
                { value: 'contains', label: 'Contains' },
                { value: 'notContains', label: 'Not Contains' },
                { value: 'startsWith', label: 'Starts with' },
                { value: 'endsWith', label: 'Ends with' }
            ],

            date: [
                { value: 'showAll', label: 'Show All' },
                { value: 'today', label: 'Today' },
                { value: 'yesterday', label: 'Yesterday' },
                { value: 'last7Days', label: 'Last 7 Days' },
                { value: 'last30Days', label: 'Last 30 Days' },
                { value: 'last90Days', label: 'Last 90 Days' },
                { value: 'last6Months', label: 'Last 6 Months' },
                { value: 'currentMonth', label: 'Current Month' },
                { value: 'lastMonth', label: 'Last Month' },
                { value: 'currentYear', label: 'Current Year' },
                { value: 'lastYear', label: 'Last Year' },
                { value: 'equals', label: 'Equals' },
                { value: 'notEquals', label: 'Not equal' },
                { value: 'lessThan', label: 'Less than' },
                { value: 'greaterThan', label: 'Greater than' },
                { value: 'inRange', label: 'In range' }
            ],

            'text-special': [], // No dropdown - checkbox only

            fixed: [] // No filtering at all
        };

        const options = filterOptionsByType[columnType] || filterOptionsByType.text;

        // Return in the format expected by createCustomDropdown
        return options.map(opt => ({
            value: opt.value,
            text: opt.label
        }));
    }

    /**
     * Get filter type based on column type (for internal filtering logic) - exact copy from old grid
     */
    getFilterType(column) {
        const columnType = this.getColumnType(column.field, column);

        // Map our column types to internal filter types
        switch (columnType) {
            case 'numeric':
            case 'percentage':
                return 'number';
            case 'date':
                return 'date';
            case 'text-special':
                return 'set';
            case 'text':
            case 'text-numeric':
            default:
                return 'text';
        }
    }

    /**
     * Create snap dropdown component (exact copy from old grid)
     */
    createCustomDropdown(options, defaultValue = '') {
        const dropdown = document.createElement('div');
        dropdown.className = 'snap-dropdown';

        const trigger = document.createElement('div');
        trigger.className = 'dropdown-header';

        const triggerText = document.createElement('span');
        triggerText.textContent = defaultValue;

        const arrow = document.createElement('img');
        arrow.src = 'assets/dropdown-ic.svg';
        arrow.alt = 'Dropdown';
        arrow.className = 'dropdown-arrow';

        trigger.appendChild(triggerText);
        trigger.appendChild(arrow);

        const menu = document.createElement('div');
        menu.className = 'dropdown-menu hidden';

        // Parse options (can be HTML string or array)
        let optionsList = [];
        if (typeof options === 'string') {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = options;
            const optionElements = tempDiv.querySelectorAll('option');
            optionsList = Array.from(optionElements).map(opt => ({
                value: opt.value,
                text: opt.textContent
            }));
        } else if (Array.isArray(options)) {
            optionsList = options;
        }

        // Check if defaultValue matches any option value
        const matchingOption = optionsList.find(option => option.value === defaultValue);

        optionsList.forEach(option => {
            // Check if this is a divider
            if (option.divider) {
                const dividerElement = this.createMenuSeparator();
                menu.appendChild(dividerElement);
                return;
            }

            const optionElement = document.createElement('div');
            optionElement.className = 'dropdown-item';
            optionElement.textContent = option.text || option.value;
            optionElement.setAttribute('data-value', option.value);

            // Only add selected class if defaultValue matches an actual option value
            if (matchingOption && option.value === defaultValue) {
                optionElement.classList.add('selected');
                triggerText.textContent = option.text || option.value;
            }

            optionElement.addEventListener('click', (e) => {
                e.stopPropagation();

                // Check if dropdown is disabled
                if (dropdown.classList.contains('disabled')) {
                    console.log('🚫 Dropdown item click blocked - dropdown is disabled');
                    return; // Don't process click if disabled
                }

                // Update selected option
                menu.querySelectorAll('.dropdown-item').forEach(opt => {
                    opt.classList.remove('selected');
                });
                optionElement.classList.add('selected');
                triggerText.textContent = optionElement.textContent;

                // Close dropdown
                dropdown.classList.remove('focused');
                menu.classList.add('hidden');
                // Ensure ALL dropdowns lose focus when an item is selected
                document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                    d.classList.remove('focused');
                    const m = d.querySelector('.dropdown-menu');
                    if (m) m.classList.add('hidden');
                });

                // Dispatch change event
                const changeEvent = new CustomEvent('change', {
                    detail: { value: option.value, text: option.text || option.value }
                });
                dropdown.dispatchEvent(changeEvent);
            });

            menu.appendChild(optionElement);
        });

        // Toggle dropdown
        trigger.addEventListener('click', (e) => {
            e.stopPropagation();

            // Check if dropdown is disabled
            if (dropdown.classList.contains('disabled')) {
                return; // Don't open if disabled
            }

            // Close any open column menus before opening dropdown, but NOT when this dropdown lives inside a column menu
            if (!dropdown.closest('.snap-grid-column-menu')) {
                this.hideColumnMenu();
            }

            const isOpen = !menu.classList.contains('hidden');

            // Close all other dropdowns
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                d.classList.remove('focused');
                d.querySelector('.dropdown-menu').classList.add('hidden');
            });

            if (!isOpen) {
                dropdown.classList.add('focused');
                menu.classList.remove('hidden');
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', (e) => {
            if (!dropdown.contains(e.target)) {
                dropdown.classList.remove('focused');
                menu.classList.add('hidden');
            }
        });

        dropdown.appendChild(trigger);
        dropdown.appendChild(menu);

        return dropdown;
    }

    /**
     * Get custom dropdown value (exact copy from old grid)
     */
    getCustomDropdownValue(dropdown) {
        const selectedItem = dropdown.querySelector('.dropdown-item.selected');
        return selectedItem ? selectedItem.getAttribute('data-value') : '';
    }

    /**
     * Set custom dropdown value (exact copy from old grid)
     */
    setCustomDropdownValue(dropdown, value) {
        const triggerText = dropdown.querySelector('.dropdown-header span');
        const menu = dropdown.querySelector('.dropdown-menu');
        const items = menu.querySelectorAll('.dropdown-item');

        // Remove selected class from all items
        items.forEach(item => item.classList.remove('selected'));

        // Find and select the matching item
        const matchingItem = Array.from(items).find(item =>
            item.getAttribute('data-value') === value
        );

        if (matchingItem) {
            matchingItem.classList.add('selected');
            triggerText.textContent = matchingItem.textContent;
        }
    }

    /**
     * Create menu separator (helper for dropdown)
     */
    createMenuSeparator() {
        const separator = document.createElement('div');
        separator.className = 'dropdown-separator';
        return separator;
    }

    /**
     * Create checkbox item - exact copy from old grid
     */
    createCheckboxItem(value, checked = false, isSelectAll = false, fieldName = null, applyFilterCallback = null) {
        const item = document.createElement('div');
        item.className = 'filter-checkbox-item';
        if (isSelectAll) {
            item.classList.add('select-all');
        }

        // Create checkbox wrapper using the app's checkbox system (same as Show/Hide)
        const checkboxWrapper = document.createElement('div');
        checkboxWrapper.className = 'checkbox-wrapper';

        const checkboxImg = document.createElement('img');
        checkboxImg.className = 'checkbox-icon';
        checkboxImg.draggable = false;
        checkboxImg.setAttribute('data-value', value);

        // Set appropriate icon based on state
        if (isSelectAll) {
            // Calculate proper initial state for Select All checkbox
            if (fieldName) {
                const currentState = this.getCheckboxState(fieldName);
                const allValues = this._getUniqueValuesSyncCached(fieldName, true, Number.MAX_SAFE_INTEGER, true);
                const totalVisible = allValues.length;
                const checkedVisibleCount = allValues.reduce((count, val) => (
                    currentState.has(val) ? count + 1 : count
                ), 0);

                if (totalVisible === 0 || checkedVisibleCount === 0) {
                    checkboxImg.src = './assets/uncheckedbox-ic.svg';
                    checkboxImg.alt = 'Unchecked';
                } else if (checkedVisibleCount === totalVisible) {
                    checkboxImg.src = './assets/checkbox-ic.svg';
                    checkboxImg.alt = 'Checked';
                } else {
                    checkboxImg.src = './assets/indeterminate-ic.svg';
                    checkboxImg.alt = 'Indeterminate';
                }
            } else {
                // Fallback to indeterminate if no field name
                checkboxImg.src = './assets/indeterminate-ic.svg';
                checkboxImg.alt = 'Select All';
            }
        } else {
            checkboxImg.src = checked ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
            checkboxImg.alt = checked ? 'Checked' : 'Unchecked';
        }

        const label = document.createElement('label');
        label.className = 'filter-checkbox-label';
        // Show value exactly like the cell displays, but keep raw value for matching
        label.textContent = isSelectAll ? String(value) : String(value);

        // Track checked state
        let isChecked = checked;

        // For Select All, set isChecked based on actual state
        if (isSelectAll && fieldName) {
            const currentState = this.getCheckboxState(fieldName);
            const allValues = this._getUniqueValuesSyncCached(fieldName, true, Number.MAX_SAFE_INTEGER, true);
            const totalVisible = allValues.length;
            const checkedVisibleCount = allValues.reduce((count, val) => (
                currentState.has(val) ? count + 1 : count
            ), 0);
            isChecked = totalVisible > 0 && checkedVisibleCount === totalVisible;
        }

        // Toggle function using centralized state
        const toggleCheckbox = () => {
            if (isSelectAll) {
                // Select All logic using centralized state
                if (!fieldName) return;

                const currentState = this.getCheckboxState(fieldName);
                // Derive all values from the current DOM list instead of the (cleared) unique-values cache
                const parentList = item.parentElement;
                const allValueIcons = parentList.querySelectorAll('.filter-checkbox-item:not(.select-all) .checkbox-icon');
                const allValues = Array.from(allValueIcons).map(ic => ic.getAttribute('data-value'));
                const shouldCheckAll = currentState.size < allValues.length;

                console.log('🔄 Select All clicked:', {
                    field: fieldName,
                    totalValues: allValues.length,
                    currentlyChecked: currentState.size,
                    shouldCheckAll,
                    willApplyFilter: !!applyFilterCallback
                });

                // Update centralized state
                this.setAllCheckboxValues(fieldName, allValues, shouldCheckAll);

                // Update all visual checkboxes
                const checkboxList = item.parentElement;
                const allIcons = checkboxList.querySelectorAll('.checkbox-icon:not([data-value="Select All"])');
                allIcons.forEach(icon => {
                    icon.src = shouldCheckAll ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                    icon.alt = shouldCheckAll ? 'Checked' : 'Unchecked';
                });

                // Update Select All icon
                checkboxImg.src = shouldCheckAll ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = shouldCheckAll ? 'Checked' : 'Unchecked';
                isChecked = shouldCheckAll;

                // Apply filter using callback
                if (applyFilterCallback) {
                    console.log('🎯 Triggering filter application after Select All');
                    applyFilterCallback();
                } else {
                    console.warn('⚠️ No applyFilterCallback available');
                }
            } else {
                // Regular checkbox toggle using centralized state
                if (!fieldName) return;

                const currentlyChecked = this.getCheckboxState(fieldName).has(value);
                isChecked = !currentlyChecked;

                // Update centralized state
                this.setCheckboxValue(fieldName, value, isChecked);

                // Update visual state
                checkboxImg.src = isChecked ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                checkboxImg.alt = isChecked ? 'Checked' : 'Unchecked';

                // Update Select All visual state
                const checkboxList = item.parentElement;
                const selectAllIcon = checkboxList.querySelector('.checkbox-icon[data-value="Select All"]');
                if (selectAllIcon) {
                    const currentState = this.getCheckboxState(fieldName);
                    // Derive all values from current DOM list for accurate tri-state
                    const allValueIcons = checkboxList.querySelectorAll('.filter-checkbox-item:not(.select-all) .checkbox-icon');
                    const allValues = Array.from(allValueIcons).map(ic => ic.getAttribute('data-value'));

                    if (currentState.size === 0) {
                        selectAllIcon.src = './assets/uncheckedbox-ic.svg';
                        selectAllIcon.alt = 'Unchecked';
                    } else if (currentState.size === allValues.length) {
                        selectAllIcon.src = './assets/checkbox-ic.svg';
                        selectAllIcon.alt = 'Checked';
                    } else {
                        selectAllIcon.src = './assets/indeterminate-ic.svg';
                        selectAllIcon.alt = 'Indeterminate';
                    }
                }

                // Apply filter using callback
                if (applyFilterCallback) {
                    console.log('🎯 Triggering filter application after checkbox change');
                    applyFilterCallback();
                } else {
                    console.warn('⚠️ No applyFilterCallback available');
                }
            }
        };

        // Store checkbox state reference for Select All updates
        item._checkboxState = { isChecked };

        // Add single click handler to the entire item to prevent double-click issues
        item.addEventListener('click', (e) => {
            e.stopPropagation();
            toggleCheckbox();
        });

        checkboxWrapper.appendChild(checkboxImg);
        item.appendChild(checkboxWrapper);
        item.appendChild(label);

        return item;
    }

    /**
     * Create checkbox list for text-special columns with async processing
     */
    async createCheckboxList(column, checkboxList, searchInput) {
        console.log('🔍 createCheckboxList called for field:', column.field);

        // Show loading indicator for large datasets
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'checkbox-loading';
        loadingIndicator.textContent = 'Loading options...';
        loadingIndicator.style.padding = '10px';
        loadingIndicator.style.textAlign = 'center';
        loadingIndicator.style.color = '#666';
        checkboxList.appendChild(loadingIndicator);

        try {
            // Get unique values from current column data (async for large datasets)
            const uniqueValues = await this.getFilteredUniqueValues(column.field, 200);
            console.log('📊 Unique values found:', uniqueValues);

            // Remove loading indicator
            loadingIndicator.remove();

        // Initialize checkbox state for this field
        this.initializeCheckboxState(column.field, uniqueValues);

        // Create apply filter callback
        const applyFilter = () => {
            this.applyCheckboxFilter(column.field);
        };

        // Create "Select All" checkbox using the proper method
        const selectAllItem = this.createCheckboxItem('Select All', true, true, column.field, applyFilter);
        checkboxList.appendChild(selectAllItem);
        console.log('✅ Select All checkbox added');

        // Create checkboxes for each unique value using centralized state
        uniqueValues.forEach(value => {
            const isChecked = this.getCheckboxState(column.field).has(value);
            const checkboxItem = this.createCheckboxItem(value, isChecked, false, column.field, applyFilter);
            checkboxList.appendChild(checkboxItem);
        });
        console.log('✅ All checkbox items added, total items:', checkboxList.children.length);

        // Standard tri-state update for Select All based on current state and visible values
        const initSelectAllIcon = checkboxList.querySelector('.checkbox-icon[data-value="Select All"]');
        if (initSelectAllIcon) {
            const currentState = this.getCheckboxState(column.field);
            const total = uniqueValues.length;
            const checkedVisibleCount = uniqueValues.reduce((count, value) => (
                currentState.has(value) ? count + 1 : count
            ), 0);

            if (total === 0 || checkedVisibleCount === 0) {
                initSelectAllIcon.src = './assets/uncheckedbox-ic.svg';
                initSelectAllIcon.alt = 'Unchecked';
            } else if (checkedVisibleCount === total) {
                initSelectAllIcon.src = './assets/checkbox-ic.svg';
                initSelectAllIcon.alt = 'Checked';
            } else {
                initSelectAllIcon.src = './assets/indeterminate-ic.svg';
                initSelectAllIcon.alt = 'Indeterminate';
            }
        }

        // Function to regenerate checkbox list based on current filtered data (like old grid)
        const regenerateCheckboxList = async () => {
            // Cache current search term so we can restore it after rebuild
            const searchTerm = searchInput.value;

            // Compute values that should be available based on other filters
            const updatedUniqueValues = await this.getFilteredUniqueValues(column.field, 200);
            const updatedUniqueSet = new Set(updatedUniqueValues);

            // Ensure checkbox state and value caches exist for this field
            if (!this.checkboxState[column.field]) {
                this.checkboxState[column.field] = new Set();
            }
            if (!this._checkboxAllValues) {
                this._checkboxAllValues = Object.create(null);
            }
            const knownValues = this._checkboxAllValues[column.field] || new Set();
            const knownValuesBefore = new Set(knownValues);

            // Track highest observed option count for filter state calculations
            if (!this._checkboxAllValueCounts) {
                this._checkboxAllValueCounts = {};
            }

            // Incorporate any newly visible values and default them to checked
            updatedUniqueValues.forEach(value => {
                if (!knownValues.has(value)) {
                    knownValues.add(value);
                }
                if (!knownValuesBefore.has(value) && !this.checkboxState[column.field].has(value)) {
                    this.checkboxState[column.field].add(value);
                }
            });
            this._checkboxAllValues[column.field] = knownValues;

            const knownCount = knownValues.size;
            const storedCount = this._checkboxAllValueCounts[column.field];
            if (typeof storedCount !== 'number' || knownCount > storedCount) {
                this._checkboxAllValueCounts[column.field] = knownCount;
            }

            // Remove checkbox items whose values are no longer available
            const existingItems = checkboxList.querySelectorAll('.filter-checkbox-item:not(.select-all)');
            existingItems.forEach(item => {
                const value = item.querySelector('.checkbox-icon')?.getAttribute('data-value');
                if (!updatedUniqueSet.has(value)) {
                    item.remove();
                }
            });

            // Build a map of remaining items for quick lookup
            const remainingItems = new Map();
            checkboxList.querySelectorAll('.filter-checkbox-item:not(.select-all)').forEach(item => {
                const value = item.querySelector('.checkbox-icon')?.getAttribute('data-value');
                if (value) {
                    remainingItems.set(value, item);
                }
            });

            // Append or update items to match the latest ordering
            updatedUniqueValues.forEach(value => {
                const isChecked = this.checkboxState[column.field].has(value);
                const existingItem = remainingItems.get(value);
                if (existingItem) {
                    const icon = existingItem.querySelector('.checkbox-icon');
                    if (icon) {
                        icon.src = isChecked ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                        icon.alt = isChecked ? 'Checked' : 'Unchecked';
                    }
                    const labelEl = existingItem.querySelector('.filter-checkbox-label');
                    if (labelEl) {
                        labelEl.textContent = String(value);
                    }
                    checkboxList.appendChild(existingItem);
                } else {
                    const checkboxItem = this.createCheckboxItem(value, isChecked, false, column.field, applyFilter);
                    checkboxList.appendChild(checkboxItem);
                }
            });

            // Reapply search filter if one was active
            if (searchTerm) {
                this.filterCheckboxList(checkboxList, searchTerm);
            }

            // Update Select All icon based on current state
            const selectAllIcon = checkboxList.querySelector('.checkbox-icon[data-value="Select All"]');
            if (selectAllIcon) {
                const current = this.getCheckboxState(column.field);
                const total = updatedUniqueValues.length;
                const checkedVisibleCount = updatedUniqueValues.reduce((count, value) => (
                    current.has(value) ? count + 1 : count
                ), 0);
                if (total === 0 || checkedVisibleCount === 0) {
                    selectAllIcon.src = './assets/uncheckedbox-ic.svg';
                    selectAllIcon.alt = 'Unchecked';
                } else if (checkedVisibleCount === total) {
                    selectAllIcon.src = './assets/checkbox-ic.svg';
                    selectAllIcon.alt = 'Checked';
                } else {
                    selectAllIcon.src = './assets/indeterminate-ic.svg';
                    selectAllIcon.alt = 'Indeterminate';
                }
            }
        };

        // Allow external triggers (and local applyFilter) to refresh the checkbox options
        checkboxList.addEventListener('regenerateCheckboxList', () => {
            regenerateCheckboxList();
        });

        // Search functionality - filter the checkbox list
        searchInput.addEventListener('input', (e) => {
            this.filterCheckboxList(checkboxList, e.target.value);
        });

        // Return the regenerateCheckboxList function so it can be used by filter inputs
        return regenerateCheckboxList;

        } catch (error) {
            console.error('Error creating checkbox list:', error);
            // Remove loading indicator on error
            if (loadingIndicator && loadingIndicator.parentNode) {
                loadingIndicator.remove();
            }
            // Return empty function to prevent errors
            return () => {};
        } finally {
            // no-op
        }
    }

    /**
     * Get unique values for a field from rows that pass all other filters.
     * The field's own checkbox selections are ignored so manually unchecked options
     * remain visible in the list, while values removed by other columns drop out.
     */
    async getFilteredUniqueValues(field, limit = 200) {
        return await this.getUniqueColumnValues(field, true, limit, true);
    }


    /**
     * Filter checkbox list based on search term - exact copy from old grid
     */
    filterCheckboxList(checkboxList, searchTerm) {
        const items = checkboxList.querySelectorAll('.filter-checkbox-item:not(.select-all)');
        const lowerSearchTerm = searchTerm.toLowerCase();

        items.forEach(item => {
            const label = item.querySelector('.filter-checkbox-label');
            const text = label ? label.textContent.toLowerCase() : '';
            const shouldShow = text.includes(lowerSearchTerm);
            item.style.display = shouldShow ? 'flex' : 'none';
        });
    }

    // Unique list helpers (index-array based)

    /**
     * Get unique values from column data with index arrays, cache, and early exit
     */
    async getUniqueColumnValues(field, useFilteredData = false, limit = 200, includeFieldFilteredOutValues = true) {
        const filterSig = this._buildFilterSignature(field);
        const cacheKey = `${field}|${filterSig}|${limit}|${includeFieldFilteredOutValues ? 'include' : 'strict'}`;
        if (this._uniqueCache[cacheKey]) {
            return this._uniqueCache[cacheKey].slice();
        }

        const data = this.data || [];
        const total = data.length;
        if (total === 0) {
            this._uniqueCache[cacheKey] = [];
            return [];
        }

        const safeLimit = Math.max(1, limit || 0);
        const target = Math.max(10, safeLimit);
        const values = new Map();

        const addValue = (raw) => {
            if (raw === null || raw === undefined || raw === '') {
                return;
            }
            const key = this._stringifyFilterValue(raw);
            if (!values.has(key)) {
                values.set(key, String(raw));
            }
        };

        const filteredIdx = useFilteredData && Array.isArray(this.filteredIdx) ? this.filteredIdx : null;
        const chunkSize = total > 50000 ? 5000 : 2000;

        if (filteredIdx && filteredIdx.length) {
            for (let offset = 0; offset < filteredIdx.length && values.size < target; offset += chunkSize) {
                const end = Math.min(filteredIdx.length, offset + chunkSize);
                for (let i = offset; i < end; i++) {
                    const idx = filteredIdx[i];
                    const row = (idx >= 0 && idx < total) ? data[idx] : null;
                    if (!row) continue;
                    addValue(row[field]);
                    if (values.size >= target) break;
                }
                if (values.size >= target) break;
                if (end < filteredIdx.length) {
                    // Yield to the event loop for very large collections
                    await new Promise(r => setTimeout(r, 0));
                }
            }
        } else {
            for (let offset = 0; offset < total && values.size < target; offset += chunkSize) {
                const end = Math.min(total, offset + chunkSize);
                for (let i = offset; i < end; i++) {
                    const row = data[i];
                    if (!row) continue;
                    addValue(row[field]);
                    if (values.size >= target) break;
                }
                if (values.size >= target) break;
                if (end < total) {
                    await new Promise(r => setTimeout(r, 0));
                }
            }
        }

        if (useFilteredData && includeFieldFilteredOutValues && values.size < target) {
            const { descriptorMap, fieldDescriptor, otherDescriptors } = this._getDescriptorContext(field);
            if (descriptorMap && descriptorMap.size && (fieldDescriptor || otherDescriptors.length)) {
                const includedMask = new Uint8Array(total);
                if (filteredIdx && filteredIdx.length <= total) {
                    for (let i = 0; i < filteredIdx.length; i++) {
                        const idx = filteredIdx[i];
                        if (idx >= 0 && idx < total) {
                            includedMask[idx] = 1;
                        }
                    }
                }

                for (let offset = 0; offset < total && values.size < target; offset += chunkSize) {
                    const end = Math.min(total, offset + chunkSize);
                    for (let i = offset; i < end; i++) {
                        if (includedMask[i]) continue;
                        const row = data[i];
                        if (!row) continue;

                        let pass = true;
                        for (let j = 0; j < otherDescriptors.length; j++) {
                            if (!this._matchesPreparedFilter(row, otherDescriptors[j], i)) {
                                pass = false;
                                break;
                            }
                        }
                        if (!pass) continue;

                        if (fieldDescriptor && !this._matchesPreparedFilter(row, fieldDescriptor, i, undefined, { ignoreCheckbox: true })) {
                            continue;
                        }

                        addValue(row[field]);
                        if (values.size >= target) break;
                    }
                    if (values.size >= target) break;
                    if (end < total) {
                        await new Promise(r => setTimeout(r, 0));
                    }
                }
            }
        }

        const uniqueValues = this.sortValuesForField(field, Array.from(values.values()).slice(0, safeLimit));
        this._uniqueCache[cacheKey] = uniqueValues.slice();
        return uniqueValues;

    }


    /** Build a signature of current filter state excluding checkbox values for a given field */
    _buildFilterSignature(excludeField) {
        const entries = Object.entries(this.filterState || {});
        // strip checkedValues for excludeField to avoid circular cache invalidation
        const simplified = entries.map(([f, flt]) => {
            if (f === excludeField) {
                const { type, operator, value, secondOperator, secondValue, logicOperator } = flt || {};
                return [f, { type, operator, value, secondOperator, secondValue, logicOperator }];
            }
            return [f, flt];
        }).sort((a,b) => a[0].localeCompare(b[0]));
        return JSON.stringify(simplified);
    }

    /** Return all row indices [0..data.length) */
    _getAllIdx() { return this.data ? this.data.map((_, i) => i) : []; }

    /**
     * Return cached unique values if present, otherwise [] (non-blocking)
     */
    _getUniqueValuesSyncCached(field, useFilteredData = false, limit = 200, includeFieldFilteredOutValues = true) {
        const filterSig = this._buildFilterSignature(field);
        const cacheKey = `${field}|${filterSig}|${limit}|${includeFieldFilteredOutValues ? 'include' : 'strict'}`;
        const arr = this._uniqueCache[cacheKey];
        return Array.isArray(arr) ? arr.slice() : [];
    }


    /**
     * Build filtered indices excluding checkbox filter for a specific field
     */
    _getFilteredIdxExcludingCheckboxes(excludeField) {
        if (!this.filterState || Object.keys(this.filterState).length === 0) {
            return this._getAllIdx();
        }

        const { descriptorMap, fieldDescriptor, otherDescriptors } = this._getDescriptorContext(excludeField);
        if (!descriptorMap || descriptorMap.size === 0) {
            return this._getAllIdx();
        }

        const out = [];
        const data = this.data;
        const len = data.length;

        for (let i = 0; i < len; i++) {
            const row = data[i];
            if (!row) continue;

            let pass = true;
            for (let j = 0; j < otherDescriptors.length; j++) {
                if (!this._matchesPreparedFilter(row, otherDescriptors[j], i)) {
                    pass = false;
                    break;
                }
            }
            if (!pass) {
                continue;
            }

            if (fieldDescriptor && !this._matchesPreparedFilter(row, fieldDescriptor, i, undefined, { ignoreCheckbox: true })) {
                continue;
            }

            out.push(i);
        }

        return out;
    }





    // -----------------------
    // Legacy object-returning path below kept for compatibility
    // -----------------------

    /**
     * Get filtered data excluding checkbox filters for a specific field
     * This is used to populate checkbox lists based on other active filters
     */
    getFilteredDataExcludingCheckboxes(excludeField) {
        window.SnapLogger?.debug('getFilteredDataExcludingCheckboxes', excludeField);

        if (!this.filterState || Object.keys(this.filterState).length === 0) {
            return [...this.data];
        }

        const { descriptorMap, fieldDescriptor, otherDescriptors } = this._getDescriptorContext(excludeField);
        if (!descriptorMap || descriptorMap.size === 0) {
            return [...this.data];
        }

        return this.data.filter((row, index) => {
            if (!row) return false;

            for (let j = 0; j < otherDescriptors.length; j++) {
                if (!this._matchesPreparedFilter(row, otherDescriptors[j], index)) {
                    return false;
                }
            }

            if (fieldDescriptor && !this._matchesPreparedFilter(row, fieldDescriptor, index, undefined, { ignoreCheckbox: true })) {
                return false;
            }

            return true;
        });
    }


    /**
     * Sort values for a specific field (helper method)
     */
    sortValuesForField(field, values) {
        // For now, just sort alphabetically
        // In the future, this could be enhanced with field-specific sorting logic
        return values.sort((a, b) => {
            // Handle numeric values
            const aNum = parseFloat(a);
            const bNum = parseFloat(b);
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return aNum - bNum;
            }
            // Default to string comparison
            return String(a).localeCompare(String(b));
        });
    }

    /**
     * Initialize checkbox state for a field - all checkboxes checked by default
     */
    initializeCheckboxState(field, uniqueValues) {
        if (!this.checkboxState) {
            this.checkboxState = {};
        }

        if (!this._checkboxAllValueCounts) {
            this._checkboxAllValueCounts = {};
        }
        const uniqueCount = Array.isArray(uniqueValues) ? uniqueValues.length : 0;
        const currentCount = this._checkboxAllValueCounts[field];
        if (typeof currentCount !== 'number' || uniqueCount > currentCount) {
            this._checkboxAllValueCounts[field] = uniqueCount;
        }

        if (!this._checkboxAllValues) {
            this._checkboxAllValues = Object.create(null);
        }
        const knownValues = this._checkboxAllValues[field] || new Set();
        if (Array.isArray(uniqueValues)) {
            uniqueValues.forEach(value => {
                knownValues.add(value);
            });
        }
        this._checkboxAllValues[field] = knownValues;

        if (!this.checkboxState[field]) {
            // Initialize with all values checked by default
            this.checkboxState[field] = new Set(uniqueValues);
        }
    }

    /**
     * Get checkbox state for a field - exact copy from old grid
     */
    getCheckboxState(field) {
        return this.checkboxState[field] || new Set();
    }

    /**
     * Set checkbox value for a field - exact copy from old grid
     */
    setCheckboxValue(field, value, checked) {
        if (!this.checkboxState[field]) {
            this.checkboxState[field] = new Set();
        }

        if (checked) {
            this.checkboxState[field].add(value);
        } else {
            this.checkboxState[field].delete(value);
        }
    }

    /**
     * Set all checkbox values for a field - exact copy from old grid
     */
    setAllCheckboxValues(field, allValues, checked) {
        if (!this.checkboxState[field]) {
            this.checkboxState[field] = new Set();
        }

        if (checked) {
            allValues.forEach(value => this.checkboxState[field].add(value));
        } else {
            this.checkboxState[field].clear();
        }
    }

    /**
     * Check if a value is checked - exact copy from old grid
     */
    isValueChecked(field, value) {
        return this.checkboxState[field] && this.checkboxState[field].has(value);
    }

    /**
     * Check a value - exact copy from old grid
     */
    checkValue(field, value) {
        if (!this.checkboxState[field]) {
            this.checkboxState[field] = new Set();
        }
        this.checkboxState[field].add(value);
    }

    /**
     * Uncheck a value - exact copy from old grid
     */
    uncheckValue(field, value) {
        if (this.checkboxState[field]) {
            this.checkboxState[field].delete(value);
        }
    }

    /**
     * Apply checkbox filter with correct filter type and operator
     */
    applyCheckboxFilter(field) {
        const checkedValues = Array.from(this.checkboxState[field] || []);

        console.log('🎯 applyCheckboxFilter called:', {
            field,
            checkedValuesCount: checkedValues.length,
            checkedValues: checkedValues.slice(0, 3) // Show first 3 for debugging
        });

        if (checkedValues.length === 0) {
            // No values selected = hide all rows for this field
            this.setFilter(field, {
                type: 'checkbox',
                operator: 'in',
                checkedValues: []
            });
        } else {
            // Set filter with proper checkbox type and operator
            this.setFilter(field, {
                type: 'checkbox',
                operator: 'in', // Use 'in' operator for checkbox filtering
                checkedValues: checkedValues
            });
        }

        // Note: setFilter already calls regenerateAllCheckboxLists, so no need to call it again
    }

    /**
     * Regenerate checkbox lists for all open column menus except the specified field
     * Optimized with debouncing to prevent excessive regeneration
     */
    regenerateAllCheckboxLists(excludeField) {
        // Debounce regeneration to prevent excessive calls
        if (this.regenerateDebounceTimer) {
            clearTimeout(this.regenerateDebounceTimer);
        }

        this.regenerateDebounceTimer = setTimeout(() => {
            // Find all open column menus with checkbox lists
            const openMenus = document.querySelectorAll('.snap-grid-column-menu');

            openMenus.forEach(menu => {
                const fieldName = menu.getAttribute('data-field');
                if (fieldName && fieldName !== excludeField) {
                    const checkboxList = menu.querySelector('.filter-checkbox-list');
                    if (checkboxList) {
                        // Trigger regeneration by dispatching a custom event
                        // This allows the existing regenerateCheckboxList function to handle it
                        const regenerateEvent = new CustomEvent('regenerateCheckboxList');
                        checkboxList.dispatchEvent(regenerateEvent);
                    }
                }
            });
        }, 100); // Debounce for 100ms to batch multiple calls
    }

    // REMOVED: regenerateCheckboxListForField - redundant with regenerateCheckboxList function
    // The regenerateCheckboxList function in createCheckboxList already handles this functionality

    /**
     * Create logic toggle for AND/OR filter logic
     */
    createLogicToggle(text, isActive, logicSection, applyFilter, firstInput, secondInput, firstDropdown, secondDropdown) {
        const wrapper = document.createElement('div');
        wrapper.className = 'logic-toggle';
        wrapper.setAttribute('data-logic', text);

        const checkbox = document.createElement('img');
        checkbox.className = 'logic-checkbox';
        checkbox.src = isActive ? 'assets/checkbox-ic.svg' : 'assets/uncheckedbox-ic.svg';
        checkbox.alt = isActive ? 'Checked' : 'Unchecked';
        checkbox.draggable = false;

        const label = document.createElement('span');
        label.textContent = text;
        label.className = 'logic-label';

        wrapper.appendChild(checkbox);
        wrapper.appendChild(label);

        // Toggle functionality - mutually exclusive
        wrapper.addEventListener('click', (e) => {
            e.stopPropagation();

            // Get both toggles
            const andToggle = logicSection.querySelector('[data-logic="AND"]');
            const orToggle = logicSection.querySelector('[data-logic="OR"]');

            // Reset both to unchecked
            const andCheckbox = andToggle.querySelector('.logic-checkbox');
            const orCheckbox = orToggle.querySelector('.logic-checkbox');

            andCheckbox.src = 'assets/uncheckedbox-ic.svg';
            andCheckbox.alt = 'Unchecked';

            orCheckbox.src = 'assets/uncheckedbox-ic.svg';
            orCheckbox.alt = 'Unchecked';

            // Set clicked one to checked
            checkbox.src = 'assets/checkbox-ic.svg';
            checkbox.alt = 'Checked';

            console.log('🔍 Logic toggle clicked:', text, 'is now active');

            // Trigger filter immediately when AND/OR logic changes
            if (applyFilter && typeof applyFilter === 'function') {
                console.log('🔍 Applying filter due to logic change to:', text);
                applyFilter(); // Apply immediately when logic changes
            }
        });

        return wrapper;
    }

    /**
     * Create menu option with icon
     */
    createMenuOptionWithIcon(text, iconSrc, action) {
        const option = document.createElement('div');
        option.className = 'menu-option-with-icon';

        const iconWrapper = document.createElement('div');
        iconWrapper.className = 'menu-option-icon-wrapper';

        const icon = document.createElement('img');
        icon.src = iconSrc;
        icon.className = 'menu-option-icon';
        icon.alt = text;
        icon.draggable = false;

        const label = document.createElement('span');
        label.textContent = text;
        label.className = 'menu-option-label';

        iconWrapper.appendChild(icon);
        option.appendChild(iconWrapper);
        option.appendChild(label);

        option.addEventListener('click', (e) => {
            e.stopPropagation();
            action();
        });

        return option;
    }

    /**
     * Get column pin state (stub implementation)
     */
    getColumnPinState(field) {
        const col = this.getColumn(field);
        if (!col || !col.pinned) return 'none';
        return col.pinned === 'left' || col.pinned === 'right' ? col.pinned : 'none';
    }

    /**
     * Pin column (stub implementation)
     */
    pinColumn(field, direction) {
        // direction: 'left' | 'right'
        this.setColumnPinned(field, direction);
    }

    /**
     * Unpin column (stub implementation)
     */
    unpinColumn(field) {
        // Clear pinned state, then restore to original center order
        const col = this.getColumn(field);
        if (!col) return;
        col.pinned = null;
        this.sortColumnsByPinned();
        this.restoreUnpinnedColumnOrder(field);
        this.render();
    }

    /**
     * Update pin submenu (stub implementation)
     */
    updatePinSubmenu(field, submenu) {
        if (!submenu) return;
        const state = this.getColumnPinState(field);
        const pinLeftCheck = submenu.querySelector('.pin-option:nth-child(1) .check-icon');
        const pinRightCheck = submenu.querySelector('.pin-option:nth-child(2) .check-icon');
        const dontPinCheck = submenu.querySelector('.pin-option:nth-child(3) .check-icon');

        if (pinLeftCheck) pinLeftCheck.className = `check-icon ${state === 'left' ? '' : 'hidden'}`;
        if (pinRightCheck) pinRightCheck.className = `check-icon ${state === 'right' ? '' : 'hidden'}`;
        if (dontPinCheck) dontPinCheck.className = `check-icon ${state === 'none' ? '' : 'hidden'}`;
    }

    /**
     * Restore unpinned column to its original order among center columns
     * based on the initial options.columns configuration order.
     */
    restoreUnpinnedColumnOrder(field) {
        const currentIndex = this.getColumnIndex(field);
        if (currentIndex < 0) return;

        // Build mapping from field -> original order index
        const originalOrderIndex = new Map(
            this.options.columns.map((c, i) => [c.field, i])
        );

        // Collect center (unpinned) column indices and compute target position
        const centerEntries = this.processedColumns
            .map((c, i) => ({ c, i }))
            .filter(x => (x.c.pinned || null) === null);

        if (centerEntries.length === 0) return;

        const minCenter = Math.min(...centerEntries.map(x => x.i));

        // Sort center fields by their original order
        const centerFieldsByOriginal = centerEntries
            .map(x => x.c.field)
            .sort((a, b) => (originalOrderIndex.get(a) ?? 1e9) - (originalOrderIndex.get(b) ?? 1e9));

        const posInCenter = centerFieldsByOriginal.indexOf(field);
        if (posInCenter < 0) return;

        const targetIndex = minCenter + posInCenter;
        if (targetIndex !== currentIndex) {
            this.moveColumn(currentIndex, targetIndex);
        }
    }

    /**
     * Autosize all columns (stub implementation)
     */
    autosizeAllColumns() {
        const excluded = ['checkbox', 'preview', 'actions', 'marketplace'];
        this.processedColumns.forEach(col => {
            if (!col.hide && !excluded.includes(col.field)) {
                this.autosizeColumn(col.field);
            }
        });
        // autosizeColumn already calls render; avoid excessive renders by final refresh if needed
        // this.render(); // not needed due to per-column render inside autosizeColumn
    }

    /**
     * Reset columns (stub implementation)
     */
    resetColumns() {
        // Reset widths to minimums and restore default pinning
        const excluded = ['checkbox', 'preview', 'actions', 'marketplace'];

        // Reset widths and visibility
        this.processedColumns.forEach(col => {
            // Reset visibility (show everything except respect explicit hide for special if any)
            if (!excluded.includes(col.field)) {
                col.hide = false;
            }

            // Reset width to minimum reasonable width
            const minWidth = this.calculateMinHeaderWidth(col);
            col.width = Math.max(minWidth, col.field === 'actions' ? (col.width || 96) : minWidth);
        });

        // Default pinning: checkbox, preview -> left; actions -> right; others -> none
        this.processedColumns.forEach(col => {
            if (col.field === 'checkbox' || col.field === 'preview') {
                col.pinned = 'left';
            } else if (col.field === 'actions') {
                col.pinned = 'right';
            } else {
                col.pinned = null;
            }
        });

        // Re-sort by pinning and render
        this.sortColumnsByPinned();
        this.render();
    }

    /**
     * Check if column is fixed (stub implementation)
     */
    isFixedColumn(field) {
        return ['checkbox', 'preview', 'actions', 'marketplace'].includes(field);
    }

    /**
     * Get valid insertion range for reordering visibility list items
     */
    getValidInsertionRange() {
        return this.getValidInsertionRangeForOrder(this.processedColumns);
    }

    /**
     * Compute valid insertion range for a given order of columns
     * Ensures we don't cross special/fixed columns like checkbox/preview/actions
     */
    getValidInsertionRangeForOrder(columnOrder) {
        if (!Array.isArray(columnOrder) || columnOrder.length === 0) {
            return { minIndex: 0, maxIndex: 0 };
        }

        const isReorderable = (col) => col && !['checkbox', 'preview', 'actions', 'marketplace'].includes(col.field);
        const reorderableIndices = [];
        for (let i = 0; i < columnOrder.length; i++) {
            const col = columnOrder[i];
            if (isReorderable(col)) reorderableIndices.push(i);
        }

        if (reorderableIndices.length === 0) {
            return { minIndex: 0, maxIndex: 0 };
        }

        const minIndex = Math.min(...reorderableIndices);
        const maxIndexExclusive = Math.max(...reorderableIndices) + 1; // insert after last reorderable
        return { minIndex, maxIndex: maxIndexExclusive };
    }

    /**
     * Get the element after which the dragged item should be inserted
     */
    getDragAfterElement(container, y) {
        const draggableElements = [...container.querySelectorAll('.column-item:not(.dragging)')];

        // Translate processedColumns range to list indices (list includes Select All at index 0)
        const validRange = this.getValidInsertionRange();
        const validElements = draggableElements.filter((_, index) => {
            // index 0 is the Select All item; positions start effectively at 0 for before first item
            return index >= validRange.minIndex - 1 && index < validRange.maxIndex;
        });

        return validElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect();
            const offset = y - box.top - box.height / 2;
            if (offset < 0 && offset > closest.offset) {
                return { offset, element: child };
            }
            return closest;
        }, { offset: Number.NEGATIVE_INFINITY }).element;
    }

    /**
     * Update drag indicator position based on drop target
     */
    updateDragIndicatorPosition(indicator, container, afterElement) {
        if (!indicator || !container) return;

        const columnListRect = container.getBoundingClientRect();
        const validRange = this.getValidInsertionRange();
        const scrollTop = container.scrollTop;
        const allItems = [...container.querySelectorAll('.column-item:not(.dragging)')];

        if (afterElement == null) {
            const maxValidIndex = Math.min(validRange.maxIndex - 1, allItems.length - 1);
            const lastValidItem = allItems[maxValidIndex];
            if (lastValidItem) {
                const rect = lastValidItem.getBoundingClientRect();
                indicator.style.top = (rect.bottom - columnListRect.top + scrollTop) + 'px';
            } else {
                const minValidItem = allItems[validRange.minIndex - 1];
                if (minValidItem) {
                    const rect = minValidItem.getBoundingClientRect();
                    indicator.style.top = (rect.bottom - columnListRect.top + scrollTop) + 'px';
                } else {
                    indicator.style.top = scrollTop + 'px';
                }
            }
        } else {
            const afterIndex = allItems.indexOf(afterElement);
            if (afterIndex >= validRange.minIndex - 1 && afterIndex < validRange.maxIndex) {
                const rect = afterElement.getBoundingClientRect();
                indicator.style.top = (rect.top - columnListRect.top + scrollTop) + 'px';
            } else {
                indicator.classList.remove('active');
                return;
            }
        }

        indicator.style.left = '0px';
        indicator.style.right = '0px';
        indicator.classList.add('active');
    }

    /**
     * Reorder column by insertion index within processedColumns
     */
    reorderColumnByIndex(field, index) {
        if (!field) return;
        if (this.isFixedColumn(field)) return;

        const currentOrder = [...this.processedColumns];
        const fromIndex = currentOrder.findIndex(c => c.field === field);
        if (fromIndex === -1) return;

        // Remove dragged column
        const [dragged] = currentOrder.splice(fromIndex, 1);

        // Compute valid range after removal
        const { minIndex, maxIndex } = this.getValidInsertionRangeForOrder(currentOrder);
        let insertionIndex = index;
        if (fromIndex < insertionIndex) insertionIndex--; // account for removal shift
        insertionIndex = Math.max(minIndex, Math.min(insertionIndex, maxIndex));

        currentOrder.splice(insertionIndex, 0, dragged);
        this.processedColumns = currentOrder;

        if (typeof this.computePinnedOffsets === 'function') {
            this.computePinnedOffsets();
        }
        this.render();

        if (typeof this.updateAllOpenVisibilityLists === 'function') {
            this.updateAllOpenVisibilityLists();
        }
    }

    /**
     * Toggle select all columns in list (stub implementation)
     */
    toggleSelectAllColumnsInList(checkbox, columnList) {
        console.log('Toggle select all columns');
        // Implement actual select all logic later
    }

    /**
     * Update select all state in list (stub implementation)
     */
    updateSelectAllStateInList(checkbox, columnList) {
        // Set initial state to checked for now
        checkbox.src = 'assets/checkbox-ic.svg';
        checkbox.alt = 'Checked';
    }

    /**
     * Update select all state in visibility tab (stub implementation)
     */
    updateSelectAllStateInVisibilityTab() {
        console.log('Update select all state in visibility tab');
        // Implement actual update logic later
    }

    /**
     * Add drag and drop listeners to a visibility list item
     */
    addColumnDragListeners(item, column) {
        if (!item) return;

        item.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', column.field);
            e.dataTransfer.effectAllowed = 'move';
            item.classList.add('dragging');

            const columnList = item.parentElement;
            let dragIndicator = columnList.querySelector('.drag-indicator');
            if (!dragIndicator) {
                dragIndicator = document.createElement('div');
                dragIndicator.className = 'drag-indicator';
                columnList.appendChild(dragIndicator);
            }
            dragIndicator.classList.add('active');
        });

        item.addEventListener('dragend', () => {
            item.classList.remove('dragging');
            const columnList = item.parentElement;
            if (columnList) {
                const dragIndicator = columnList.querySelector('.drag-indicator');
                if (dragIndicator) dragIndicator.remove();
                columnList.querySelectorAll('.column-item').forEach(el => el.classList.remove('drag-over'));
            }
        });

        item.addEventListener('dragenter', (e) => {
            e.preventDefault();
            item.classList.add('drag-over');
        });

        item.addEventListener('dragleave', (e) => {
            if (!item.contains(e.relatedTarget)) {
                item.classList.remove('drag-over');
            }
        });
    }

    /**
     * Rebuild any open visibility lists to reflect current processedColumns order
     */
    updateAllOpenVisibilityLists() {
        const lists = document.querySelectorAll('.snap-grid-column-menu .menu-tab-panel[data-tab-panel="visibility"] .column-list');
        lists.forEach((columnList) => {
            if (!columnList) return;
            const selectAll = columnList.querySelector('.select-all-item');
            columnList.innerHTML = '';
            if (selectAll) columnList.appendChild(selectAll);

            this.processedColumns.forEach((column, index) => {
                if (['checkbox', 'preview', 'actions', 'marketplace'].includes(column.field)) return;
                const item = this.createVisibilityColumnItem(column);
                item.setAttribute('data-column-index', index);
                columnList.appendChild(item);
            });

            const selectAllCheckbox = selectAll ? selectAll.querySelector('.checkbox-icon') : null;
            if (selectAllCheckbox) this.updateSelectAllStateInList(selectAllCheckbox, columnList);
        });
    }

    /**
     * Toggle Select All columns functionality for columns in the list
     */
    toggleSelectAllColumnsInList(selectAllCheckbox, columnList) {
        const isCurrentlyAllSelected = this.areAllColumnsInListVisible(columnList);

        if (isCurrentlyAllSelected) {
            // Deselect all columns in the list (hide them)
            this.hideAllColumnsInList(columnList);
            selectAllCheckbox.src = './assets/uncheckedbox-ic.svg';
            selectAllCheckbox.alt = 'Unchecked';
        } else {
            // Select all columns in the list (show them)
            this.showAllColumnsInList(columnList);
            selectAllCheckbox.src = './assets/checkbox-ic.svg';
            selectAllCheckbox.alt = 'Checked';
        }

        // Update individual column checkboxes in the list
        this.updateColumnCheckboxesInList(columnList);
    }

    /**
     * Check if all columns in the list are currently visible
     */
    areAllColumnsInListVisible(columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        let visibleCount = 0;

        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                const column = this.processedColumns.find(col => col.field === field);
                if (column && !column.hide) {
                    visibleCount++;
                }
            }
        });

        return visibleCount === columnItems.length;
    }

    /**
     * Hide all columns that are in the list
     */
    hideAllColumnsInList(columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                this.setColumnVisible(field, false);
            }
        });
    }

    /**
     * Show all columns that are in the list
     */
    showAllColumnsInList(columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                this.setColumnVisible(field, true);
            }
        });
    }

    /**
     * Update individual column checkboxes in the list
     */
    updateColumnCheckboxesInList(columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                const checkbox = item.querySelector('.checkbox-icon');
                if (checkbox) {
                    const column = this.processedColumns.find(col => col.field === field);
                    const isVisible = column && !column.hide;
                    checkbox.src = isVisible ? './assets/checkbox-ic.svg' : './assets/uncheckedbox-ic.svg';
                    checkbox.alt = isVisible ? 'Checked' : 'Unchecked';
                }
            }
        });
    }

    /**
     * Update Select All state in visibility tab
     */
    updateSelectAllStateInVisibilityTab() {
        const visibilityTab = document.querySelector('[data-tab-panel="visibility"]');
        if (!visibilityTab) return;

        const columnList = visibilityTab.querySelector('.column-list');
        const selectAllCheckbox = visibilityTab.querySelector('.select-all-item .checkbox-icon');
        if (selectAllCheckbox && columnList) {
            this.updateSelectAllStateInList(selectAllCheckbox, columnList);
        }
    }

    /**
     * Update Select All state based on columns in the list
     */
    updateSelectAllStateInList(selectAllCheckbox, columnList) {
        const columnItems = columnList.querySelectorAll('.column-item:not(.select-all-item)');
        let visibleCount = 0;

        columnItems.forEach(item => {
            const field = item.getAttribute('data-field');
            if (field) {
                const column = this.processedColumns.find(col => col.field === field);
                if (column && !column.hide) {
                    visibleCount++;
                }
            }
        });

        if (visibleCount === 0) {
            selectAllCheckbox.src = './assets/uncheckedbox-ic.svg';
            selectAllCheckbox.alt = 'Unchecked';
        } else if (visibleCount === columnItems.length) {
            selectAllCheckbox.src = './assets/checkbox-ic.svg';
            selectAllCheckbox.alt = 'Checked';
        } else {
            selectAllCheckbox.src = './assets/indeterminate-ic.svg';
            selectAllCheckbox.alt = 'Indeterminate';
        }
    }

    /**
     * Determine if a column is fixed (non-draggable/non-reorderable)
     */
    isFixedColumn(field) {
        return ['checkbox', 'preview', 'actions', 'marketplace'].includes(field);
    }

    /**
     * Helper methods for layout dropdown functionality (from old grid)
     */
    updateLayoutSelection(selectedElement) {
        const dropdown = selectedElement.closest('.layout-dropdown');
        if (!dropdown) return;

        const menu = dropdown.querySelector('.dropdown-menu');
        const layoutItems = menu.querySelectorAll('.layout-item');

        // Remove selected class from all layout items
        layoutItems.forEach(item => {
            item.classList.remove('selected');
            const checkIcon = item.querySelector('.check-icon');
            if (checkIcon) {
                checkIcon.classList.add('hidden');
            }
        });

        // Add selected class to the clicked item
        selectedElement.classList.add('selected');
        const checkIcon = selectedElement.querySelector('.check-icon');
        if (checkIcon) {
            checkIcon.classList.remove('hidden');
        }
    }

    /**
     * Update layout dropdown header text (from old grid)
     */
    updateLayoutDropdownHeader(layoutName) {
        const layoutDropdown = this.elements.layoutDropdown;
        if (!layoutDropdown) return;

        const headerSpan = layoutDropdown.querySelector('.dropdown-header span');
        if (headerSpan) {
            headerSpan.textContent = layoutName;
        }

        // Save current layout to localStorage
        this.saveCurrentLayoutSelection(layoutName);
    }

    /**
     * Save current layout selection to localStorage (from old grid)
     */
    saveCurrentLayoutSelection(layoutName) {
        try {
            localStorage.setItem('snapGrid_currentLayout', layoutName);
            console.log('💾 Saved current layout selection:', layoutName);
        } catch (error) {
            console.error('❌ Failed to save current layout selection:', error);
        }
    }

    updateLayoutDropdown() {
        const layoutDropdown = this.elements.layoutDropdown;
        if (!layoutDropdown) return;

        const menu = layoutDropdown.querySelector('.dropdown-menu');
        if (!menu) return;

        // Clear existing layout items (keep Default Layout, divider, Save Layout, Delete Selected)
        const existingLayoutItems = menu.querySelectorAll('.dropdown-item[data-layout-id]');
        existingLayoutItems.forEach(item => item.remove());

        // Get saved layouts
        const savedLayouts = this.getSavedLayouts();

        // Ensure Custom option visibility based on state/storage and position it
        const customOption = menu.querySelector('.dropdown-item.layout-item[data-value="custom"]');
        const defaultOption = menu.querySelector('.dropdown-item.layout-item[data-value="default"]');
        if (customOption && defaultOption) {
            const hasSavedCustom = savedLayouts.some(layout => (layout.name || '').toLowerCase() === 'custom');
            const shouldShowCustom = (this.currentLayoutType === 'custom') || hasSavedCustom;
            if (shouldShowCustom) {
                // Make Custom visible and move it to be immediately after Default
                customOption.style.display = '';
                customOption.removeAttribute('data-hidden');
                if (customOption.nextSibling !== defaultOption.nextSibling) {
                    defaultOption.parentNode.insertBefore(customOption, defaultOption.nextSibling);
                }
            } else {
                // Hide Custom option when there is no saved Custom and it's not the active layout
                customOption.style.display = 'none';
                customOption.setAttribute('data-hidden', 'true');
            }
        }

        // Filter out Custom layouts from saved layouts to prevent duplication
        // The built-in Custom option handles Custom layout functionality
        const nonCustomLayouts = savedLayouts.filter(layout =>
            !(layout.name && layout.name.toLowerCase() === 'custom')
        );

        // Find the divider to insert layouts before it (use correct separator class)
        const divider = menu.querySelector('.dropdown-separator');
        if (divider && nonCustomLayouts.length > 0) {
            // Insert saved layouts before the divider (excluding Custom layouts)
            nonCustomLayouts.forEach(layout => {
                const layoutItem = this.createLayoutMenuItem(layout);
                divider.parentNode.insertBefore(layoutItem, divider);
            });
        }

        console.log('✅ Layout dropdown updated with', nonCustomLayouts.length, 'saved layouts');
    }

    createLayoutMenuItem(layout) {
        const item = document.createElement('div');
        item.className = 'dropdown-item layout-item';
        item.setAttribute('data-value', layout.id);
        item.setAttribute('data-layout-id', layout.id);

        // Create icon container
        const iconContainer = document.createElement('div');
        iconContainer.className = 'item-icon';
        iconContainer.style.width = '16px';
        iconContainer.style.height = '16px';
        iconContainer.style.marginRight = '8px';

        // Create checkbox icon (initially hidden) - matching old grid implementation
        const icon = document.createElement('img');
        icon.src = 'assets/checked-option-ic.svg';
        icon.alt = 'Selected';
        icon.className = 'check-icon hidden';
        icon.style.width = '12px';
        icon.style.height = '12px';
        iconContainer.appendChild(icon);

        // Create text container
        const textContainer = document.createElement('div');
        textContainer.className = 'item-text';
        textContainer.textContent = layout.name;

        item.appendChild(iconContainer);
        item.appendChild(textContainer);

        // Add click handler
        item.addEventListener('click', (e) => {
            e.stopPropagation();
            this.handleLayoutDropdownChange(layout.id);

            // Close dropdown
            const layoutDropdown = this.elements.layoutDropdown;
            if (layoutDropdown) {
                const dropdown = layoutDropdown.querySelector('.dropdown-menu');
                if (dropdown) {
                    dropdown.classList.add('hidden');
                }
                layoutDropdown.classList.remove('focused');
            }
            // Ensure ALL dropdowns lose focus when an item is selected
            document.querySelectorAll('.snap-dropdown.focused').forEach(d => {
                d.classList.remove('focused');
                const m = d.querySelector('.dropdown-menu');
                if (m) m.classList.add('hidden');
            });

            // When selecting a saved layout, do not overwrite Custom immediately; wait for further user changes
        });

        return item;
    }

    updateCurrentLayoutSelection() {
        // Placeholder for updating current layout selection
        console.log('Updating current layout selection');
    }

    /**
     * Store the original column state for Default Layout restoration
     */
    storeOriginalColumnState() {
        this.originalColumnState = this.processedColumns.map((col, idx) => ({
            field: col.field,
            width: col.width,
            pinned: col.pinned,
            hide: col.hide || false,
            hidden: col.hidden || false,
            order: idx
        }));
        console.log('📋 Stored original column state:', this.originalColumnState);
    }

    /**
     * Enhanced ensureColumnVisible method for quick filters
     * Shows hidden columns when quick filters are applied
     */
    ensureColumnVisible(fieldName) {
        console.log(`🔍 Ensuring column '${fieldName}' is visible`);

        // Find the column and make sure it's visible
        const column = this.processedColumns.find(col => col.field === fieldName);
        if (!column) {
            console.warn(`⚠️ Column '${fieldName}' not found`);
            return false;
        }

        let wasHidden = false;

        // Check both hide and hidden properties for compatibility
        if (column.hidden || column.hide) {
            console.log(`👁️ Column '${fieldName}' was hidden, making it visible`);
            column.hidden = false;
            column.hide = false;
            wasHidden = true;
        }

        // If column was hidden and we're on default layout, normally we'd switch to custom.
        // However, during a Quick Filter session or any suppressed-persist flow, remain on Default baseline.
        if (wasHidden && this.currentLayoutType === 'default') {
            if (this.isQuickFilterActive || this._suppressCustomPersist) {
                console.log('⏭️ Skipping switch to Custom: Quick Filter/suppressed persist active; remain on Default baseline');
            } else {
                console.log('🔄 Switching from Default to Custom layout due to column visibility change');
                this.switchToCustomLayout();
            }
        }

        // Re-render if column visibility changed
        if (wasHidden) {
            this.renderHeader();
            this.renderRows();
            console.log(`✅ Column '${fieldName}' is now visible`);
        }

        // Persist as Custom when visibility changes via ensureColumnVisible
        // Never persist during Quick Filters or when suppression is active
        if (!this._suppressCustomPersist && !this.isQuickFilterActive) {
            this.onUserCustomized && this.onUserCustomized('visibility');
        }
        return true;
    }

    /**
     * Switch from Default layout to Custom layout
     */
    switchToCustomLayout() {
        // Allow switching to Custom from Default or any Saved layout
        if (this.currentLayoutType === 'custom') {
            return; // Already on custom
        }

        this.currentLayoutType = 'custom';

        // Show the Custom layout option in dropdown
        this.showCustomLayoutOption();

        // Update the layout dropdown to show "Custom" instead of "Default Layout"
        this.updateLayoutDropdownSelection('custom');

        // Update dropdown states (disable filters dropdown when Custom layout is active)
        this.updateFilterState();

        // Save current layout to localStorage as "Custom" layout
        this.saveOrUpdateCustomLayout();

        // Make sure Custom is shown and positioned immediately after Default
        const layoutDropdownRoot = this.elements?.layoutDropdown?.querySelector('.snap-dropdown');
        const menu = layoutDropdownRoot?.querySelector('.dropdown-menu');
        if (menu) {
            const customOption = menu.querySelector('.dropdown-item.layout-item[data-value="custom"]');
            const defaultOption = menu.querySelector('.dropdown-item.layout-item[data-value="default"]');
            if (customOption && defaultOption) {
                customOption.style.display = '';
                customOption.removeAttribute('data-hidden');
                defaultOption.parentNode.insertBefore(customOption, defaultOption.nextSibling);
            }
        }

        console.log('🎨 Switched to Custom layout');
    }

    /**
     * Save or update the Custom layout in localStorage
     */
    saveOrUpdateCustomLayout() {
        console.log('💾 Saving Custom Layout to localStorage');

        const baseLayout = this.getCurrentLayout();
        // Do NOT include sort state for Custom per requirement
        if (baseLayout && typeof baseLayout === 'object') {
            if ('sortState' in baseLayout) {
                baseLayout.sortState = {};
            }
            // Always include current filters in Custom
            baseLayout.includeFilters = true;
            baseLayout.filters = { ...this.filterState };
            // Persist checkbox selection state (serialize Sets to arrays)
            const cs = this.checkboxState || {};
            baseLayout.checkboxState = Object.fromEntries(Object.entries(cs).map(([field, setOrArr]) => {
                const arr = Array.isArray(setOrArr) ? setOrArr : Array.from(setOrArr || []);
                return [field, arr];
            }));
            // Persist total counts used for proper isFilterActive evaluation
            baseLayout.checkboxAllValueCounts = { ...(this._checkboxAllValueCounts || {}) };
        }

        const savedLayouts = this.getSavedLayouts();

        // Remove any existing Custom layout
        const filteredLayouts = savedLayouts.filter(layout =>
            !(layout.name && layout.name.toLowerCase() === 'custom')
        );

        // Add/replace the single Custom layout entry
        const customLayout = {
            id: this._customLayoutId || 'custom-' + Date.now(),
            name: 'Custom',
            layout: baseLayout,
            timestamp: Date.now()
        };
        this._customLayoutId = customLayout.id;

        filteredLayouts.push(customLayout);

        // Save to localStorage
        localStorage.setItem('snapGrid_savedLayouts', JSON.stringify(filteredLayouts));

        console.log('✅ Custom Layout saved to localStorage');
    }

    /**
     * Clear the Custom layout from localStorage
     */
    clearCustomLayoutFromStorage() {
        console.log('🗑️ Clearing Custom Layout from localStorage');

        const savedLayouts = this.getSavedLayouts();

        // Remove any existing Custom layout
        const filteredLayouts = savedLayouts.filter(layout =>
            !(layout.name && layout.name.toLowerCase() === 'custom')
        );

        // Save the filtered layouts back to localStorage
        localStorage.setItem('snapGrid_savedLayouts', JSON.stringify(filteredLayouts));

        console.log('✅ Custom Layout cleared from localStorage');
    }

    /**
     * Show the Custom layout option in the dropdown
     */
    showCustomLayoutOption() {
        const layoutDropdown = this.elements?.layoutDropdown?.querySelector('.snap-dropdown');
        if (layoutDropdown) {
            const customOption = layoutDropdown.querySelector('[data-value="custom"]');
            if (customOption) {
                customOption.style.display = '';
                customOption.removeAttribute('data-hidden');
            }
        }
    }

    /**
     * Hide the Custom layout option in the dropdown
     */
    hideCustomLayoutOption() {
        const layoutDropdown = this.elements?.layoutDropdown?.querySelector('.snap-dropdown');
        if (layoutDropdown) {
            const customOption = layoutDropdown.querySelector('[data-value="custom"]');
            if (customOption) {
                customOption.style.display = 'none';
                customOption.setAttribute('data-hidden', 'true');
            }
        }
    }

    /**
     * Update layout dropdown selection
     */
    updateLayoutDropdownSelection(layoutValue) {
        const layoutDropdown = this.elements?.layoutDropdown?.querySelector('.snap-dropdown');
        if (!layoutDropdown) return;

        const headerSpan = layoutDropdown.querySelector('.dropdown-header span');
        const menu = layoutDropdown.querySelector('.dropdown-menu');

        if (!headerSpan || !menu) return;

        // Update header text
        let headerText = 'Default Layout';
        if (layoutValue === 'custom') {
            headerText = 'Custom';
        } else if (layoutValue === 'default') {
            headerText = 'Default Layout';
        } else if (layoutValue.startsWith('layout_')) {
            // Find saved layout name
            const savedLayouts = this.getSavedLayouts();
            const layout = savedLayouts.find(l => l.id === layoutValue);
            if (layout) {
                headerText = layout.name;
            }
        }

        headerSpan.textContent = headerText;

        // Update selected state in menu
        menu.querySelectorAll('.dropdown-item.layout-item').forEach(item => {
            const itemValue = item.getAttribute('data-value');
            const checkIcon = item.querySelector('.check-icon');

            if (itemValue === layoutValue) {
                item.classList.add('selected');
                if (checkIcon) checkIcon.classList.remove('hidden');
            } else {
                item.classList.remove('selected');
                if (checkIcon) checkIcon.classList.add('hidden');
            }
        });

        // Do not trigger save from here to avoid recursion
    }

    applyDefaultLayout() {
        console.log('🎨 Applying default layout - restoring original state');

        // Clear any current selection when toggling Default Layout
        if (this.selectedRows && this.selectedRows.size > 0) {
            this.clearSelection();
        }

        // Reorder columns back to the original order
        if (Array.isArray(this.originalColumnState) && this.originalColumnState.length) {
            const orderMap = new Map(this.originalColumnState.map((c, i) => [c.field, i]));
            this.processedColumns.sort((a, b) => {
                const ai = orderMap.has(a.field) ? orderMap.get(a.field) : Number.MAX_SAFE_INTEGER;
                const bi = orderMap.has(b.field) ? orderMap.get(b.field) : Number.MAX_SAFE_INTEGER;
                return ai - bi;
            });
        }

        // Restore original column state
        if (this.originalColumnState) {
            this.processedColumns.forEach((column) => {
                const originalCol = this.originalColumnState.find(orig => orig.field === column.field);
                if (originalCol) {
                    column.width = originalCol.width;
                    column.hide = originalCol.hide;
                    column.hidden = originalCol.hidden;
                } else {
                    // Fallback sane defaults if no original entry
                    column.hidden = false;
                    column.hide = false;
                }
                // Default layout pinning rules:
                // - checkbox and preview must remain pinned-left
                // - actions must remain pinned-right
                // - all other columns are unpinned
                if (column.field === 'checkbox' || column.field === 'preview') {
                    column.pinned = 'left';
                } else if (column.field === 'actions') {
                    column.pinned = 'right';
                } else {
                    column.pinned = undefined;
                }
            });
            // Ensure pinned groups are properly ordered after reset
            this.sortColumnsByPinned();
        } else {
            // Fallback: show all columns if no original state stored
            this.processedColumns.forEach(column => {
                column.hidden = false;
                column.hide = false;
            });
        }

        // Clear all filters (suppress persisting this clear into Custom)
        // Preserve previous suppression flag so callers (e.g., Quick Filters) remain in suppressed mode.
        const __prevSuppress = !!this._suppressCustomPersist;
        this._suppressCustomPersist = true;
        this.clearFilters();
        this._suppressCustomPersist = __prevSuppress;

        // Clear any active Quick Filters state and ensure dropdown shows default text
        if (this.activeQuickFilters && this.activeQuickFilters.size > 0) {
            this.activeQuickFilters.clear();
        }

        // Hide custom layout option since we're back to default
        this.hideCustomLayoutOption();

        // Update current layout type
        this.currentLayoutType = 'default';
        this.isSavedLayoutActive = false;

        // Update layout dropdown header to show "Default Layout"
        this.updateLayoutDropdownHeader('Default Layout');

        // Update dropdown selection to show checkbox on Default Layout
        this.updateCurrentLayoutSelection();

        // Re-render the grid
        this.renderHeader();
        this.renderRows();

        // Ensure header-body synchronization after layout changes
        this.syncHorizontalScroll();

        // Update filter state
        this.updateFilterState();

        console.log('✅ Default layout applied - restored to original state');

        // When switching back to Default, do not overwrite Custom, but ensure dropdown stays correct
        this.hideCustomLayoutOption();
    }

    /**
     * Update layout dropdown header text (from old grid)
     */
    updateLayoutDropdownHeader(layoutName) {
        const layoutDropdown = this.elements.layoutDropdown;
        if (!layoutDropdown) return;

        const headerSpan = layoutDropdown.querySelector('.dropdown-header span');
        if (headerSpan) {
            headerSpan.textContent = layoutName;
        }

        // Save current layout to localStorage
        this.saveCurrentLayoutSelection(layoutName);
    }

    /**
     * Save current layout selection to localStorage (from old grid)
     */
    saveCurrentLayoutSelection(layoutName) {
        try {
            localStorage.setItem('snapGrid_currentLayout', layoutName);
            console.log('💾 Saved current layout selection:', layoutName);
        } catch (error) {
            console.error('❌ Failed to save current layout selection:', error);
        }
    }

    /**
     * Update layout selection in dropdown (show checkbox only on selected item) (from old grid)
     */
    updateLayoutSelection(selectedItem) {
        const layoutDropdown = this.elements.layoutDropdown;
        if (!layoutDropdown) return;

        const menu = layoutDropdown.querySelector('.dropdown-menu');
        if (!menu) return;

        // Remove selection from all layout items
        const allLayoutItems = menu.querySelectorAll('.layout-item');
        allLayoutItems.forEach(item => {
            item.classList.remove('selected');
            const checkIcon = item.querySelector('.check-icon');
            if (checkIcon) {
                checkIcon.classList.add('hidden');
            }
        });

        // Add selection to the selected item
        if (selectedItem) {
            selectedItem.classList.add('selected');
            const checkIcon = selectedItem.querySelector('.check-icon');
            if (checkIcon) {
                checkIcon.classList.remove('hidden');
            }
        }
    }

    /**
     * Update dropdown selection to match current layout (from old grid)
     */
    updateCurrentLayoutSelection() {
        const layoutDropdown = this.elements.layoutDropdown;
        if (!layoutDropdown) return;

        const headerSpan = layoutDropdown.querySelector('.dropdown-header span');
        const currentLayoutName = headerSpan ? headerSpan.textContent : '';

        console.log('🔄 Updating dropdown selection for current layout:', currentLayoutName);

        const menu = layoutDropdown.querySelector('.dropdown-menu');
        if (!menu) return;

        // Find the item that matches the current layout name
        let selectedItem = null;

        if (currentLayoutName === 'Default Layout') {
            selectedItem = menu.querySelector('[data-value="default"]');
        } else {
            // Find saved layout by name
            const savedLayouts = this.getSavedLayouts();
            const savedLayout = savedLayouts.find(layout => layout.name === currentLayoutName);
            if (savedLayout) {
                selectedItem = menu.querySelector(`[data-layout-id="${savedLayout.id}"]`);
            }
        }

        if (selectedItem) {
            console.log('✅ Found matching item, updating selection');
            this.updateLayoutSelection(selectedItem);
        } else {
            console.log('⚠️ Could not find item for current layout:', currentLayoutName);
        }
    }

    deleteSelectedLayout() {
        console.log('🗑️ Delete Selected clicked');

        // Get current layout name from dropdown header
        const layoutDropdown = this.elements.layoutDropdown;
        if (!layoutDropdown) {
            console.error('❌ Layout dropdown not found');
            return;
        }

        const headerSpan = layoutDropdown.querySelector('.dropdown-header span');
        const currentLayoutName = headerSpan ? headerSpan.textContent : '';

        console.log('📋 Current layout:', currentLayoutName);

        // Check if trying to delete Default Layout
        if (currentLayoutName === 'Default Layout') {
            this.showNotification('This layout cannot be deleted. The Default Layout is protected and cannot be removed.', 'warning');
            console.log('⚠️ Attempted to delete Default Layout - blocked');
            return;
        }

        // Check if it's a saved layout
        if (currentLayoutName && currentLayoutName !== 'Default Layout') {
            // Show confirmation dialog
            const confirmed = confirm(`Are you sure you want to delete "${currentLayoutName}"?\n\nThis action cannot be undone.`);

            if (confirmed) {
                console.log('✅ User confirmed deletion of layout:', currentLayoutName);
                this.performDeleteLayout(currentLayoutName);
            } else {
                console.log('❌ User cancelled deletion');
            }
        } else {
            console.log('⚠️ No valid layout selected for deletion');
        }
    }

    /**
     * Perform the actual deletion of a saved layout
     */
    performDeleteLayout(layoutName) {
        try {
            // Special handling for 'Custom' — even if not found in storage, clear and reset
            if ((layoutName || '').toLowerCase() === 'custom') {
                this.clearCustomLayoutFromStorage();
                this.updateLayoutDropdown();
                this.applyDefaultLayout();
                this.showNotification('Custom layout has been cleared and reset to Default.', 'success');
                return;
            }

            // Get saved layouts
            const savedLayouts = this.getSavedLayouts();

            // Find the layout to delete
            const layoutIndex = savedLayouts.findIndex(layout => layout.name === layoutName);

            if (layoutIndex === -1) {
                console.error('❌ Layout not found:', layoutName);
                this.showNotification('Layout not found. It may have already been deleted.', 'warning');
                return;
            }

            // Remove the layout
            const deletedLayout = savedLayouts.splice(layoutIndex, 1)[0];

            // Save updated layouts to localStorage
            localStorage.setItem('snapGrid_savedLayouts', JSON.stringify(savedLayouts));

            console.log('✅ Layout deleted successfully:', deletedLayout);

            // Update the dropdown to remove the deleted layout
            this.updateLayoutDropdown();

            // If we were on the deleted layout, switch to Default Layout
            const headerSpan = this.elements.layoutDropdown.querySelector('.dropdown-header span');
            if (headerSpan && headerSpan.textContent === layoutName) {
                console.log('🔄 Switching to Default Layout after deletion');
                this.applyDefaultLayout();
            }

            // Show success message
            this.showNotification(`Layout "${layoutName}" has been deleted successfully.`, 'success');

        } catch (error) {
            console.error('❌ Error deleting layout:', error);
            this.showNotification('Failed to delete layout. Please try again.', 'warning');
        }
    }

    /**
     * Show notification message (Snap Image Studio style)
     */
    showNotification(message, type = 'warning') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        // Use fixed positioning, so we can safely append to document body
        document.body.appendChild(notification);

        // Remove notification after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);

        return notification;
    }

    // Removed duplicate method - using the localStorage implementation below

    applyLayout(layout) {
        console.log('🎨 Applying layout:', layout);

        // Apply a specific layout
        if (typeof this.options.onApplyLayout === 'function') {
            this.options.onApplyLayout(layout);
            return;
        }

        // Default implementation: apply layout from saved layouts
        let layoutData = layout;

        // If layout is a string (layout ID), get the layout data from storage
        if (typeof layout === 'string') {
            const savedLayouts = this.getSavedLayouts();
            const foundLayout = savedLayouts.find(l => l.id === layout);
            if (!foundLayout) {
                console.error('❌ Layout not found:', layout);
                return;
            }
            layoutData = foundLayout;
        }

        console.log('🎨 Applying layout data:', layoutData);

        // Apply column configuration if available
        if (layoutData.columns && Array.isArray(layoutData.columns)) {
            // Update column widths and order
            layoutData.columns.forEach(layoutCol => {
                const column = this.processedColumns.find(col => col.field === layoutCol.field);
                if (column) {
                    if (layoutCol.width) {
                        column.width = layoutCol.width;
                    }
                    if (layoutCol.pinned !== undefined) {
                        if (column.field === 'checkbox' || column.field === 'preview') {
                            column.pinned = 'left';
                        } else if (column.field === 'actions') {
                            column.pinned = 'right';
                        } else {
                            column.pinned = layoutCol.pinned;
                        }
                    }
                }
            });

            // Reorder columns to match layout order
            const orderedColumns = [];
            layoutData.columns.forEach(layoutCol => {
                const column = this.processedColumns.find(col => col.field === layoutCol.field);
                if (column) {
                    orderedColumns.push(column);
                }
            });

            // Add any columns not in the layout to the end
            this.processedColumns.forEach(column => {
                if (!orderedColumns.find(col => col.field === column.field)) {
                    orderedColumns.push(column);
                }
            });

            this.processedColumns = orderedColumns;
        }

        // Apply hidden state if provided
        if (layoutData.hidden && typeof layoutData.hidden === 'object') {
            this.processedColumns.forEach(column => {
                if (Object.prototype.hasOwnProperty.call(layoutData.hidden, column.field)) {
                    column.hide = !!layoutData.hidden[column.field];
                }
            });
        }

        // Apply sort state if provided
        if (layoutData.sortState && typeof layoutData.sortState === 'object') {
            this.sortState = { ...layoutData.sortState };
        } else {
            this.sortState = {};
        }

        // Apply filters if included in layout
        if (layoutData.includeFilters && layoutData.filters) {
            console.log('🔍 Applying saved filters:', layoutData.filters);
            this.filterState = { ...layoutData.filters };

            // Also apply checkbox states if available (deserialize arrays to Sets)
            if (layoutData.checkboxState) {
                this.checkboxState = Object.fromEntries(
                  Object.entries(layoutData.checkboxState).map(([field, arr]) => [field, new Set(Array.isArray(arr) ? arr : [])])
                );
            } else {
                this.checkboxState = {};
            }

            // Restore counts used for checkbox filtering/indicators
            if (layoutData.checkboxAllValueCounts) {
                this._checkboxAllValueCounts = { ...layoutData.checkboxAllValueCounts };
            }
        } else {
            // Clear all filters if no filters in layout
            this.clearFilters();
        }

        // Update layout dropdown header to show current layout (fallback to 'Custom' if name missing)
        const displayName = layoutData && layoutData.name ? layoutData.name : 'Custom';
        this.updateLayoutDropdownHeader(displayName);

        // Update dropdown selection to show checkbox on selected layout (from old grid)
        const layoutDropdown = this.elements.layoutDropdown;
        if (layoutDropdown) {
            const menu = layoutDropdown.querySelector('.dropdown-menu');
            if (menu) {
                const selectedItem = layoutValue =>
                  layoutValue ? menu.querySelector(`[data-layout-id="${layoutValue}"]`) : null;
                const item = selectedItem(layoutData.id);
                if (item) {
                    this.updateLayoutSelection(item);
                } else {
                    // If no saved layout item matches (e.g., Custom), select the built-in Custom option
                    const customItem = menu.querySelector('.dropdown-item.layout-item[data-value="custom"]');
                    if (customItem) this.updateLayoutSelection(customItem);
                }
            }
        }

        // Recompute data and re-render to reflect new filters/indicators
        this.processData();
        this.renderHeader();
        this.renderRows();
        this.updateFilterState();

        // Re-render the grid with the new layout
        this.processData();
        this.render();

        console.log('✅ Layout applied successfully');
    }

    /**
     * Get Pacific Time using Snap's timezone utility
     * Falls back to proper Pacific timezone calculation if utility not available
     */
    getPacificTime() {
        if (window.SnapTimezone && typeof window.SnapTimezone.getPacificTime === 'function') {
            return window.SnapTimezone.getPacificTime();
        }
        // Fallback to Pacific timezone calculation
        return this._calculatePacificTime();
    }

    /**
     * Get Pacific Date (midnight) using Snap's timezone utility
     * Falls back to proper Pacific timezone calculation if utility not available
     */
    getPacificDate() {
        if (window.SnapTimezone && typeof window.SnapTimezone.getPacificDate === 'function') {
            return window.SnapTimezone.getPacificDate();
        }
        // Fallback to Pacific timezone calculation
        const pacificTime = this._calculatePacificTime();
        pacificTime.setHours(0, 0, 0, 0);
        return pacificTime;
    }

    /**
     * Calculate Pacific time from UTC
     * Handles PST (-8) and PDT (-7) automatically
     */
    _calculatePacificTime() {
        const now = new Date();

        // Convert to Pacific timezone
        // Pacific timezone is UTC-8 (PST) or UTC-7 (PDT)
        const pacificTime = new Date(now.toLocaleString("en-US", {timeZone: "America/Los_Angeles"}));

        return pacificTime;
    }

    /**
     * Fallback method to check if a date is today (local time)
     */
    isLocalToday(date, today) {
        const itemDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        return itemDate.getTime() === today.getTime();
    }

    saveLayoutWithPrompt() {
        console.log('💾 Save Current Layout clicked');
        this.showSaveLayoutPopup();
    }

    getSaveLayoutPopupHTML() {
        return `
            <div class="popup-overlay" id="saveLayoutPopup">
                <div class="popup-card save-layout-popup">
                    <img src="assets/close-ic.svg" class="popup-close" alt="Close">
                    <h3 class="popup-title">Save New Layout</h3>
                    <div class="popup-form-group">
                        <div class="layout-input-container">
                            <input type="text" id="layoutNameInput" placeholder="Enter layout name">
                        </div>
                        <div class="error-message">This layout name already exists.</div>
                    </div>
                    <button id="saveLayoutButton" class="save-layout-button">
                        <span>Save Layout</span>
                    </button>
                </div>
            </div>
        `;
    }

    showSaveLayoutPopup() {
        // Create popup if it doesn't exist
        let popup = document.getElementById('saveLayoutPopup');
        if (!popup) {
            // Add popup to body
            document.body.insertAdjacentHTML('beforeend', this.getSaveLayoutPopupHTML());
            popup = document.getElementById('saveLayoutPopup');
            this.setupSaveLayoutPopupEvents();
        }

        // Show popup
        popup.style.display = 'flex';

        // Focus input and clear previous values
        const input = document.getElementById('layoutNameInput');
        const errorMsg = popup.querySelector('.error-message');
        const saveBtn = document.getElementById('saveLayoutButton');

        input.value = '';
        errorMsg.style.display = 'none';
        saveBtn.classList.remove('active');

        // Focus input after a short delay
        setTimeout(() => {
            input.focus();
        }, 50);
    }

    hideSaveLayoutPopup() {
        const popup = document.getElementById('saveLayoutPopup');
        if (popup) {
            popup.style.display = 'none';
        }
    }

    setupSaveLayoutPopupEvents() {
        const popup = document.getElementById('saveLayoutPopup');
        if (!popup) return;

        const input = document.getElementById('layoutNameInput');
        const closeBtn = popup.querySelector('.popup-close');
        const saveBtn = document.getElementById('saveLayoutButton');

        // Close button
        closeBtn.addEventListener('click', () => {
            this.hideSaveLayoutPopup();
        });

        // Click outside to close
        popup.addEventListener('click', (e) => {
            if (e.target === popup) {
                this.hideSaveLayoutPopup();
            }
        });

        // Input validation
        input.addEventListener('input', () => {
            this.validateLayoutName();
        });

        // Enter key to save
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && saveBtn.classList.contains('active')) {
                this.performSaveLayout();
            }
        });

        // Save button
        saveBtn.addEventListener('click', () => {
            if (saveBtn.classList.contains('active')) {
                this.performSaveLayout();
            }
        });
    }

    validateLayoutName() {
        const input = document.getElementById('layoutNameInput');
        const inputContainer = input.closest('.layout-input-container');
        const saveBtn = document.getElementById('saveLayoutButton');
        const errorMsg = document.querySelector('#saveLayoutPopup .error-message');

        const name = input.value.trim();
        saveBtn.classList.remove('active');
        errorMsg.style.display = 'none';
        inputContainer.classList.remove('error');

        if (name.length === 0) {
            return;
        }

        // Check if name is too long (max 24 characters)
        if (name.length > 24) {
            errorMsg.textContent = 'Layout name cannot exceed 24 characters';
            errorMsg.style.display = 'block';
            inputContainer.classList.add('error');
            return;
        }

        // Check for duplicate names
        const savedLayouts = this.getSavedLayouts();
        const isDuplicate = savedLayouts.some(layout => layout.name.toLowerCase() === name.toLowerCase());

        if (isDuplicate) {
            errorMsg.textContent = 'This layout name already exists.';
            errorMsg.style.display = 'block';
            inputContainer.classList.add('error');
        } else {
            // Enable the button if name is valid
            saveBtn.classList.add('active');
        }
    }

    performSaveLayout() {
        const input = document.getElementById('layoutNameInput');
        const name = input.value.trim();

        // Get current layout state
        const currentLayout = this.getCurrentLayout();

        // Build full parity layout (match Custom). Exclude sort; always include filters state
        const checkboxStateSerialized = Object.fromEntries(
          Object.entries(this.checkboxState || {}).map(([f, setOrArr]) => [f, Array.isArray(setOrArr) ? setOrArr : Array.from(setOrArr || [])])
        );
        const layout = {
            id: `layout_${Date.now()}`,
            name: name,
            ...currentLayout,
            sortState: {},
            includeFilters: true,
            filters: { ...this.filterState },
            checkboxState: checkboxStateSerialized,
            checkboxAllValueCounts: { ...(this._checkboxAllValueCounts || {}) },
            createdAt: new Date().toISOString()
        };

        // Save to localStorage
        this.saveLayoutToStorage(layout);

        // Update layout dropdown to include the new layout
        this.updateLayoutDropdown();

        // Hide popup
        this.hideSaveLayoutPopup();

        // Show success message
        this.showNotification(`Layout "${layout.name}" has been saved successfully.`, 'success');
        console.log('✅ Layout saved successfully!', layout);
    }

    saveLayoutToStorage(layout) {
        try {
            const savedLayouts = this.getSavedLayouts();
            // If saving Custom by name, replace existing Custom
            if (layout && typeof layout.name === 'string' && layout.name.toLowerCase() === 'custom') {
                const idx = savedLayouts.findIndex(l => (l?.name || '').toLowerCase() === 'custom');
                if (idx >= 0) {
                    layout.id = savedLayouts[idx].id || layout.id || (this._customLayoutId || `layout_custom_${Date.now()}`);
                    savedLayouts[idx] = layout;
                    this._customLayoutId = layout.id;
                } else {
                    layout.id = layout.id || (this._customLayoutId || `layout_custom_${Date.now()}`);
                    savedLayouts.push(layout);
                    this._customLayoutId = layout.id;
                }
            } else {
                savedLayouts.push(layout);
            }
            localStorage.setItem('snapGrid_savedLayouts', JSON.stringify(savedLayouts));
            console.log('💾 Layout saved to localStorage:', layout);
        } catch (error) {
            console.error('❌ Failed to save layout to localStorage:', error);
        }
    }

    getSavedLayouts() {
        // Get saved layouts from localStorage or options
        let fromLocal = [];
        try {
            const saved = localStorage.getItem('snapGrid_savedLayouts');
            fromLocal = saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('Failed to load saved layouts:', error);
            fromLocal = [];
        }

        // Deduplicate and normalize: enforce single Custom
        const deduped = [];
        const seenIds = new Set();
        let customKept = false;
        for (const l of Array.isArray(fromLocal) ? fromLocal : []) {
            if (!l || typeof l !== 'object') continue;
            if (typeof l.name === 'string' && l.name.toLowerCase() === 'custom') {
                if (customKept) continue;
                customKept = true;
                // Normalize id for Custom if we already have a tracked id
                if (this._customLayoutId) l.id = this._customLayoutId;
                else if (!l.id) l.id = `layout_custom_${Date.now()}`;
                this._customLayoutId = l.id;
            }
            const id = l.id || `layout_${Date.now()}_${Math.random().toString(36).slice(2,8)}`;
            if (seenIds.has(id)) continue;
            seenIds.add(id);
            l.id = id;
            deduped.push(l);
        }

        // Persist deduped result back
        try {
            localStorage.setItem('snapGrid_savedLayouts', JSON.stringify(deduped));
        } catch {}

        return deduped;
    }

    // Delete confirmation popup methods
    getDeleteProductPopupHTML(selectedData) {
        const productCount = selectedData.length;

        // Resolve fields based on configured columns
        const findField = (names = []) => {
            const lower = names.map(n => String(n).toLowerCase());
            const col = this.processedColumns.find(c => {
                const f = String(c.field || '').toLowerCase();
                const h = String(c.headerName || '').toLowerCase();
                return lower.includes(f) || lower.includes(h);
            });
            return col?.field || null;
        };
        const salesField = findField(['sales']);
        const reviewsField = findField(['reviews']);
        const royaltiesField = findField(['royalties','royalty','royals']);

        // Safe numeric coercion (handles formatted strings like "1,234")
        const toNumber = (v) => {
            if (typeof v === 'number') return v;
            if (v == null) return 0;
            const n = Number(String(v).replace(/[^0-9.\-]/g, ''));
            return Number.isFinite(n) ? n : 0;
        };

        const getVal = (row, field, fallbacks = []) => {
            if (field && row[field] != null) return row[field];
            for (const k of fallbacks) {
                if (row[k] != null) return row[k];
            }
            return 0;
        };

        const salesSum = selectedData.reduce((sum, row) => sum + toNumber(getVal(row, salesField, ['sales', 'Sales'])), 0);
        const reviewsSum = selectedData.reduce((sum, row) => sum + toNumber(getVal(row, reviewsField, ['reviews', 'Reviews'])), 0);
        const royaltiesSum = selectedData.reduce((sum, row) => sum + toNumber(getVal(row, royaltiesField, ['royalties','Royalties','royalty','Royalty'])), 0);
        const soldProductsCount = selectedData.filter(row => toNumber(getVal(row, salesField, ['sales', 'Sales'])) > 0).length;

        // Format for display
        const fmt = (n) => Number(n || 0).toLocaleString();

        return `
            <div class="popup-overlay" id="deleteProductPopup">
                <div class="popup-card delete-product-popup">
                    <div class="delete-popup-header">
                        <h3 class="delete-popup-title">Delete Product</h3>
                        <img src="assets/close-ic.svg" class="delete-popup-close" alt="Close">
                    </div>

                    <div class="delete-stats-grid">
                        <div class="delete-stat-item">
                            <div class="delete-stat-top">
                                <div class="delete-stat-number">${fmt(productCount)}</div>
                            </div>
                            <div class="delete-stat-label">Products</div>
                        </div>
                        <div class="delete-stat-divider"></div>
                        <div class="delete-stat-item">
                            <div class="delete-stat-top">
                                <div class="delete-stat-number">${fmt(soldProductsCount)}</div>
                            </div>
                            <div class="delete-stat-label">Sold Products</div>
                        </div>
                        <div class="delete-stat-divider"></div>
                        <div class="delete-stat-item">
                            <div class="delete-stat-top">
                                <div class="delete-stat-number">${fmt(salesSum)}</div>
                            </div>
                            <div class="delete-stat-label">Sales</div>
                        </div>
                        <div class="delete-stat-divider"></div>
                        <div class="delete-stat-item">
                            <div class="delete-stat-top">
                                <div class="delete-stat-number">${fmt(royaltiesSum)}</div>
                            </div>
                            <div class="delete-stat-label">Royalties</div>
                        </div>
                        <div class="delete-stat-divider"></div>
                        <div class="delete-stat-item">
                            <div class="delete-stat-top">
                                <div class="delete-stat-number">${fmt(reviewsSum)}</div>
                            </div>
                            <div class="delete-stat-label">Reviews</div>
                        </div>
                    </div>

                    <div class="delete-confirmation-section">
                        <div class="delete-confirmation-label">Type SNAP to confirm</div>
                        <div class="delete-confirmation-input-container">
                            <input type="text" id="deleteProductConfirmInput" placeholder="Type SNAP here" maxlength="4">
                        </div>
                    </div>

                    <div class="delete-warning-section">
                        <img src="assets/warning-ic.svg" class="delete-warning-icon" alt="Warning">
                        <div class="delete-warning-text">This will permanently delete the selected products and data. This cannot be undone.</div>
                    </div>

                    <button id="confirmDeleteProductButton" class="delete-confirm-btn disabled">Confirm & Delete</button>
                </div>
            </div>
        `;
    }

    getDeleteDesignPopupHTML(selectedData) {
        const productCount = selectedData.length;

        // Resolve fields based on configured columns
        const findField = (names = []) => {
            const lower = names.map(n => String(n).toLowerCase());
            const col = this.processedColumns.find(c => {
                const f = String(c.field || '').toLowerCase();
                const h = String(c.headerName || '').toLowerCase();
                return lower.includes(f) || lower.includes(h);
            });
            return col?.field || null;
        };
        const salesField = findField(['sales']);
        const reviewsField = findField(['reviews']);
        const royaltiesField = findField(['royalties','royalty','royals']);
        const designField = findField(['design', 'designid', 'design_id', 'design id', 'design-id']);

        // Safe numeric coercion
        const toNumber = (v) => {
            if (typeof v === 'number') return v;
            if (v == null) return 0;
            const n = Number(String(v).replace(/[^0-9.\-]/g, ''));
            return Number.isFinite(n) ? n : 0;
        };

        const getVal = (row, field, fallbacks = []) => {
            if (field && row[field] != null) return row[field];
            for (const k of fallbacks) {
                if (row[k] != null) return row[k];
            }
            return 0;
        };

        const salesSum = selectedData.reduce((sum, row) => sum + toNumber(getVal(row, salesField, ['sales', 'Sales'])), 0);
        const reviewsSum = selectedData.reduce((sum, row) => sum + toNumber(getVal(row, reviewsField, ['reviews', 'Reviews'])), 0);
        const royaltiesSum = selectedData.reduce((sum, row) => sum + toNumber(getVal(row, royaltiesField, ['royalties','Royalties','royalty','Royalty'])), 0);
        const soldProductsCount = selectedData.filter(row => toNumber(getVal(row, salesField, ['sales', 'Sales'])) > 0).length;

        // Derive design count from unique design field if available; fallback to 1
        let designCount = 1;
        if (designField) {
            const uniq = new Set(selectedData.map(r => String(getVal(r, designField, ['design', 'Design']))));
            designCount = uniq.size || 1;
        }

        const fmt = (n) => Number(n || 0).toLocaleString();

        return `
            <div class="popup-overlay" id="deleteDesignPopup">
                <div class="popup-card delete-product-popup">
                    <div class="delete-popup-header">
                        <h3 class="delete-popup-title">Delete Design</h3>
                        <img src="assets/close-ic.svg" class="delete-popup-close" alt="Close">
                    </div>

                    <div class="delete-stats-grid">
                        <div class="delete-stat-item">
                            <div class="delete-stat-top">
                                <div class="delete-stat-number">${fmt(designCount)}</div>
                            </div>
                            <div class="delete-stat-label">${designCount === 1 ? 'Design' : 'Designs'}</div>
                        </div>
                        <div class="delete-stat-divider"></div>
                        <div class="delete-stat-item">
                            <div class="delete-stat-top">
                                <div class="delete-stat-number">${fmt(productCount)}</div>
                            </div>
                            <div class="delete-stat-label">Products</div>
                        </div>
                        <div class="delete-stat-divider"></div>
                        <div class="delete-stat-item">
                            <div class="delete-stat-top">
                                <div class="delete-stat-number">${fmt(soldProductsCount)}</div>
                            </div>
                            <div class="delete-stat-label">Sold Products</div>
                        </div>
                        <div class="delete-stat-divider"></div>
                        <div class="delete-stat-item">
                            <div class="delete-stat-top">
                                <div class="delete-stat-number">${fmt(salesSum)}</div>
                            </div>
                            <div class="delete-stat-label">Sales</div>
                        </div>
                        <div class="delete-stat-divider"></div>
                        <div class="delete-stat-item">
                            <div class="delete-stat-top">
                                <div class="delete-stat-number">${fmt(royaltiesSum)}</div>
                            </div>
                            <div class="delete-stat-label">Royalties</div>
                        </div>
                        <div class="delete-stat-divider"></div>
                        <div class="delete-stat-item">
                            <div class="delete-stat-top">
                                <div class="delete-stat-number">${fmt(reviewsSum)}</div>
                            </div>
                            <div class="delete-stat-label">Reviews</div>
                        </div>
                    </div>

                    <div class="delete-confirmation-section">
                        <div class="delete-confirmation-label">Type SNAP to confirm</div>
                        <div class="delete-confirmation-input-container">
                            <input type="text" id="deleteDesignConfirmInput" placeholder="Type SNAP here" maxlength="4">
                        </div>
                    </div>

                    <div class="delete-warning-section">
                        <img src="assets/warning-ic.svg" class="delete-warning-icon" alt="Warning">
                        <div class="delete-warning-text">Permanently delete selected products and data.. This action cannot be undone.</div>
                    </div>

                    <button id="confirmDeleteDesignButton" class="delete-confirm-btn disabled">Confirm & Delete</button>
                </div>
            </div>
        `;
    }

    showDeleteProductPopup(selectedData) {
        // Create popup if it doesn't exist
        let popup = document.getElementById('deleteProductPopup');
        if (!popup) {
            // Add popup to body
            document.body.insertAdjacentHTML('beforeend', this.getDeleteProductPopupHTML(selectedData));
            popup = document.getElementById('deleteProductPopup');
            this.setupDeleteProductPopupEvents(selectedData);
        }

        // Show popup
        popup.style.display = 'flex';

        // Focus input
        const input = document.getElementById('deleteProductConfirmInput');
        setTimeout(() => {
            input.focus();
        }, 50);
    }

    showDeleteDesignPopup(selectedData) {
        // Create popup if it doesn't exist
        let popup = document.getElementById('deleteDesignPopup');
        if (!popup) {
            // Add popup to body
            document.body.insertAdjacentHTML('beforeend', this.getDeleteDesignPopupHTML(selectedData));
            popup = document.getElementById('deleteDesignPopup');
            this.setupDeleteDesignPopupEvents(selectedData);
        }

        // Show popup
        popup.style.display = 'flex';

        // Focus input
        const input = document.getElementById('deleteDesignConfirmInput');
        setTimeout(() => {
            input.focus();
        }, 50);
    }

    hideDeleteProductPopup() {
        const popup = document.getElementById('deleteProductPopup');
        if (popup) {
            popup.style.display = 'none';
            popup.remove(); // Remove from DOM to reset state
        }
    }

    hideDeleteDesignPopup() {
        const popup = document.getElementById('deleteDesignPopup');
        if (popup) {
            popup.style.display = 'none';
            popup.remove(); // Remove from DOM to reset state
        }
    }

    setupDeleteProductPopupEvents(selectedData) {
        const popup = document.getElementById('deleteProductPopup');
        if (!popup) return;

        const input = document.getElementById('deleteProductConfirmInput');
        const closeBtn = popup.querySelector('.delete-popup-close');
        const confirmBtn = document.getElementById('confirmDeleteProductButton');

        // Close button
        closeBtn.addEventListener('click', () => {
            this.hideDeleteProductPopup();
        });

        // Click outside to close
        popup.addEventListener('click', (e) => {
            if (e.target === popup) {
                this.hideDeleteProductPopup();
            }
        });

        // Input validation
        input.addEventListener('input', () => {
            this.validateDeleteConfirmation('deleteProductConfirmInput', 'confirmDeleteProductButton');
        });

        // Confirm button
        confirmBtn.addEventListener('click', () => {
            if (input.value.trim().toUpperCase() === 'SNAP') {
                this.hideDeleteProductPopup();
                // Execute original delete product callback
                if (typeof this.options.onDeleteProduct === 'function') {
                    this.options.onDeleteProduct(selectedData);
                } else {
                    console.log('Delete Product action confirmed for:', selectedData);
                }
            }
        });

        // Enter key to confirm
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && input.value.trim().toUpperCase() === 'SNAP') {
                confirmBtn.click();
            }
        });
    }

    setupDeleteDesignPopupEvents(selectedData) {
        const popup = document.getElementById('deleteDesignPopup');
        if (!popup) return;

        const input = document.getElementById('deleteDesignConfirmInput');
        const closeBtn = popup.querySelector('.delete-popup-close');
        const confirmBtn = document.getElementById('confirmDeleteDesignButton');

        // Close button
        closeBtn.addEventListener('click', () => {
            this.hideDeleteDesignPopup();
        });

        // Click outside to close
        popup.addEventListener('click', (e) => {
            if (e.target === popup) {
                this.hideDeleteDesignPopup();
            }
        });

        // Input validation
        input.addEventListener('input', () => {
            this.validateDeleteConfirmation('deleteDesignConfirmInput', 'confirmDeleteDesignButton');
        });

        // Confirm button
        confirmBtn.addEventListener('click', () => {
            if (input.value.trim().toUpperCase() === 'SNAP') {
                this.hideDeleteDesignPopup();
                // Execute original delete design callback
                if (typeof this.options.onDeleteDesign === 'function') {
                    this.options.onDeleteDesign(selectedData);
                } else {
                    console.log('Delete Design action confirmed for:', selectedData);
                }
            }
        });

        // Enter key to confirm
        input.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && input.value.trim().toUpperCase() === 'SNAP') {
                confirmBtn.click();
            }
        });
    }

    validateDeleteConfirmation(inputId, buttonId) {
        const input = document.getElementById(inputId);
        const confirmBtn = document.getElementById(buttonId);

        const isValid = input.value.trim().toUpperCase() === 'SNAP';

        if (isValid) {
            confirmBtn.classList.remove('disabled');
            confirmBtn.classList.add('active');
        } else {
            confirmBtn.classList.add('disabled');
            confirmBtn.classList.remove('active');
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SnapGrid;
} else if (typeof window !== 'undefined') {
    window.SnapGrid = SnapGrid;
}
