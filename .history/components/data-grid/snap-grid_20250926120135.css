/**
 * SnapGrid CSS Styles
 * Comprehensive styling for the data grid component
 * Following the design system patterns from snap-charts.css and tokens.css
 *
 * Features:
 * - Responsive design with mobile support
 * - Dark mode compatibility
 * - Virtual scrolling optimizations
 * - Performance optimizations
 * - Multiple themes (default, compact, dense, comfortable)
 *
 * @version 1.0.0
 */

/* ==========================================================================
   CSS Variables and Tokens
   ========================================================================== */

.snap-grid {
    /* Core grid variables using design tokens */
    --grid-hscroll-height: 14px;
    --grid-border-color: var(--border-color);
    --grid-header-bg: var(--bg-secondary);
    --grid-row-bg: var(--bg-primary);
    --grid-row-hover-bg: var(--btn-hover);
    --grid-row-selected-bg: var(--btn-hover);
    --grid-cell-padding: var(--spacing-sm, 12px);
    --grid-header-height: 48px;
    --grid-row-height: 40px;
    --grid-font-size: var(--font-size-xs, 12px);
    --grid-font-family: 'Amazon Ember', Arial, sans-serif;
    --grid-font-weight: 400;
    --grid-font-weight-medium: 500;
    --grid-font-weight-bold: 700;

    /* Text colors */
    --grid-text-color: var(--text-primary);
    --grid-text-secondary: var(--text-secondary);
    --grid-text-accent: var(--text-accent);

    /* Interactive states */
    --grid-hover-bg: var(--btn-hover);
    --grid-active-bg: var(--palette-blue-accent);
    --grid-focus-outline: var(--palette-blue-primary);

    /* Scrollbar styling */
    --grid-scrollbar-width: 8px;
    --grid-scrollbar-bg: var(--bg-secondary);
    --grid-scrollbar-thumb: var(--border-color);
    --grid-scrollbar-thumb-hover: var(--text-secondary);

    /* Animation and transitions */
    --grid-transition: all 0.2s ease;
    --grid-hover-transition: background-color 0.15s ease;
}

/* ==========================================================================
   Main Grid Container
   ========================================================================== */

.snap-grid {
    position: relative;
    width: 100%;
    height: 100%;
    box-sizing: border-box; /* Include border in width/height calculations */
    font-family: var(--grid-font-family);
    font-size: var(--grid-font-size);
    color: var(--grid-text-color);
    background: var(--grid-row-bg);
    border: 1px solid var(--grid-border-color);
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    /* Flexible height that adapts to container */
    min-height: calc(75px + var(--grid-header-height) + 72px);
}

.snap-grid-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    position: relative;
}

/* ==========================================================================
   Grid Controls Header (from old style, tokenized)
   ========================================================================== */

.snap-grid-controls-header {
    background: #F9FAFB;
    border-bottom: 1px solid var(--grid-border-color);
    height: 75px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 15px;
    box-sizing: border-box;
    position: relative;
    z-index: var(--z-header, 5000);
}

.snap-grid-controls-left,
.snap-grid-controls-center,
.snap-grid-controls-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.snap-grid-controls-center {
    margin-left: 32px;
    position: relative; /* Required for absolute positioning of notifications */
}
.snap-grid-controls-right { margin-left: auto; }

.snap-grid-filters-dropdown,
.snap-grid-layout-dropdown { width: 200px; height: 40px; }

/* Clear Filters button - match old grid exactly (light mode) */
.snap-grid-clear-filters-btn {
    background: #E9EBF2;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    height: 40px;
    font-family: var(--grid-font-family);
    font-weight: 500;
    font-size: 12px;
    color: #606F95;
    cursor: pointer;
}

.snap-grid-clear-filters-btn:hover:not(:disabled) { background: #D9DDEB; }
.snap-grid-clear-filters-btn:disabled {
    background: #E9EBF2;
    color: #B4B9C5;
    cursor: not-allowed;
    opacity: 0.5;
}

/* Filter Labels Container */
.snap-grid-filter-labels {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    padding: 15px 15px; /* Add padding to align with controls above and below */
    border-bottom: 1px solid var(--grid-border-color); /* Same bottom border as controls header */
}

/* Individual Filter Label */
.snap-grid-filter-label {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 16px;
    height: 32px;
    background: transparent;
    border: 1.3px solid var(--border-color);
    border-radius: 24px;
    box-sizing: border-box;
    font-family: var(--grid-font-family);
    font-weight: 500;
    font-size: 10px;
    line-height: 2em;
    color: var(--text-primary);
    cursor: default;
}

.snap-grid-filter-label-content {
    display: flex;
    align-items: center;
    gap: 4px;
}

.snap-grid-filter-label-icon {
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.snap-grid-filter-label-icon img {
    width: 12px;
    height: 12px;
}

.snap-grid-filter-label-text {
    font-family: var(--grid-font-family);
    font-weight: 500;
    font-size: 10px;
    line-height: 2em;
    text-align: center;
    color: var(--text-primary);
}

.snap-grid-filter-label-close {
    width: 8px;
    height: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.snap-grid-filter-label-close img {
    width: 8px;
    height: 8px;
}

.snap-grid-filter-label-close:hover {
    opacity: 0.7;
}

/* Clear All Button */
.snap-grid-filter-clear-all {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 8px 16px;
    height: 32px;
    background: #E9EBF2;
    border: none;
    border-radius: 24px;
    box-sizing: border-box;
    font-family: var(--grid-font-family);
    font-weight: 500;
    font-size: 12px;
    color: #606F95;
    cursor: pointer;
}

.snap-grid-filter-clear-all:hover {
    background: #D9DDEB;
}

.snap-grid-filter-clear-all-icon {
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.snap-grid-filter-clear-all-icon img {
    width: 12px;
    height: 12px;
}

.snap-grid-delete-btn,
.snap-grid-export-btn {
    width: 40px !important;
    height: 40px !important;
    min-width: 40px !important;
    min-height: 40px !important;
    max-width: 40px !important;
    max-height: 40px !important;
    border: none;
    border-radius: 50%;
    background: #E9EBF2; /* match old grid */
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    flex-shrink: 0;
}

.snap-grid-delete-btn:hover,
.snap-grid-export-btn:hover { background: #D9DDEB; }

.snap-grid-delete-btn img,
.snap-grid-export-btn img { width: 14px; height: 14px; object-fit: contain; }

.snap-grid-loaded-info {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 40px;
    padding: 10px 16px 8px 16px; /* 2px extra top padding for text positioning */
    font-family: var(--grid-font-family);
    font-weight: 700;
    font-size: 11px;
    color: var(--text-primary);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.snap-grid-loaded-info .loaded-info-icon {
    width: 12px;
    height: 12px;
    flex-shrink: 0;
}

/* ==========================================================================
   Header Styles
   ========================================================================== */

.snap-grid-header {
    background: var(--grid-row-hover-bg);
    border-bottom: 1px solid var(--grid-border-color);
    position: sticky;
    top: 0;
    z-index: 10;
    overflow: hidden; /* header content is translated to match horizontal scroll */
    padding-right: 0; /* header does not reserve vscroll; actions header will offset itself */
    width: 100%; /* Force header to be exactly viewport width */
}

/* Visually fill the header’s right-side gap (equal to the body vscroll width) */
.snap-grid-header::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: var(--vscroll-offset, 0px);
    height: 100%;
    background: var(--grid-header-bg);
    pointer-events: none;
}


/* removed header wrapper lanes */

.snap-grid-header-row {
    display: flex;
    height: var(--grid-header-height);
    min-width: 100%;
    position: relative; /* Enable absolute positioning for pinned-right cells */
}

.snap-grid-header-scroller {
    will-change: transform;
    transform: translateZ(0);
}

.snap-grid-header-cell {
    position: relative;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 0 var(--grid-cell-padding);
    border-right: 1px solid var(--grid-border-color);
    background: var(--grid-header-bg);
    font-weight: var(--grid-font-weight-medium);
    color: var(--grid-text-accent);
    cursor: pointer;
    user-select: none;
    flex-shrink: 0;
}

.snap-grid-header-cell:hover { background: var(--grid-row-hover-bg); }

.snap-grid-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: 8px;
}

.snap-grid-header-text {
    flex: 1;
    font-weight: var(--grid-font-weight-medium);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.snap-grid-sort-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    font-size: 12px;
    color: var(--grid-text-secondary);
    opacity: 0;
    transition: opacity 0.2s ease;
}

.snap-grid-header-cell:hover .snap-grid-sort-icon,
.snap-grid-sort-icon.sort-asc,
.snap-grid-sort-icon.sort-desc {
    opacity: 1;
}

.snap-grid-sort-icon.sort-asc,
.snap-grid-sort-icon.sort-desc {
    color: var(--palette-blue-primary);
    font-weight: var(--grid-font-weight-bold);
}

.snap-grid-sort-icon .sort-icon-img {
    width: 16px;
    height: 16px;
    filter: var(--grid-icon-filter);
}

.snap-grid-column-menu-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border: none;
    background: transparent;
    color: var(--grid-text-secondary);
    cursor: pointer;
    border-radius: 4px;
    font-size: 14px;
    opacity: 0;
    transition: all 0.2s ease;
}

.snap-grid-header-cell:hover .snap-grid-column-menu-btn {
    opacity: 1;
}

.snap-grid-column-menu-container {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.snap-grid-filter-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--color-primary-600, #470CED);
    pointer-events: none;
    z-index: 1;
    opacity: 1;
}

[data-theme="dark"] .snap-grid-filter-indicator,
.snap-grid.dark .snap-grid-filter-indicator,
body.dark .snap-grid-filter-indicator {
    background: var(--color-primary-600, #470ced);
}

.snap-grid-column-menu-btn:hover {
    background: var(--grid-hover-bg);
    color: var(--grid-text-accent);
}

.snap-grid-column-menu-btn img {
    filter: var(--grid-icon-filter);
}

/* Dark mode - make menu button icon white */
[data-theme="dark"] .snap-grid-column-menu-btn img,
.snap-grid.dark .snap-grid-column-menu-btn img,
body.dark .snap-grid-column-menu-btn img {
    filter: brightness(0) invert(1);
}

.snap-grid-resize-handle {
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    cursor: col-resize;
    background: transparent;
    transition: background-color 0.2s ease;
}

.snap-grid-resize-handle:hover {
    background: var(--palette-blue-primary);
}

/* ==========================================================================
   Snap Dropdown (from old style, tokenized)
   ========================================================================== */

.snap-dropdown { position: relative; cursor: pointer; user-select: none; width: 100%; }

.snap-dropdown .dropdown-header {
    display: flex; align-items: center; justify-content: space-between;
    padding: 8px 12px; height: 40px; box-sizing: border-box;
    border: 1.5px solid var(--border-color); border-radius: 4px;
    background: var(--bg-primary);
    font-family: var(--grid-font-family); font-weight: 500; font-size: 12px; color: var(--text-primary);
}

.snap-dropdown.focused .dropdown-header { border-color: var(--action-btn-bg); }
.snap-dropdown .dropdown-header span {
    flex: 1;
    text-align: left;
    padding-right: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
}
.snap-dropdown .dropdown-header img { width: 15px; height: 15px; flex-shrink: 0; }

.snap-dropdown .dropdown-menu {
    position: absolute; top: 100%; left: 0; width: 100%; margin-top: 4px;
    background: var(--bg-primary); border: 1.5px solid var(--border-color); border-radius: 8px;
    box-shadow: none; z-index: var(--z-dropdown) !important;
    max-height: 220px; overflow-y: auto; box-sizing: border-box;
}

.snap-dropdown .dropdown-menu.hidden { display: none; }
.snap-dropdown .dropdown-menu:not(.hidden) { display: block; }

.snap-dropdown .dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    font-family: var(--grid-font-family);
    font-weight: 500;
    font-size: 12px;
    color: var(--text-primary);
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
    overflow: hidden;
}
.snap-dropdown .dropdown-item:hover { background: var(--btn-hover); }
.snap-dropdown .dropdown-item.selected { background: var(--btn-hover); color: var(--text-primary); }

.snap-dropdown .dropdown-divider { height: 1px; background: var(--border-color); margin: 4px 0; }

.layout-dropdown .item-icon { display: flex; align-items: center; justify-content: center; width: 16px; height: 16px; flex-shrink: 0; }
/* General text truncation for all dropdown items */
.snap-dropdown .item-text {
    flex: 1;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    min-width: 0;
}

/* Specific styling for layout dropdown items */
.layout-dropdown .item-text {
    flex: 1;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    min-width: 0;
}

/* ==========================================================================
   Checkbox Styles
   ========================================================================== */

.snap-grid-checkbox-cell,
.snap-grid-checkbox-header {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    user-select: none;
    padding: 0;
    margin: 0;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
}

.snap-grid-checkbox-icon {
    width: 16px;
    height: 16px;
    transition: opacity 0.2s ease;
    filter: var(--grid-icon-filter);
    margin: 0;
    padding: 0;
    display: block;
}

.snap-grid-checkbox-cell:hover .snap-grid-checkbox-icon,
.snap-grid-checkbox-header:hover .snap-grid-checkbox-icon {
    opacity: 0.8;
}

/* Light mode: unchecked checkbox icon with 20% opacity */
.snap-grid:not([data-theme="dark"]) .snap-grid-checkbox-icon[src*="uncheckedbox"] {
    opacity: 0.2;
}

.snap-grid:not([data-theme="dark"]) .snap-grid-checkbox-cell:hover .snap-grid-checkbox-icon[src*="uncheckedbox"],
.snap-grid:not([data-theme="dark"]) .snap-grid-checkbox-header:hover .snap-grid-checkbox-icon[src*="uncheckedbox"] {
    opacity: 0.4;
}

/* Dark mode: unchecked checkbox icon with 100% opacity */
[data-theme="dark"] .snap-grid-checkbox-icon[src*="uncheckedbox"] {
    opacity: 1 !important;
}

[data-theme="dark"] .snap-grid-checkbox-cell:hover .snap-grid-checkbox-icon[src*="uncheckedbox"],
[data-theme="dark"] .snap-grid-checkbox-header:hover .snap-grid-checkbox-icon[src*="uncheckedbox"] {
    opacity: 1 !important;
}

/* ==========================================================================
   Pinned Columns
   ========================================================================== */

.snap-grid-header-cell.pinned-left {
    position: sticky;
    left: 0;
    z-index: 7;
    background: var(--grid-header-bg);
    border-right: 2px solid var(--grid-border-color);
}

.snap-grid-header-cell.pinned-right {
    position: sticky;
    right: 0; /* Stick to right edge of scrollable area */
    z-index: 10; /* Increased z-index to stay above unpinned columns */
    background: var(--grid-header-bg);
    border-left: 2px solid var(--grid-border-color);
    /* Ensure full width is maintained */
    min-width: 96px;
    width: 96px !important;
}

/* Actions column should be flush against right edge without scrollbar offset */
.snap-grid-header-cell[data-field="actions"].pinned-right {
    right: var(--vscroll-offset, 0px) !important; /* align with body content edge accounting for scrollbar */
    /* Use margin-left auto to push Actions to right when row is narrower than viewport */
    margin-left: auto;

/* Removed old disabled dropdown color overrides - using only opacity now */

/* Removed old dark theme disabled dropdown color overrides */

    /* Ensure width is maintained despite margin-left auto */
    width: 96px !important;
    min-width: 96px !important;
    max-width: 96px !important;
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
}

/* In the header wrappers, pinned cells should layout normally (no sticky per-cell) */
/* remove header wrapper-specific overrides */

.snap-grid-cell.pinned-left,
.snap-grid-cell.pinned-right {
    position: sticky;
    z-index: 6;
}

.snap-grid-cell.pinned-left { left: 0; background: var(--grid-row-bg); border-right: 2px solid var(--grid-border-color); }
.snap-grid-cell.pinned-right {
    position: sticky;
    right: 0; /* Stick to right edge of scrollable area */
    background: var(--grid-row-bg);
    border-left: 2px solid var(--grid-border-color);
    /* Ensure full width is maintained for data cells */
    min-width: 96px;
    width: 96px !important;
    z-index: 9; /* Ensure data cells stay above unpinned columns */
}

/* Actions column data cells should be flush against right edge without scrollbar offset */
.snap-grid-cell[data-field="actions"].pinned-right {
    right: 0 !important;
    /* Use margin-left auto to push Actions to right when row is narrower than viewport */
    margin-left: auto;
    /* Ensure width is maintained despite margin-left auto */
    width: 96px !important;
    min-width: 96px !important;
    max-width: 96px !important;
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
}

/* Mask scrolling content behind pinned Actions column */
.snap-grid-cell[data-field="actions"].pinned-right::after {
    content: '';
    position: absolute;
    top: 0;
    right: -20px; /* Extend beyond the column to mask any gap in body only */
    width: 20px;
    height: 100%;
    background: inherit;
    z-index: 1;
    pointer-events: none;
}

/* removed row lane wrappers */

/* Ensure pinned cells reflect row hover/selected states like AG Grid */
.snap-grid-row:hover .snap-grid-cell.pinned-left,
.snap-grid-row:hover .snap-grid-cell.pinned-right { background: var(--grid-row-hover-bg); }

.snap-grid-row.selected .snap-grid-cell.pinned-left,
.snap-grid-row.selected .snap-grid-cell.pinned-right { background: var(--grid-row-selected-bg); }

/* ==========================================================================
   Column Dragging
   ========================================================================== */

.snap-grid-header-text.draggable {
    cursor: grab;
}

.snap-grid-header-text.draggable:active {
    cursor: grabbing;
}

/* ==========================================================================
   Preview + Actions Columns (visuals ported from old grid)
   ========================================================================== */

/* Specific styling for Actions column to prevent overlap */
.snap-grid-header-cell[data-field="actions"],
.snap-grid-cell[data-field="actions"] {
    width: 96px !important;
    min-width: 96px !important;
    max-width: 96px !important;
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
    z-index: 15 !important; /* Highest z-index to stay above all other columns */
    border-right: 1px solid var(--grid-border-color) !important; /* Right border for visual separation */
}


/* Remove right border on Actions header so the filled gap blends seamlessly */
.snap-grid-header-cell[data-field="actions"] {
    border-right: none !important;
}

.preview-square {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    background: var(--bg-secondary);
    cursor: pointer;
}

/* Center preview square in its cell using same approach as checkbox */
.snap-grid-cell:has(.preview-square) {
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-square:hover { background: var(--btn-hover); }

.actions-container {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
}

/* Base styling for action icons */
.listing-analyse-ic,
.listing-edit-ic {
    position: relative;
    width: 28px;
    height: 28px;
    cursor: pointer;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.15s ease;
}

/* Icon sizing */
.listing-analyse-ic img {
    width: 16px;
    height: 16px;
    position: relative;
    z-index: 1;
}

.listing-edit-ic img {
    width: 14px;
    height: 14px;
    position: relative;
    z-index: 1;
}

/* Hover effects */
.listing-analyse-ic:hover {
    background: #04AE2C !important;
}

.listing-edit-ic:hover {
    background: #470CED !important;
}

/* Change icon source on hover using background approach */
.listing-analyse-ic:hover img {
    opacity: 0 !important;
}

.listing-edit-ic:hover img {
    opacity: 0 !important;
}

.listing-analyse-ic:hover::before {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 16px !important;
    height: 16px !important;
    background: url('assets/listing-analyse-hover-ic.svg') center/contain no-repeat !important;
    background-size: 16px 16px !important;
    z-index: 2 !important;
}

.listing-edit-ic:hover::before {
    content: '' !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 14px !important;
    height: 14px !important;
    background: url('assets/edit-hover-ic.svg') center/contain no-repeat !important;
    background-size: 14px 14px !important;
    z-index: 2 !important;
}

/* Dark theme support */
[data-theme="dark"] .listing-analyse-ic,
[data-theme="dark"] .listing-edit-ic {
    background: var(--border-color);
}

[data-theme="dark"] .listing-analyse-ic:hover {
    background: #04AE2C;
}

[data-theme="dark"] .listing-edit-ic:hover {
    background: #470CED;
}
[data-theme="dark"] .preview-square { background: var(--bg-secondary); }
[data-theme="dark"] .preview-square:hover { background: var(--btn-hover); }

.snap-grid-header-cell.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
    z-index: 1000;
    box-shadow: none;
}

/* Ensure the dragging overlay covers the entire header cell */
.snap-grid-header-cell.dragging .snap-grid-header-text.draggable {
    opacity: 1; /* Keep text visible during drag */
}

.snap-grid-header-cell.drag-over {
    border-left: 3px solid var(--palette-blue-primary);
}

.snap-grid-drop-indicator {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--palette-blue-primary);
    z-index: 1001;
    pointer-events: none;
}

/* ==========================================================================
   Flag Icon Styles
   ========================================================================== */

/* Ensure all flag icons within the grid are 16x16px */
.snap-grid .listing-marketplace-flag,
.snap-grid .marketplace-icon,
.snap-grid .ad-spend-flag,
.snap-grid img[src*="US.svg"],
.snap-grid img[src*="UK.svg"],
.snap-grid img[src*="DE.svg"],
.snap-grid img[src*="FR.svg"],
.snap-grid img[src*="IT.svg"],
.snap-grid img[src*="ES.svg"],
.snap-grid img[src*="JP.svg"] {
    width: 16px !important;
    height: 16px !important;
    object-fit: contain;
}

/* ==========================================================================
   Status Badge Styles (Figma Design Implementation)
   ========================================================================== */

.snap-grid-status {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 8px;
    border-radius: 6px;
    font-weight: 500;
    font-size: 12px;
    font-family: var(--grid-font-family);
    height: 24px;
    box-sizing: border-box;
}

.snap-grid-status .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    border: 0.7px solid;
    flex-shrink: 0;
}

/* Blue Badge (Processing) */
.snap-grid-status.status-blue {
    background: linear-gradient(90deg, rgba(15, 188, 249, 0) 0%, rgba(15, 188, 249, 0.10) 100%);
    border: 1.3px solid rgba(15, 188, 249, 0.2);
    color: #0FBCF9;
}
.snap-grid-status.status-blue .dot {
    background: rgba(15, 188, 249, 0.5);
    border-color: #0FBCF9;
}

/* Amber Badge (Locked) */
.snap-grid-status.status-amber {
    background: linear-gradient(90deg, rgba(255, 167, 0, 0) 0%, rgba(255, 167, 0, 0.10) 100%);
    border: 1.3px solid rgba(255, 167, 0, 0.2);
    color: #FFA700;
}
.snap-grid-status.status-amber .dot {
    background: rgba(255, 167, 0, 0.5);
    border-color: #FFA700;
}

/* Red Badge (Rejected) */
.snap-grid-status.status-red {
    background: linear-gradient(90deg, rgba(255, 57, 31, 0) 0%, rgba(255, 57, 31, 0.10) 100%);
    border: 1.3px solid rgba(255, 57, 31, 0.2);
    color: #FF391F;
}
.snap-grid-status.status-red .dot {
    background: rgba(255, 57, 31, 0.5);
    border-color: #FF391F;
}

/* Green Badge (Live) */
.snap-grid-status.status-green {
    background: linear-gradient(90deg, rgba(4, 174, 44, 0) 0%, rgba(4, 174, 44, 0.10) 100%);
    border: 1.3px solid rgba(4, 174, 44, 0.2);
    color: #04AE2C;
}
.snap-grid-status.status-green .dot {
    background: rgba(4, 174, 44, 0.5);
    border-color: #04AE2C;
}

/* Gray Badge (Draft) */
.snap-grid-status.status-gray {
    background: linear-gradient(90deg, rgba(107, 114, 128, 0) 0%, rgba(107, 114, 128, 0.10) 100%);
    border: 1.3px solid rgba(107, 114, 128, 0.2);
    color: #6B7280;
}
.snap-grid-status.status-gray .dot {
    background: rgba(107, 114, 128, 0.5);
    border-color: #6B7280;
}

/* ==========================================================================
   Body and Viewport Styles
   ========================================================================== */

.snap-grid-body {
    flex: 1;
    position: relative;
    overflow-y: auto; /* vertical scrollbar in body */
    overflow-x: auto; /* horizontal scroll enabled */
    background: var(--grid-row-bg);
    min-height: 0; /* Allow flex item to shrink below content size */
    width: 100%; /* Force body to be exactly viewport width */
}

.snap-grid-viewport {
    position: relative;
    min-height: var(--grid-header-height);
    min-width: 100%; /* Ensure viewport maintains full width for sticky columns */
}

/* Remove dedicated horizontal scrollbar - use body scrollbar only */

.snap-grid-scroll-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 1px;
    pointer-events: none;
}

/* ==========================================================================
   Row Styles
   ========================================================================== */

.snap-grid-row {
    display: flex;
    height: var(--grid-row-height);
    border-bottom: 1px solid var(--grid-border-color);
    background: var(--grid-row-bg);
    transition: var(--grid-hover-transition);
    cursor: pointer;
    min-width: 100%;
    position: relative; /* Enable absolute positioning for pinned-right cells */
}

.snap-grid-row:hover {
    background: var(--grid-row-hover-bg);
}

.snap-grid-row.selected {
    background: var(--grid-row-selected-bg);
    color: var(--grid-text-accent);
}

/* Last row now keeps its border for visual separation before scrollbar */

/* ==========================================================================
   Cell Styles
   ========================================================================== */

.snap-grid-cell {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 0 var(--grid-cell-padding);
    border-right: 1px solid var(--grid-border-color);
    background: inherit;
    color: inherit;
    min-width: 120px;
    flex-shrink: 0;
    font-size: var(--grid-font-size);
}

.snap-grid-cell-content {
    width: 100%;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.snap-grid-cell:last-child {
    border-right: none;
}

.snap-grid-cell.editable {
    cursor: text;
}

.snap-grid-cell.editing {
    padding: 0;
}

.snap-grid-cell-editor {
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    background: var(--bg-primary);
    color: var(--grid-text-accent);
    font-family: inherit;
    font-size: inherit;
    padding: 0 var(--grid-cell-padding);
    border: 2px solid var(--palette-blue-primary);
    border-radius: 4px;
}

/* ==========================================================================
   Scrollbar Styling
   ========================================================================== */

.snap-grid-body::-webkit-scrollbar {
    width: var(--grid-scrollbar-width);
    height: var(--grid-scrollbar-width);
}

.snap-grid-body::-webkit-scrollbar-track { background: transparent; }

.snap-grid-body::-webkit-scrollbar-thumb {
    background: var(--grid-scrollbar-thumb);
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.snap-grid-body::-webkit-scrollbar-thumb:hover {
    background: var(--grid-scrollbar-thumb-hover);
}

.snap-grid-body::-webkit-scrollbar-corner {
    background: var(--grid-scrollbar-bg);
}

/* Firefox scrollbar styling */
.snap-grid-body { scrollbar-width: thin; scrollbar-color: var(--grid-scrollbar-thumb) transparent; }

/* ==========================================================================
   Theme Variations
   ========================================================================== */

/* Compact theme */
.snap-grid.compact {
    --grid-row-height: 32px;
    --grid-header-height: 40px;
    --grid-cell-padding: var(--spacing-xs, 8px);
    --grid-font-size: var(--font-size-xs, 12px);
}

/* Dense theme */
.snap-grid.dense {
    --grid-row-height: 28px;
    --grid-header-height: 36px;
    --grid-cell-padding: var(--spacing-xs, 6px);
    --grid-font-size: var(--font-size-xs, 11px);
}

/* Comfortable theme */
.snap-grid.comfortable {
    --grid-row-height: 48px;
    --grid-header-height: 56px;
    --grid-cell-padding: var(--spacing-md, 16px);
    --grid-font-size: var(--font-size-base, 16px);
}

/* Dark Mode Support for Filter Labels */
[data-theme="dark"] .snap-grid-filter-label,
.snap-grid.dark .snap-grid-filter-label,
body.dark .snap-grid-filter-label {
    color: var(--text-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .snap-grid-filter-label-text,
.snap-grid.dark .snap-grid-filter-label-text,
body.dark .snap-grid-filter-label-text {
    color: var(--text-primary);
}

[data-theme="dark"] .snap-grid-filter-label-icon img,
.snap-grid.dark .snap-grid-filter-label-icon img,
body.dark .snap-grid-filter-label-icon img {
    filter: brightness(0) invert(1); /* Make icons white */
}

[data-theme="dark"] .snap-grid-filter-label-close img,
.snap-grid.dark .snap-grid-filter-label-close img,
body.dark .snap-grid-filter-label-close img {
    filter: brightness(0) invert(1); /* Make close icon white */
}

[data-theme="dark"] .snap-grid-filter-clear-all,
.snap-grid.dark .snap-grid-filter-clear-all,
body.dark .snap-grid-filter-clear-all {
    background: rgba(42, 47, 55, 0.6) !important;
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .snap-grid-filter-clear-all:hover,
.snap-grid.dark .snap-grid-filter-clear-all:hover,
body.dark .snap-grid-filter-clear-all:hover {
    background: var(--palette-gray-750, #2A2F37) !important;
}

[data-theme="dark"] .snap-grid-filter-clear-all img,
.snap-grid.dark .snap-grid-filter-clear-all img,
body.dark .snap-grid-filter-clear-all img {
    filter: brightness(0) invert(1); /* Make clear all icon white */
}

/* ==========================================================================
   Dark Mode Support
   ========================================================================== */

[data-theme="dark"] .snap-grid {
    --grid-row-selected-bg: #292E38;
    --grid-row-hover-bg: #292E38;
    --grid-active-bg: rgba(71, 12, 237, 0.3);
    --grid-scrollbar-thumb: var(--palette-gray-600);
    --grid-scrollbar-thumb-hover: var(--palette-gray-500);
}

/* Dark mode: ensure visible outer border like old grid */
[data-theme="dark"] .snap-grid { border: 1px solid var(--grid-border-color) !important; }

/* Enhanced border visibility for dark mode */
[data-theme="dark"] .snap-grid-row {
    border-bottom-color: var(--grid-border-color, #4A5568) !important;
}

[data-theme="dark"] .snap-grid-header-cell {
    border-right-color: var(--grid-border-color, #4A5568) !important;
}

[data-theme="dark"] .snap-grid-cell {
    border-right-color: var(--grid-border-color, #4A5568) !important;
}

[data-theme="dark"] .snap-grid-header { background: var(--grid-header-bg); }
[data-theme="dark"] .snap-grid-controls-header { background: var(--bg-secondary); border-color: var(--border-color); }
/* Match old grid dark-mode Clear Filters button */
[data-theme="dark"] .snap-grid-clear-filters-btn { background: rgba(42, 47, 55, 0.6) !important; color: var(--palette-gray-400, #B4B9C5) !important; }
[data-theme="dark"] .snap-grid-clear-filters-btn:hover:not(:disabled) { background: var(--palette-gray-750, #2A2F37) !important; }
[data-theme="dark"] .snap-grid-clear-filters-btn:disabled { background: var(--palette-gray-750, #2A2F37) !important; color: var(--palette-gray-400, #B4B9C5) !important; opacity: 0.5; cursor: not-allowed; }
[data-theme="dark"] .snap-grid-delete-btn,
[data-theme="dark"] .snap-grid-export-btn { background: var(--palette-gray-750, #2A2F37) !important; }
[data-theme="dark"] .snap-grid-delete-btn:hover,
[data-theme="dark"] .snap-grid-export-btn:hover { background: rgba(42, 47, 55, 0.6) !important; }
[data-theme="dark"] .snap-grid-loaded-info { background: var(--bg-secondary); border-color: var(--border-color); color: var(--text-primary); }

[data-theme="dark"] .snap-dropdown .dropdown-header { background: var(--bg-primary); border-color: var(--border-color); color: var(--text-primary); }
[data-theme="dark"] .snap-dropdown.focused .dropdown-header { border-color: var(--action-btn-bg); }
[data-theme="dark"] .snap-dropdown .dropdown-menu { background: var(--bg-primary); border-color: var(--border-color); }
[data-theme="dark"] .snap-dropdown .dropdown-item:hover { background: var(--btn-hover); }

/* ==========================================================================
   Loading and Empty States
   ========================================================================== */

.snap-grid-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--grid-text-secondary);
    font-size: var(--font-size-sm);
}

.snap-grid-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--grid-text-secondary);
    font-size: var(--font-size-sm);
    gap: 8px;
}

.snap-grid-empty-icon {
    font-size: 48px;
    opacity: 0.5;
}

/* ==========================================================================
   Footer & Stats (from old style, tokenized)
   ========================================================================== */

.snap-grid-footer {
    background: var(--grid-row-hover-bg);
    border-top: 1px solid var(--grid-border-color);
    height: 72px;
    padding: 0 48px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    position: relative;
    z-index: 10;
    margin-top: -1px;
}

.snap-grid-footer-stats { display: flex; align-items: center; gap: 64px; width: 100%; height: 100%; position: relative; }
.snap-grid-stat-item { display: flex; flex-direction: column; gap: 2px; }
.snap-grid-stat-label { font-family: var(--grid-font-family); font-weight: 500; font-size: 11px; color: var(--text-secondary); line-height: 1.2; }
.snap-grid-stat-value { font-family: var(--grid-font-family); font-weight: 700; font-size: 12px; color: var(--text-primary); line-height: 1.2; }

[data-theme="dark"] .snap-grid-footer { background: var(--bg-secondary); border-top-color: var(--grid-border-color); }
[data-theme="dark"] .snap-grid-stat-label { color: var(--text-secondary); }
[data-theme="dark"] .snap-grid-stat-value { color: var(--text-accent); }

/* ==========================================================================
   Column Menu Styles - Exact copy from old grid
   ========================================================================== */

.snap-grid-column-menu {
    background: var(--color-surface-primary, #ffffff);
    border: 1px solid var(--grid-border-color);
    border-radius: var(--border-radius-md, 8px);
    box-shadow: var(--shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1));
    padding: var(--spacing-xs, 4px) 0;
    min-width: 180px;
    z-index: 9999 !important;
}

/* Tabbed menu styles */
.snap-grid-column-menu.tabbed-menu {
    min-width: 281px;
    width: 281px;
    padding: 0;
    border: 1.5px solid var(--border-color);
    border-radius: 8px;
}

.menu-tab-header {
    display: flex;
    background: var(--bg-secondary);
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid var(--border-color);
    height: 48px;
}

.menu-tab-btn {
    flex: 1;
    padding: 12px;
    background: var(--bg-secondary);
    border: none;
    border-right: 1px solid var(--border-color);
    cursor: pointer;
    font-size: 16px;
    color: var(--text-secondary);
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    box-sizing: border-box;
}

.menu-tab-btn:first-child {
    border-top-left-radius: 8px;
}

.menu-tab-btn:last-child {
    border-right: none;
    border-top-right-radius: 8px;
}

.menu-tab-btn:hover {
    background: var(--btn-hover);
}

.menu-tab-btn.active {
    background: var(--bg-primary);
    position: relative;
}

.menu-tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--action-btn-bg);
}

.tab-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
}

.menu-tab-content {
    padding: 16px;
}

.menu-tab-panel {
    display: none;
}

.menu-tab-panel.active {
    display: block;
}

.menu-tab-panel[data-tab-panel="filter"].active {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm, 12px);
}

/* Dark mode support for menu tabs - exact copy from old grid */
[data-theme="dark"] .snap-grid-column-menu.tabbed-menu,
.snap-grid.dark .snap-grid-column-menu.tabbed-menu,
body.dark .snap-grid-column-menu.tabbed-menu {
    background: var(--palette-gray-800, #1A1D23) !important;
    border-color: var(--palette-gray-700, #2F3341) !important;
}

[data-theme="dark"] .menu-tab-header,
.snap-grid.dark .menu-tab-header,
body.dark .menu-tab-header {
    background: var(--palette-gray-900, #111216) !important;
    border-bottom-color: var(--palette-gray-700, #2F3341) !important;
}

[data-theme="dark"] .menu-tab-btn,
.snap-grid.dark .menu-tab-btn,
body.dark .menu-tab-btn {
    background: var(--palette-gray-800, #1A1D23) !important;
    border-color: var(--palette-gray-700, #2F3341) !important;
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .menu-tab-btn:hover,
.snap-grid.dark .menu-tab-btn:hover,
body.dark .menu-tab-btn:hover {
    background: var(--palette-gray-700, #2F3341) !important;
}

[data-theme="dark"] .menu-tab-btn.active,
.snap-grid.dark .menu-tab-btn.active,
body.dark .menu-tab-btn.active {
    background: var(--palette-gray-900, #111216) !important;
    color: var(--palette-gray-400, #B4B9C5) !important;
}

/* Dark mode support for tab icons - make active tab icons white */
[data-theme="dark"] .menu-tab-btn.active .tab-icon,
.snap-grid.dark .menu-tab-btn.active .tab-icon,
body.dark .menu-tab-btn.active .tab-icon {
    filter: brightness(0) invert(1) !important; /* Makes icons white */
}

/* Dark mode support for Save Layout dropdown icons - make all icons fully white */
[data-theme="dark"] .layout-dropdown .item-icon img,
.snap-grid.dark .layout-dropdown .item-icon img,
body.dark .layout-dropdown .item-icon img {
    filter: brightness(0) invert(1) !important; /* Makes icons fully white */
}

[data-theme="dark"] .snap-grid-column-menu {
    background: var(--bg-secondary);
    box-shadow: none;
}

.snap-grid-menu-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    color: var(--grid-text-color);
    font-size: var(--font-size-sm);
    transition: background-color 0.15s ease;
    gap: 8px;
}

.snap-grid-menu-item:hover {
    background: var(--grid-hover-bg);
}

.snap-grid-menu-item-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.snap-grid-menu-item-icon .menu-icon-img {
    width: 12px;
    height: 12px;
    filter: var(--grid-icon-filter);
}

/* ==========================================================================
   Tabbed Column Menu Styles
   ========================================================================== */

/* Tab Header Styles */
.menu-tab-header {
    display: flex;
    background: var(--bg-secondary);
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid var(--border-color);
    height: 48px;
}

.menu-tab-btn {
    flex: 1;
    padding: 12px;
    background: var(--bg-secondary);
    border: none;
    border-right: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 48px;
    box-sizing: border-box;
}

.menu-tab-btn:first-child {
    border-top-left-radius: 8px;
}

.menu-tab-btn:last-child {
    border-right: none;
    border-top-right-radius: 8px;
}

.menu-tab-btn:hover {
    background: var(--btn-hover);
}

.menu-tab-btn.active {
    background: var(--bg-primary);
    position: relative;
}

.menu-tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--palette-blue-primary);
}

.tab-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
}

/* Tab Content Styles */
.menu-tab-content {
    padding: 16px;
}

.menu-tab-panel {
    display: none;
}

.menu-tab-panel.active {
    display: block;
}

/* Snap Dropdown Styles - Exact copy from old grid */
.snap-dropdown {
    position: relative;
    cursor: pointer;
    user-select: none;
    width: 100%;
}

.snap-dropdown .dropdown-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    height: 40px;
    background: var(--bg-primary);
    border: 1.5px solid var(--border-color);
    border-radius: 6px;
    box-sizing: border-box;
    font-family: var(--grid-font-family);
    font-weight: 500;
    font-size: 12px;
    color: var(--text-primary);
    cursor: pointer;
}

.snap-dropdown.focused .dropdown-header {
    border-color: var(--action-btn-bg);
}

.snap-dropdown .dropdown-header span {
    flex: 1;
    text-align: left;
    padding-left: 0;
    padding-right: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
}

.snap-dropdown .dropdown-header img {
    width: 15px;
    height: 15px;
    flex-shrink: 0;
    margin-right: 0;
    filter: var(--dropdown-icon-filter, none);
}

.snap-dropdown .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: var(--bg-primary);
    border: 1.5px solid var(--border-color);
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
    box-sizing: border-box;
    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: #D9DDEB transparent;
}

.snap-dropdown .dropdown-menu::-webkit-scrollbar {
    width: 6px;
}

.snap-dropdown .dropdown-menu::-webkit-scrollbar-track {
    background: transparent;
}

.snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb {
    background: #D9DDEB;
    border-radius: 3px;
}

.snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #E9EBF2;
}

.snap-dropdown .dropdown-menu.hidden {
    display: none;
}

.snap-dropdown .dropdown-menu:not(.hidden) {
    display: block;
}

.snap-dropdown .dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    font-family: var(--grid-font-family);
    font-weight: 500;
    font-size: 12px;
    color: var(--text-primary);
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
    overflow: hidden;
}

.snap-dropdown .dropdown-item:hover {
    background: #F3F4F6;
}

.snap-dropdown .dropdown-item:first-child:hover {
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
}

.snap-dropdown .dropdown-item:last-child:hover {
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
}

.snap-dropdown .dropdown-item.selected {
    background: #F3F4F6;
    color: var(--action-btn-bg);
}

.dropdown-separator {
    height: 1px;
    background: var(--border-color);
    margin: 4px 0;
}

/* Dark mode support for snap dropdown - exact copy from old grid */
[data-theme="dark"] .snap-dropdown .dropdown-header,
.snap-grid.dark .snap-dropdown .dropdown-header,
body.dark .snap-dropdown .dropdown-header {
    background: var(--palette-gray-800, #1A1D23) !important;
    border: 1.5px solid var(--palette-gray-700, #2F3341) !important;
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .snap-dropdown.focused .dropdown-header,
.snap-grid.dark .snap-dropdown.focused .dropdown-header,
body.dark .snap-dropdown.focused .dropdown-header {
    border-color: #470CED !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-menu,
.snap-grid.dark .snap-dropdown .dropdown-menu,
body.dark .snap-dropdown .dropdown-menu {
    background: var(--palette-gray-800, #1A1D23) !important;
    border: 1.5px solid var(--palette-gray-700, #2F3341) !important;
    scrollbar-color: #43506F transparent !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb,
.snap-grid.dark .snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb,
body.dark .snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb {
    background: #43506F !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb:hover,
.snap-grid.dark .snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb:hover,
body.dark .snap-dropdown .dropdown-menu::-webkit-scrollbar-thumb:hover {
    background: #43506F !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-item,
.snap-grid.dark .snap-dropdown .dropdown-item,
body.dark .snap-dropdown .dropdown-item {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-item:hover,
.snap-grid.dark .snap-dropdown .dropdown-item:hover,
body.dark .snap-dropdown .dropdown-item:hover {
    background: #292E38 !important;
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .snap-dropdown .dropdown-item.selected,
.snap-grid.dark .snap-dropdown .dropdown-item.selected,
body.dark .snap-dropdown .dropdown-item.selected {
    background: #292E38 !important;
    color: var(--palette-gray-400, #B4B9C5) !important;
}

/* Use variable for dropdown icon tinting across themes */
[data-theme="dark"] .snap-grid,
.snap-grid.dark,
body.dark .snap-grid {
    --dropdown-icon-filter: brightness(0) invert(1);
}

/* Dark mode support for search and filter icons */
[data-theme="dark"] .search-icon,
.snap-grid.dark .search-icon,
body.dark .search-icon {
    filter: brightness(0) invert(1) !important;
}

[data-theme="dark"] .filter-icon,
.snap-grid.dark .filter-icon,
body.dark .filter-icon {
    filter: brightness(0) invert(1) !important;
}

/* Dark mode support for logic labels in filter tab */
[data-theme="dark"] .logic-label,
.snap-grid.dark .logic-label,
body.dark .logic-label {
    color: #ffffff !important;
}

/* Filter Tab Styles */
.filter-type-dropdown {
    /* Uses snap-dropdown styles above - no additional styles needed */
    display: block;
}

/* Search input wrapper - exact copy from old grid */
.search-input-wrapper {
    position: relative;
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    display: block !important;
    flex-direction: unset !important;
    align-items: unset !important;
    border-radius: 0 !important;
    width: 100% !important;
    min-width: unset !important;
    box-sizing: border-box !important;
    gap: 0 !important;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    object-fit: contain;
    pointer-events: none;
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: var(--spacing-sm, 12px) var(--spacing-sm, 12px) var(--spacing-sm, 12px) 36px;
    border: 1px solid var(--grid-border-color, #dce0e5);
    border-radius: var(--border-radius-sm, 4px);
    font-size: var(--font-size-sm, 12px);
    background: var(--color-surface-primary, #ffffff);
    color: var(--color-text-primary, #18181b);
    box-sizing: border-box;
}

/* Remove border when search input is inside dropdown menu to prevent double border */
.snap-dropdown .search-input {
    border: none;
    border-radius: 0;
    background: transparent;
    padding-left: 36px;
}

.search-input::placeholder {
    color: var(--color-text-placeholder, #dce0e5);
    font-family: var(--grid-font-family);
    font-size: 12px;
}

.search-input:focus {
    outline: none;
    border-color: var(--color-primary-600, #470ced);
    box-shadow: 0 0 0 1px var(--color-primary-600, #470ced);
}

/* Focus state for search input inside dropdown - no border, just background highlight */
.snap-dropdown .search-input:focus {
    border: none;
    box-shadow: none;
    background: var(--color-surface-hover, #f3f4f6);
}

/* Checkbox list styles - exact copy from old grid */
.filter-checkbox-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm, 8px);
    max-height: 300px;
    overflow-y: auto;
    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: #D9DDEB transparent;
}

.filter-checkbox-list::-webkit-scrollbar {
    width: 6px;
}

.filter-checkbox-list::-webkit-scrollbar-track {
    background: transparent;
}

.filter-checkbox-list::-webkit-scrollbar-thumb {
    background: #D9DDEB;
    border-radius: 3px;
}

.filter-checkbox-list::-webkit-scrollbar-thumb:hover {
    background: #E9EBF2;
}

/* Dark mode scrollbar for filter checkbox list - standardized colors */
[data-theme="dark"] .filter-checkbox-list,
.snap-grid.dark .filter-checkbox-list,
body.dark .filter-checkbox-list {
    scrollbar-color: #43506F transparent !important;
}

[data-theme="dark"] .filter-checkbox-list::-webkit-scrollbar-thumb,
.snap-grid.dark .filter-checkbox-list::-webkit-scrollbar-thumb,
body.dark .filter-checkbox-list::-webkit-scrollbar-thumb {
    background: #43506F !important;
}

[data-theme="dark"] .filter-checkbox-list::-webkit-scrollbar-thumb:hover,
.snap-grid.dark .filter-checkbox-list::-webkit-scrollbar-thumb:hover,
body.dark .filter-checkbox-list::-webkit-scrollbar-thumb:hover {
    background: #43506F !important;
}

.filter-checkbox-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm, 10px);
    padding: 0 var(--spacing-xs, 8px) 0 0;
    border-radius: var(--border-radius-sm, 4px);
}

/* Unchecked checkbox opacity styling for light mode */
.filter-checkbox-item .checkbox-icon[src*="uncheckedbox-ic.svg"] {
    opacity: 0.2;
    transition: opacity 0.2s ease;
}

/* Dark mode - full opacity for unchecked checkboxes */
[data-theme="dark"] .filter-checkbox-item .checkbox-icon[src*="uncheckedbox-ic.svg"],
.snap-grid.dark .filter-checkbox-item .checkbox-icon[src*="uncheckedbox-ic.svg"],
body.dark .filter-checkbox-item .checkbox-icon[src*="uncheckedbox-ic.svg"] {
    opacity: 1;
}

.filter-checkbox-label {
    font-size: var(--font-size-sm, 12px);
    font-family: var(--grid-font-family);
    color: var(--color-text-primary, #18181b);
    cursor: pointer;
    user-select: none;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
    flex: 1;
}

/* Dark mode support for filter checkbox labels */
[data-theme="dark"] .filter-checkbox-label,
.snap-grid.dark .filter-checkbox-label,
body.dark .filter-checkbox-label {
    color: #ffffff !important;
}

.checkbox-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    font-family: var(--grid-font-family);
    font-weight: 500;
    font-size: 12px;
    color: var(--text-primary);
    transition: background-color 0.2s ease;
}

.checkbox-item:hover {
    background: #F3F4F6;
}

.checkbox-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

.checkbox-label {
    flex: 1;
    text-align: left;
}

/* Filter divider */
.filter-divider {
    height: 1px;
    background: var(--border-color);
}

/* Logic section styles */
.filter-logic-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md, 16px);
}

.logic-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs, 8px);
    cursor: pointer;
    padding: var(--spacing-xs, 4px);
    border-radius: var(--border-radius-sm, 4px);
    transition: background-color 0.15s ease;
}

.logic-toggle:hover {
    background: var(--color-surface-hover, #f3f4f6);
}

.logic-checkbox {
    width: 16px;
    height: 16px;
    margin-right: 6px;
}

.logic-checkbox {
    width: 16px;
    height: 16px;
    object-fit: contain;
}

.logic-label {
    font-size: var(--font-size-xs, 12px);
    font-weight: var(--grid-font-weight, 400);
    color: var(--color-text-primary, #000000);
}

.logic-separator {
    font-size: var(--font-size-xs, 12px);
    color: var(--color-text-secondary, #6b7280);
}

.filter-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.filter-icon {
    position: absolute;
    left: 12px;
    width: 16px;
    height: 16px;
    z-index: 1;
}

.filter-input-with-icon {
    width: 100%;
    padding: 8px 12px 8px 40px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--grid-font-family);
    font-size: 12px;
}

.filter-input-with-icon:focus {
    outline: none;
    border-color: var(--color-primary-600, #470ced);
    box-shadow: 0 0 0 1px var(--color-primary-600, #470ced);
}

.filter-input-with-icon::placeholder {
    color: var(--color-text-placeholder, #dce0e5);
    font-family: var(--grid-font-family);
    font-size: 12px;
}

/* Search input wrapper for visibility tab */
/* Custom date picker icon styling (match old grid) */
.filter-input-with-icon[type="date"]::-webkit-calendar-picker-indicator {
    background-image: url('assets/date-picker-ic.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 12px 12px;
    width: 12px;
    height: 12px;
    cursor: pointer;
    opacity: 1;
}

.filter-input-with-icon[type="date"]::-moz-calendar-picker-indicator {
    background-image: url('assets/date-picker-ic.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: 12px 12px;
    width: 12px;
    height: 12px;
    cursor: pointer;
    opacity: 1;
    border: none;
    background-color: transparent;
}

/* Dark mode support for date picker icon - make it white */
[data-theme="dark"] .filter-input-with-icon[type="date"]::-webkit-calendar-picker-indicator,
.snap-grid.dark .filter-input-with-icon[type="date"]::-webkit-calendar-picker-indicator,
body.dark .filter-input-with-icon[type="date"]::-webkit-calendar-picker-indicator {
    filter: brightness(0) invert(1) !important;
}

[data-theme="dark"] .filter-input-with-icon[type="date"]::-moz-calendar-picker-indicator,
.snap-grid.dark .filter-input-with-icon[type="date"]::-moz-calendar-picker-indicator,
body.dark .filter-input-with-icon[type="date"]::-moz-calendar-picker-indicator {
    filter: brightness(0) invert(1) !important;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 12px;
    height: 12px;
    object-fit: contain;
    pointer-events: none;
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 8px 12px 8px 40px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--grid-font-family);
    font-size: 12px;
}

.search-input:focus {
    outline: none;
    border-color: var(--color-primary-600, #470ced);
    box-shadow: 0 0 0 1px var(--color-primary-600, #470ced);
}

/* Logic Toggle Styles */
.filter-logic-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-md, 16px);
}

.logic-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs, 8px);
    cursor: pointer;
    padding: var(--spacing-xs, 4px);
    border-radius: var(--border-radius-sm, 4px);
    transition: background-color 0.15s ease;
}

.logic-toggle:hover {
    background: var(--color-surface-hover, #f3f4f6);
}

.logic-checkbox {
    width: 16px;
    height: 16px;
}

.logic-label {
    font-size: var(--font-size-xs, 12px);
    font-weight: var(--grid-font-weight, 400);
    color: var(--color-text-primary, #000000);
}

.logic-separator {
    font-size: var(--font-size-xs, 12px);
    color: var(--color-text-secondary, #6b7280);
}

/* Management Tab Styles */
.menu-option-with-icon {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm, 10px);
    padding: var(--spacing-sm, 12px) 16px var(--spacing-sm, 12px) 16px; /* Same padding as hover state */
    margin: 0 -16px; /* Same margin as hover state */
    cursor: pointer;
    transition: background-color 0.15s ease;
    background: transparent; /* Transparent background in normal state */
}

.menu-option-with-icon:hover {
    background: var(--color-surface-hover, #f3f4f6);
}

.menu-option-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
}

.menu-option-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
}

.menu-option-label {
    font-size: var(--font-size-sm, 14px);
    font-weight: 500;
    color: var(--color-text-primary, #18181b);
}

/* Management Tab Styles - Enhanced from old grid */
.column-management-divider {
    width: auto;
    height: 1.5px;
    background: var(--grid-border-color, #E9EBF2);
    margin: var(--spacing-xs, 4px) -16px;
    flex-shrink: 0;
}

[data-theme="dark"] .column-management-divider {
    background: var(--border-color) !important;
}

.pin-column-container {
    position: relative;
}

.pin-column-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 -16px; /* Extend hover area to edges */
    padding: 12px 16px; /* Add horizontal padding to compensate for margin */
    cursor: pointer;
    transition: background-color 0.15s ease;
    font-family: var(--grid-font-family);
    font-weight: 500;
    font-size: 14px;
    color: var(--color-text-primary, #18181b);
}

.pin-column-option:hover {
    background: var(--btn-hover, #F3F4F6);
}

/* Dark mode support for pin-column-option hover */
[data-theme="dark"] .pin-column-option:hover,
.snap-grid.dark .pin-column-option:hover,
body.dark .pin-column-option:hover {
    background: var(--palette-gray-700, #2F3341) !important;
}

.pin-column-main {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.pin-arrow {
    width: 10px;
    height: 10px;
    flex-shrink: 0;
}

.pin-submenu {
    position: absolute;
    top: 0;
    left: 100%;
    width: 130px;
    background: var(--color-surface-primary, #FFFFFF);
    border: 1.5px solid var(--grid-border-color, #E9EBF2);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: var(--z-dropdown, 1001);
    margin-left: 4px;
    padding: 16px 0;
    display: none; /* Hidden by default */
}

.pin-submenu:not(.hidden) {
    display: block;
}

.pin-submenu.hidden {
    display: none;
}

[data-theme="dark"] .pin-submenu,
.snap-grid.dark .pin-submenu,
body.dark .pin-submenu {
    background: var(--palette-gray-800, #1A1D23) !important;
    border-color: var(--palette-gray-700, #2F3341) !important;
}

.pin-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 16px;
    cursor: pointer;
    transition: background-color 0.15s ease;
    font-family: 'Amazon Ember', sans-serif;
    font-weight: var(--grid-font-weight-medium, 500);
    font-size: 14px;
    color: #18181B;
}

.pin-option:hover {
    background: #F3F4F6;
}

[data-theme="dark"] .pin-option,
.snap-grid.dark .pin-option,
body.dark .pin-option {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .pin-option:hover,
.snap-grid.dark .pin-option:hover,
body.dark .pin-option:hover {
    background: var(--palette-gray-700, #2F3341) !important;
}

.check-icon {
    width: 12px;
    height: 12px;
    flex-shrink: 0;
}

/* Hidden utility class */
.check-icon.hidden {
    visibility: hidden !important;
}

.reset-option {
    padding: 12px 16px 12px 42px; /* Same padding as hover state */
    margin: 0 -16px; /* Same margin as hover state */
    font-family: var(--grid-font-family);
    font-weight: var(--grid-font-weight-medium, 500);
    font-size: 14px;
    color: #18181B;
    cursor: pointer;
    transition: background-color 0.15s ease;
    background: transparent; /* Transparent background in normal state */
}

.reset-option:hover {
    background: var(--color-surface-hover, #F3F4F6);
}

[data-theme="dark"] .reset-option,
.snap-grid.dark .reset-option,
body.dark .reset-option {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] .reset-option:hover {
    background: var(--btn-hover) !important;
}

/* Dark mode support for pin-column-option text color */
[data-theme="dark"] .pin-column-option,
.snap-grid.dark .pin-column-option,
body.dark .pin-column-option {
    color: var(--palette-gray-400, #B4B9C5) !important;
}


/* Visibility Tab Styles */
.column-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs, 4px);
    max-height: 300px;
    overflow-y: auto;
    /* Custom scrollbar styling */
    scrollbar-width: thin;
    scrollbar-color: #D9DDEB transparent;
}

/* Webkit scrollbar styling for column list */
.column-list::-webkit-scrollbar {
    width: 6px;
}

.column-list::-webkit-scrollbar-track {
    background: transparent;
}

.column-list::-webkit-scrollbar-thumb {
    background: #D9DDEB;
    border-radius: 3px;
}

.column-list::-webkit-scrollbar-thumb:hover {
    background: #E9EBF2;
}

/* Dark mode scrollbar for column list - standardized colors */
[data-theme="dark"] .column-list,
.snap-grid.dark .column-list,
body.dark .column-list {
    scrollbar-color: #43506F transparent !important;
}

[data-theme="dark"] .column-list::-webkit-scrollbar-thumb,
.snap-grid.dark .column-list::-webkit-scrollbar-thumb,
body.dark .column-list::-webkit-scrollbar-thumb {
    background: #43506F !important;
}

[data-theme="dark"] .column-list::-webkit-scrollbar-thumb:hover,
.snap-grid.dark .column-list::-webkit-scrollbar-thumb:hover,
body.dark .column-list::-webkit-scrollbar-thumb:hover {
    background: #43506F !important;
}

.column-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm, 10px);
    padding: var(--spacing-xs, 8px);
    border-radius: var(--border-radius-sm, 4px);
    transition: background-color 0.15s ease;
}

.column-item:hover {
    background: var(--color-surface-hover, #f3f4f6);
}

/* Dark mode styles for column items */
[data-theme="dark"] .column-item:hover,
.snap-grid.dark .column-item:hover,
body.dark .column-item:hover {
    background: var(--btn-hover, var(--palette-gray-700));
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
}

.grip-handle {
    width: 16px;
    height: 16px;
    object-fit: contain;
    cursor: grab;
    opacity: 0.6;
    transition: opacity 0.15s ease;
}

.grip-handle:hover {
    opacity: 1;
}

.grip-handle:active {
    cursor: grabbing;
}

/* Drag and Drop Styles */
.column-item.dragging {
    opacity: 0.5;
    transform: rotate(2deg);
}

.column-item.drag-over {
    background: var(--color-primary-50, #f0f9ff);
    border: 1px dashed var(--color-primary-300, #93c5fd);
}

/* Dark mode drag-over styling with blue background and opacity */
[data-theme="dark"] .column-item.drag-over,
.snap-grid.dark .column-item.drag-over,
body.dark .column-item.drag-over {
    background: rgba(71, 12, 237, 0.1); /* Blue primary with 10% opacity */
    border: 1px dashed var(--palette-blue-primary, #470CED);
}

.drag-indicator {
    position: absolute;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--color-primary-600, #470ced);
    border-radius: 1px;
    z-index: 1000;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.drag-indicator.active {
    opacity: 1;
}

.column-list {
    position: relative;
}

.column-item label {
    font-size: var(--font-size-sm, 14px);
    font-weight: 500;
    color: var(--color-text-primary, #18181b);
    cursor: pointer;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Dark mode text color for column item labels */
[data-theme="dark"] .column-item label,
.snap-grid.dark .column-item label,
body.dark .column-item label {
    color: var(--text-primary, var(--palette-gray-400));
}

/* Checkbox wrapper styling for column visibility */
.column-item .checkbox-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    cursor: pointer;
    flex-shrink: 0;
}

.column-item .checkbox-wrapper img {
    width: 16px;
    height: 16px;
    transition: opacity 0.2s ease;
}

/* Column item checkbox styling for unchecked state */
.column-item .checkbox-wrapper img[src*="uncheckedbox-ic"] {
    opacity: 0.2;
}

[data-theme="dark"] .column-item .checkbox-wrapper img[src*="uncheckedbox-ic"],
body.dark .column-item .checkbox-wrapper img[src*="uncheckedbox-ic"],
.snap-grid.dark .column-item .checkbox-wrapper img[src*="uncheckedbox-ic"] {
    opacity: 1 !important;
}



.select-all-item {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 8px;
    padding-bottom: 8px;
}

.column-label {
    font-size: 14px;
    color: var(--text-primary);
    font-weight: 600;
    cursor: pointer;
    flex: 1;
}

/* Webkit scrollbar styling for column list */
.column-list::-webkit-scrollbar {
    width: 6px;
}

.column-list::-webkit-scrollbar-track {
    background: transparent;
}

.column-list::-webkit-scrollbar-thumb {
    background: #D9DDEB;
    border-radius: 3px;
}

.column-list::-webkit-scrollbar-thumb:hover {
    background: #E9EBF2;
}

/* Dark mode scrollbar for column list - standardized colors */
[data-theme="dark"] .column-list,
.snap-grid.dark .column-list,
body.dark .column-list {
    scrollbar-color: #43506F transparent !important;
}

[data-theme="dark"] .column-list::-webkit-scrollbar-thumb,
.snap-grid.dark .column-list::-webkit-scrollbar-thumb,
body.dark .column-list::-webkit-scrollbar-thumb {
    background: #43506F !important;
}

[data-theme="dark"] .column-list::-webkit-scrollbar-thumb:hover,
.snap-grid.dark .column-list::-webkit-scrollbar-thumb:hover,
body.dark .column-list::-webkit-scrollbar-thumb:hover {
    background: #43506F !important;
}

/* Checkbox wrapper styling for column visibility */
.column-item .checkbox-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    cursor: pointer;
    flex-shrink: 0;
}

.column-item .checkbox-wrapper img {
    width: 16px;
    height: 16px;
    transition: opacity 0.2s ease;
}

/* Column item checkbox styling for unchecked state */
.column-item .checkbox-wrapper img[src*="uncheckedbox-ic"] {
    opacity: 0.2;
}

[data-theme="dark"] .column-item .checkbox-wrapper img[src*="uncheckedbox-ic"],
body.dark .column-item .checkbox-wrapper img[src*="uncheckedbox-ic"],
.snap-grid.dark .column-item .checkbox-wrapper img[src*="uncheckedbox-ic"] {
    opacity: 1 !important;
}

/* Old column-list-item styles removed - replaced by column-item */

/* Dark mode support for tabbed menu */
[data-theme="dark"] .menu-tab-header {
    background: var(--bg-tertiary);
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .menu-tab-btn.active {
    background: var(--bg-secondary);
}

[data-theme="dark"] .filter-type-dropdown,
[data-theme="dark"] .filter-input,
[data-theme="dark"] .column-search-input {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

.column-search-input::placeholder {
    color: var(--color-text-placeholder, #dce0e5);
    font-family: var(--grid-font-family);
    font-size: 12px;
}

[data-theme="dark"] .filter-apply-btn,
[data-theme="dark"] .filter-clear-btn {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .menu-option-with-icon:hover {
    background: var(--palette-gray-700, #2F3341) !important;
}

[data-theme="dark"] .menu-option-label {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

/* Enhanced dark mode support for menu options */
[data-theme="dark"] .menu-option-with-icon,
.snap-grid.dark .menu-option-with-icon,
body.dark .menu-option-with-icon {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

.snap-grid-menu-divider {
    height: 1px;
    background: var(--grid-border-color);
    margin: 4px 0;
}

/* ==========================================================================
   Filter Input Styles
   ========================================================================== */

.snap-grid-filter-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--grid-border-color);
    border-radius: 4px;
    background: var(--bg-primary);
    color: var(--grid-text-accent);
    font-family: inherit;
    font-size: var(--font-size-sm);
    outline: none;
    transition: border-color 0.2s ease;
}

.snap-grid-filter-input:focus {
    outline: none;
    border-color: var(--color-primary-600, #470ced);
    box-shadow: 0 0 0 1px var(--color-primary-600, #470ced);
}

.snap-grid-filter-input::placeholder {
    color: var(--color-text-placeholder, #dce0e5);
    font-family: var(--grid-font-family);
    font-size: 12px;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .snap-grid {
        --grid-cell-padding: var(--spacing-xs, 8px);
        --grid-font-size: var(--font-size-xs, 12px);
        border-radius: 4px;
    }

    .snap-grid-header-cell,
    .snap-grid-cell {
        min-width: 100px;
    }

    .snap-grid-column-menu-btn {
        width: 32px;
        height: 32px;
        opacity: 1; /* Always visible on mobile */
    }

    .snap-grid-column-menu {
        min-width: 180px;
    }
}

@media (max-width: 480px) {
    .snap-grid {
        --grid-cell-padding: var(--spacing-xs, 6px);
        --grid-font-size: var(--font-size-xs, 11px);
        --grid-row-height: 36px;
        --grid-header-height: 44px;
    }

    .snap-grid-header-cell,
    .snap-grid-cell {
        min-width: 80px;
    }
}

/* ==========================================================================
   Performance Optimizations
   ========================================================================== */

/* Avoid transforms on viewport to keep sticky pinned columns reliable */
.snap-grid-viewport { will-change: auto; }

.snap-grid-row {
    contain: layout style;
}

.snap-grid-cell {
    contain: layout;
}

/* Reduce repaints during scrolling */
.snap-grid-body {
    contain: layout style;
}

/* ==========================================================================
   Accessibility Enhancements
   ========================================================================== */

.snap-grid:focus-within {
    outline: 2px solid var(--grid-focus-outline);
    outline-offset: 2px;
}

.snap-grid-header-cell:focus,
.snap-grid-cell:focus { outline: none; box-shadow: none; }

/* Screen reader only content */
.snap-grid-sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .snap-grid {
        --grid-border-color: ButtonText;
        --grid-text-color: ButtonText;
        --grid-row-bg: ButtonFace;
        --grid-header-bg: ButtonFace;
    }

    .snap-grid-row:hover {
        background: Highlight;
        color: HighlightText;
    }

    .snap-grid-row.selected {
        background: Highlight;
        color: HighlightText;
        outline: 2px solid ButtonText;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .snap-grid,
    .snap-grid * {
        transition: none !important;
        animation: none !important;
    }
}

/* ==========================================================================
   Test Page Styles
   ========================================================================== */

/* Test page layout */
body {
    font-family: var(--grid-font-family);
    margin: 20px;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.test-container {
    max-width: 1400px;
    margin: 0 auto;
}

.test-header {
    margin-bottom: 20px;
    text-align: center;
}

.test-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-accent);
    margin-bottom: 10px;
}

.test-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
    align-items: center;
}

.test-button {
    padding: 8px 16px;
    border: 1px solid var(--btn-border);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.test-button:hover {
    background: var(--btn-hover);
}

.test-button.primary {
    background: var(--palette-blue-primary);
    color: white;
    border-color: var(--palette-blue-primary);
}

.test-button.active {
    background: var(--palette-blue-primary);
    color: white;
    border-color: var(--palette-blue-primary);
}

.dataset-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-left: auto;
    flex-wrap: wrap;
}

.dataset-label {
    font-weight: 600;
    margin-right: 10px;
}

/* Grid container for testing - minimal styling */
.grid-container {
    height: 80vh;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
}

.test-results {
    background: var(--bg-secondary);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.test-log {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
    background: var(--bg-primary);
    padding: 10px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
    white-space: pre-wrap;
}

.theme-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px;
    border: 1px solid var(--btn-border);
    border-radius: 6px;
    background: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
    font-size: 14px;
    z-index: 1000;
}

.stats-display {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
    font-size: 14px;
}

.stat-item {
    background: var(--bg-primary);
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.stat-label {
    font-weight: 600;
    color: var(--text-accent);
}

/* Responsive adjustments for test pages */
@media (max-width: 768px) {
    .test-container {
        margin: 10px;
    }

    .test-title {
        font-size: 1.5rem;
    }

    .test-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .dataset-controls {
        margin-left: 0;
        justify-content: center;
    }

    .grid-container {
        height: 400px;
    }
}

/* Loading Overlay Styles */
.snap-grid-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: inherit;
}

[data-theme="dark"] .snap-grid-loading-overlay {
    background: rgba(20, 20, 20, 0.95);
}

.snap-grid-loading-content {
    text-align: center;
    padding: 2rem;
    background: var(--color-surface);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--color-border);
    min-width: 300px;
    max-width: 400px;
}

.snap-grid-loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--color-border);
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    animation: snap-grid-spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes snap-grid-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.snap-grid-loading-text {
    font-size: 1rem;
    font-weight: 500;
    color: var(--color-text);
    margin-bottom: 1rem;
}

.snap-grid-loading-progress {
    margin-bottom: 1.5rem;
}

.snap-grid-progress-bar {
    width: 100%;
    height: 8px;
    background: var(--color-border);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.snap-grid-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
    border-radius: 4px;
    transition: width 0.3s ease;
}

.snap-grid-progress-text {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    font-weight: 500;
}

.snap-grid-cancel-loading {
    background: var(--color-danger);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.snap-grid-cancel-loading:hover {
    background: var(--color-danger-dark);
}

.snap-grid-cancel-loading:active {
    transform: translateY(1px);
}

/* ==========================================================================
   Old UI alignment for Show/Hide (Visibility) tab
   - Match spacing, border, and font sizes from snap-old-grid
   ========================================================================== */
/* Filter tab: increase spacing between checkbox items */
.menu-tab-panel[data-tab-panel="filter"] .filter-checkbox-list {
    gap: 12px !important;
}

/* Visibility tab: match filter list (no padding/border, 12px gap) */
.menu-tab-panel[data-tab-panel="visibility"] .column-list {
    gap: 4px !important;
    border: 1px solid var(--grid-border-color) !important;
    border-radius: var(--border-radius-md, 6px) !important;
    padding: var(--spacing-sm, 8px) !important;
    margin-top: var(--spacing-md, 16px) !important;
}

/* Remove divider styling under Select All item */
.menu-tab-panel[data-tab-panel="visibility"] .select-all-item {
    border: none !important;
    margin: 0 !important;
    padding: var(--spacing-xs, 8px) !important;
}

/* Visibility tab list text should be 12px like filter list */
.menu-tab-panel[data-tab-panel="visibility"] .column-item label {
    font-size: 12px !important;
}

/* Enhanced Dataset Control Styles - Updated existing rules */

/* 100K and 1M buttons use same style as other dataset buttons */
#btn-100000, #btn-1000000 {
    /* Use default test-button styles - no custom styling needed */
    /* These selectors are intentionally empty to override any potential conflicting styles */
    display: inline-block;
}

/* No filter message for fixed columns */
.no-filter-message {
    padding: 20px;
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    background: var(--grid-row-hover-bg);
    border-radius: 6px;
    margin: 8px 0;
}

/* ==========================================================================
   Save Layout Popup Styles
   ========================================================================== */

#saveLayoutPopup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

#saveLayoutPopup .popup-card {
    width: 270px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    padding: 40px 20px 20px;
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

#saveLayoutPopup .popup-close {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

#saveLayoutPopup .popup-close:hover {
    opacity: 1;
}

#saveLayoutPopup .popup-title {
    color: #000000;
    font-size: 14px;
    font-weight: 500;
    margin: 0;
    font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

#saveLayoutPopup .layout-input-container {
    border: 1px solid var(--grid-border-color, #dce0e5);
    border-radius: 4px;
    height: 40px;
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    transition: border-color 0.2s ease;
    background: white;
}

#saveLayoutPopup .layout-input-container input {
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
    padding: 0 12px;
    font-size: 12px;
    font-weight: 500;
    color: #606F95;
    background: transparent;
    font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

#saveLayoutPopup .layout-input-container input::placeholder {
    color: #606F95;
}

#saveLayoutPopup .layout-input-container:focus-within {
    border-color: var(--color-primary-600, #470CED);
    box-shadow: 0 0 0 1px var(--color-primary-600, #470CED);
}

#saveLayoutPopup .layout-input-container.error {
    border-color: #FF391F !important;
}

#saveLayoutPopup .layout-input-container.error:focus-within {
    border-color: #FF391F !important;
    box-shadow: 0 0 0 1px #FF391F !important;
}

/* More specific error state to override focus */
#saveLayoutPopup .popup-form-group .layout-input-container.error {
    border-color: #FF391F !important;
}

#saveLayoutPopup .popup-form-group .layout-input-container.error:focus-within {
    border-color: #FF391F !important;
    box-shadow: 0 0 0 1px #FF391F !important;
}

#saveLayoutPopup .layout-input-container {
    margin-bottom: 0;
}

/* Form group with 8px gaps between input, error, and checkbox */
#saveLayoutPopup .popup-form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

#saveLayoutPopup .checkbox-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

#saveLayoutPopup .filter-checkbox-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm, 10px);
    padding: var(--spacing-xs, 8px) var(--spacing-xs, 8px) var(--spacing-xs, 8px) 0;
    border-radius: var(--border-radius-sm, 4px);
    cursor: pointer;
}

/* Removed hover effect for popup checkbox */
#saveLayoutPopup .filter-checkbox-item:hover {
    background: transparent !important;
}

#saveLayoutPopup .checkbox-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    cursor: pointer;
    flex-shrink: 0;
}

#saveLayoutPopup .checkbox-wrapper img {
    width: 16px;
    height: 16px;
    transition: opacity 0.2s ease;
}

#saveLayoutPopup .filter-checkbox-label {
    font-size: var(--font-size-xs, 12px);
    font-weight: 500;
    color: var(--color-text-primary, #18181b);
    cursor: pointer;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#saveLayoutPopup .checkbox-text {
    font-size: 12px;
    font-weight: 500;
    color: #606F95;
    font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

#saveLayoutPopup .error-message {
    color: #FF391F;
    font-size: 12px;
    margin: 0;
    display: none;
    text-align: left;
    width: 100%;
    font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

#saveLayoutPopup .save-layout-button {
    width: 100%;
    height: 32px;
    background: #470CED;
    border: none;
    border-radius: 4px;
    color: white;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
    font-family: 'Amazon Ember', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

#saveLayoutPopup .save-layout-button:hover {
    background-color: #2A00A0;
}

#saveLayoutPopup .save-layout-button:not(.active) {
    background: #E9EBF2;
    color: #B4B9C5;
    cursor: not-allowed;
    opacity: 0.5;
}

#saveLayoutPopup .save-layout-button:not(.active):hover {
    background: #E9EBF2;
}

/* Dark mode support for save layout popup */
[data-theme="dark"] #saveLayoutPopup .popup-card,
.snap-grid.dark #saveLayoutPopup .popup-card,
body.dark #saveLayoutPopup .popup-card {
    background: var(--palette-gray-800, #1A1D23) !important;
    border: 1px solid var(--palette-gray-700, #2F3341) !important;
}

[data-theme="dark"] #saveLayoutPopup .popup-title,
.snap-grid.dark #saveLayoutPopup .popup-title,
body.dark #saveLayoutPopup .popup-title {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] #saveLayoutPopup .layout-input-container,
.snap-grid.dark #saveLayoutPopup .layout-input-container,
body.dark #saveLayoutPopup .layout-input-container {
    background: var(--palette-gray-750, #2A2F37) !important;
    border-color: #470CED !important;
}

[data-theme="dark"] #saveLayoutPopup .layout-input-container input,
.snap-grid.dark #saveLayoutPopup .layout-input-container input,
body.dark #saveLayoutPopup .layout-input-container input {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

[data-theme="dark"] #saveLayoutPopup .layout-input-container input::placeholder,
.snap-grid.dark #saveLayoutPopup .layout-input-container input::placeholder,
body.dark #saveLayoutPopup .layout-input-container input::placeholder {
    color: var(--palette-gray-500, #8A92A6) !important;
}

[data-theme="dark"] #saveLayoutPopup .filter-checkbox-label,
.snap-grid.dark #saveLayoutPopup .filter-checkbox-label,
body.dark #saveLayoutPopup .filter-checkbox-label {
    color: var(--palette-gray-400, #B4B9C5) !important;
}

/* Removed hover effect for popup checkbox in dark mode */

[data-theme="dark"] #saveLayoutPopup .checkbox-wrapper img[src*="uncheckedbox-ic"],
.snap-grid.dark #saveLayoutPopup .checkbox-wrapper img[src*="uncheckedbox-ic"],
body.dark #saveLayoutPopup .checkbox-wrapper img[src*="uncheckedbox-ic"] {
    opacity: 1 !important;
}

/* Dark mode input container styles */
[data-theme="dark"] #saveLayoutPopup .layout-input-container,
.snap-grid.dark #saveLayoutPopup .layout-input-container,
body.dark #saveLayoutPopup .layout-input-container {
    border-color: var(--border-color) !important;
    background: var(--bg-primary) !important;
}

[data-theme="dark"] #saveLayoutPopup .layout-input-container:focus-within,
.snap-grid.dark #saveLayoutPopup .layout-input-container:focus-within,
body.dark #saveLayoutPopup .layout-input-container:focus-within {
    border-color: var(--color-primary-600, #470CED) !important;
    box-shadow: 0 0 0 1px var(--color-primary-600, #470CED) !important;
}

[data-theme="dark"] #saveLayoutPopup .layout-input-container.error,
.snap-grid.dark #saveLayoutPopup .layout-input-container.error,
body.dark #saveLayoutPopup .layout-input-container.error {
    border-color: #FF391F !important;
}

[data-theme="dark"] #saveLayoutPopup .layout-input-container.error:focus-within,
.snap-grid.dark #saveLayoutPopup .layout-input-container.error:focus-within,
body.dark #saveLayoutPopup .layout-input-container.error:focus-within {
    border-color: #FF391F !important;
    box-shadow: 0 0 0 1px #FF391F !important;
}

/* Dark mode disabled state for save layout button */
[data-theme="dark"] #saveLayoutPopup .save-layout-button:not(.active),
.snap-grid.dark #saveLayoutPopup .save-layout-button:not(.active),
body.dark #saveLayoutPopup .save-layout-button:not(.active) {
    background: var(--palette-gray-750, #2A2F37) !important;
    color: var(--palette-gray-400, #B4B9C5) !important;
    cursor: not-allowed;
    opacity: 0.5;
}

[data-theme="dark"] #saveLayoutPopup .save-layout-button:not(.active):hover,
.snap-grid.dark #saveLayoutPopup .save-layout-button:not(.active):hover,
body.dark #saveLayoutPopup .save-layout-button:not(.active):hover {
    background: var(--palette-gray-750, #2A2F37) !important;
}

/* ==========================================================================
   Notification Styles (Snap Image Studio style)
   ========================================================================== */

.notification {
  display: inline-block !important;
  width: auto !important;
  position: fixed;
  top: 80px;
  right: 20px;
  max-width: 300px;
  background-color: var(--tab-active-bg, #FFFFFF);
  color: var(--tab-text, #606F95);
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  z-index: 6000;
  opacity: 0;
  transform: translateY(-10px);
  animation: notification-show 0.3s ease forwards;
  font-size: 14px;
  border-left: 4px solid #FF9800;
}

/* Handle sidebar collapsed state */
.sidebar.collapsed ~ .main-content .notification {
  max-width: calc(100% - 180px);
}

.notification.warning {
  border-left: 4px solid #FF9800;
}

.notification.success {
  border-left: 4px solid #04AE2C;
}

@keyframes notification-show {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Dark mode notification styles */
[data-theme="dark"] .notification,
.snap-grid.dark .notification,
body.dark .notification {
  background-color: var(--tab-active-bg, #292E38);
  color: var(--tab-text, #A1AEBE);
}


/* ==========================================================================
   Disabled Dropdown - Exact Color Overrides (wins over all earlier rules)
   Spec:
   - Light mode: #C7CBDA for border, text, and icon
   - Dark mode:  #353C48 for border, text, and icon
   ========================================================================== */
/* Removed light mode disabled dropdown color overrides */

/* Removed dark mode disabled dropdown color overrides */

/* ======================================================================
   Disabled Dropdown - Final spec: #606f95 border, original icon colors, 50% opacity
   ====================================================================== */
.snap-dropdown.disabled {
  opacity: 0.3 !important;
  cursor: not-allowed !important;
}
.snap-dropdown.disabled .dropdown-header { 
  cursor: not-allowed !important;
  border-color: #606f95 !important;
}
.snap-dropdown.disabled * { cursor: not-allowed !important; }

/* Disabled dropdown icons: NO filters - use original icon colors */
.snap-dropdown.disabled .dropdown-header img {
  filter: none !important;
}

/* Dark mode: Set text color to #606f95 for disabled dropdowns */
[data-theme="dark"] .snap-dropdown.disabled .dropdown-header,
.snap-grid.dark .snap-dropdown.disabled .dropdown-header,
body.dark .snap-dropdown.disabled .dropdown-header {
  color: #606f95 !important;
}
[data-theme="dark"] .snap-dropdown.disabled .dropdown-header span,
.snap-grid.dark .snap-dropdown.disabled .dropdown-header span,
body.dark .snap-dropdown.disabled .dropdown-header span {
  color: #606f95 !important;
}


/* ======================================================================
   Disabled Dropdown - keep normal colors, only reduce opacity to 50%
   ====================================================================== */
/* Removed base color overrides - disabled dropdowns inherit normal colors */

/* Removed variable-based icon filter - using original icon colors now */


/* Disabled dropdowns: NO color overrides - inherit all normal state styles
   Only the 50% opacity above should apply */
