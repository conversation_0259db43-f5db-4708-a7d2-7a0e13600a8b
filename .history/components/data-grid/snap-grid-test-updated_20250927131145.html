<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SnapGrid Updated Test - Checkbox & Column Dragging</title>
    
    <!-- Design System CSS -->
    <link rel="stylesheet" href="../../css/tokens.css">
    <link rel="stylesheet" href="../../css/base.css">
    
    <!-- SnapGrid CSS -->
    <link rel="stylesheet" href="snap-grid.css">
    
    <style>
        body {
            background-color: var(--bg-secondary);
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">🌙 Toggle Theme</button>
    
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">SnapGrid Updated Test</h1>
            <p>Testing checkbox selection, column dragging, and new data structure</p>
        </div>
        
        <div class="test-controls">
            <button class="test-button primary" onclick="initializeGrid()">Initialize Grid</button>
            <button class="test-button" onclick="exportSelected()">Export Selected</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
            
            <div class="dataset-controls">
                <span class="dataset-label">Dataset:</span>
                <button class="test-button" onclick="loadDataset(500)" id="btn-500">500 rows</button>
                <button class="test-button active" onclick="loadDataset(1000)" id="btn-1000">1K rows</button>
                <button class="test-button" onclick="loadDataset(10000)" id="btn-10000">10K rows</button>
                <button class="test-button" onclick="loadDatasetChunked(100000)" id="btn-100000">100K rows</button>
                <button class="test-button" onclick="loadDatasetChunked(1000000)" id="btn-1000000">1M rows</button>
                <button class="test-button" onclick="loadDatasetChunked(4000000)" id="btn-4000000">4M rows</button>
            </div>
        </div>


        <div class="grid-container">
            <div id="SnapGrid"></div>
        </div>
        
        <div class="test-results">
            <h3>Test Results</h3>
            <div id="testLog" class="test-log">Ready to run tests...\n</div>
        </div>
    </div>
    
    <!-- Old Snap Grid dataset generator -->
    <script src="../../old Snap data-grid Ref code/dummy-grid-data.js"></script>
    <!-- Chunked Data Loader -->
    <script src="chunked-data-loader.js"></script>
    <!-- SnapGrid JavaScript -->
    <script src="snap-grid.js"></script>
    
    <script>
        let grid = null;
        let currentDataset = 1000;
        
        // Use old Snap Grid dataset generator to match exact fields/types
        function generateSampleData(count = 1000) {
            return generateProductData(count, { seed: 1234 });
        }
        
        // Column definitions (old grid order and types)
        const columnDefs = [
            { field: 'marketplace', headerName: 'Marketplace', width: 100, sortable: true, filterable: true,
              cellRenderer: (value) => `<img src="assets/${value}.svg" alt="${value}" style=\"width:16px;height:12px;margin-right:6px;vertical-align:middle;\" onerror=\"this.style.display='none'\"><span>${value}</span>` },
            { field: 'asin', headerName: 'ASIN', width: 120, sortable: true, filterable: true },
            { field: 'status', headerName: 'Status', width: 140, type: 'status', sortable: true, filterable: true },
            { field: 'productType', headerName: 'Product Type', width: 150, sortable: true, filterable: true },
            { field: 'brand', headerName: 'Brand', width: 140, sortable: true, filterable: true },
            { field: 'title', headerName: 'Product Title', width: 260, sortable: true, filterable: true },
            { field: 'price', headerName: 'Price', width: 100, type: 'currency', sortable: true, filterable: true },
            { field: 'sales', headerName: 'Sales', width: 100, type: 'number', sortable: true, filterable: true },
            { field: 'returns', headerName: 'Returns', width: 100, type: 'number', sortable: true, filterable: true },
            { field: 'returnRate', headerName: 'Return Rate', width: 110, sortable: true, filterable: true,
              cellRenderer: (value) => value == null ? '' : `${value}%` },
            { field: 'royalties', headerName: 'Royalties', width: 120, type: 'currency', sortable: true, filterable: true },
            { field: 'firstSold', headerName: 'First Sold', width: 140, type: 'date', sortable: true, filterable: true },
            { field: 'lastSold', headerName: 'Last Sold', width: 140, type: 'date', sortable: true, filterable: true },
            { field: 'bsr', headerName: 'BSR', width: 120, type: 'number', sortable: true, filterable: true },
            { field: 'firstPublished', headerName: 'First Published', width: 160, type: 'date', sortable: true, filterable: true },
            { field: 'lastUpdated', headerName: 'Last Updated', width: 160, type: 'date', sortable: true, filterable: true },
            { field: 'reviews', headerName: 'Reviews', width: 110, type: 'number', sortable: true, filterable: true },
            { field: 'designId', headerName: 'Design ID', width: 140, sortable: true, filterable: true }
        ];
        
        // Logging function
        function log(message) {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        // Clear log
        function clearLog() {
            document.getElementById('testLog').textContent = 'Log cleared...\n';
        }
        
        // Update stats display
        function updateStats() {
            if (grid) {
                const stats = grid.getStats();
                const selectedData = grid.getSelectedData();

                // Add null checks to prevent errors
                const totalRowsEl = document.getElementById('totalRows');
                const selectedRowsEl = document.getElementById('selectedRows');
                const renderTimeEl = document.getElementById('renderTime');

                if (totalRowsEl) totalRowsEl.textContent = stats.totalRows;
                if (selectedRowsEl) selectedRowsEl.textContent = selectedData.length;
                if (renderTimeEl) renderTimeEl.textContent = stats.lastRenderDuration.toFixed(2) + 'ms';
            }
        }
        
        // Initialize grid
        function initializeGrid() {
            loadDataset(currentDataset);
        }
        
        // Load dataset (for smaller datasets)
        function loadDataset(count) {
            log(`Loading ${count} rows...`);
            currentDataset = count;
            
            // Update button states
            document.querySelectorAll('.dataset-controls .test-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(`btn-${count}`).classList.add('active');
            
            try {
                const data = generateSampleData(count);
                const container = document.getElementById('SnapGrid');
                
                if (grid) {
                    grid.destroy();
                    log('Destroyed existing grid');
                }
                
                const startTime = performance.now();
                
                grid = new SnapGrid(container, {
                    data: data,
                    columns: columnDefs,
                    checkboxSelection: true,
                    headerCheckboxSelection: true,
                    columnDragging: true,
                    virtualScrolling: true,
                    sortable: true,
                    filterable: true,
                    resizable: true,
                    editable: false,
                    
                    onSelectionChanged: (selectedData) => {
                        log(`Selection changed: ${selectedData.length} rows selected`);
                        updateStats();
                    },
                    
                    onColumnMoved: (fromIndex, toIndex) => {
                        log(`Column moved from index ${fromIndex} to ${toIndex}`);
                    },
                    
                    onRowClick: (rowData, rowIndex) => {
                        log(`Row clicked: ${rowData.productTitle} (index: ${rowIndex})`);
                    },
                    
                    onCellEdit: (field, newValue, oldValue, rowData) => {
                        log(`Cell edited: ${field} changed from "${oldValue}" to "${newValue}"`);
                    },

                    onDeleteProduct: (selectedData) => {
                        log(`Delete Product confirmed for ${selectedData.length} items:`);
                        selectedData.forEach(item => {
                            log(`  - ${item.productTitle || item.title || 'Unknown Product'}`);
                        });
                        // Simulate deletion by removing from grid
                        setTimeout(() => {
                            log('Products deleted successfully');
                        }, 500);
                    },

                    onDeleteDesign: (selectedData) => {
                        log(`Delete Design confirmed for ${selectedData.length} items:`);
                        selectedData.forEach(item => {
                            log(`  - ${item.productTitle || item.title || 'Unknown Product'}`);
                        });
                        // Simulate deletion by removing from grid
                        setTimeout(() => {
                            log('Design and associated products deleted successfully');
                        }, 500);
                    }
                });
                
                const endTime = performance.now();
                log(`Grid loaded with ${count} rows in ${(endTime - startTime).toFixed(2)}ms`);
                updateStats();
                
            } catch (error) {
                log(`Failed to load dataset: ${error.message}`);
                console.error('Dataset loading error:', error);
            }
        }

        // Load dataset with chunked loading (for large datasets)
        async function loadDatasetChunked(count) {
            console.log(`loadDatasetChunked called with count: ${count}`);
            log(`Loading ${count.toLocaleString()} rows with chunked loading...`);
            currentDataset = count;
            
            // Update button states
            document.querySelectorAll('.dataset-controls .test-button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(`btn-${count}`).classList.add('active');
            
            try {
                const container = document.getElementById('SnapGrid');
                
                if (grid) {
                    grid.destroy();
                    log('Destroyed existing grid');
                }
                
                // Create empty grid first
                grid = new SnapGrid(container, {
                    data: [],
                    columns: columnDefs,
                    checkboxSelection: true,
                    headerCheckboxSelection: true,
                    columnDragging: true,
                    virtualScrolling: true,
                    sortable: true,
                    filterable: true,
                    resizable: true,
                    editable: false,
                    
                    onSelectionChanged: (selectedData) => {
                        log(`Selection changed: ${selectedData.length} rows selected`);
                        updateStats();
                    },
                    
                    onColumnMoved: (fromIndex, toIndex) => {
                        log(`Column moved from index ${fromIndex} to ${toIndex}`);
                    },
                    
                    onRowClick: (rowData, rowIndex) => {
                        log(`Row clicked: ${rowData.title} (index: ${rowIndex})`);
                    },
                    
                    onCellEdit: (field, newValue, oldValue, rowData) => {
                        log(`Cell edited: ${field} changed from "${oldValue}" to "${newValue}"`);
                    }
                });
                
                const startTime = performance.now();
                
                // Create data generator using the same function as regular loading
                const dataGenerator = (chunkSize, offset) => {
                    // Generate data with consistent seed but different starting point
                    const data = generateSampleData(chunkSize, { seed: 1234 });
                    
                    // Update ASINs and other unique fields to be offset-aware
                    return data.map((record, index) => ({
                        ...record,
                        asin: `B${String(offset + index).padStart(9, '0')}`,
                        designId: `DESIGN-${String(offset + index).padStart(6, '0')}`,
                        title: `${record.brand} ${record.productType} - Design ${offset + index + 1}`
                    }));
                };
                
                // Load data in chunks
                await grid.loadDataInChunks(count, dataGenerator, {
                    chunkSize: 10000, // 10K records per chunk
                    delayBetweenChunks: 16, // 60fps
                    maxMemoryChunks: 5, // Keep 5 chunks in memory
                    enableMemoryManagement: false, // Disable memory management for large datasets
                    
                    onProgress: (progress, totalRecords, loadedRecords, currentChunk, totalChunks) => {
                        log(`Loading progress: ${progress.toFixed(1)}% (${loadedRecords.toLocaleString()} / ${totalRecords.toLocaleString()} records) - Chunk ${currentChunk}/${totalChunks}`);
                    },
                    
                    onChunkLoaded: (chunkData, chunkIndex, allData) => {
                        log(`Chunk ${chunkIndex + 1} loaded: ${chunkData.length} records (Total: ${allData.length.toLocaleString()})`);
                        updateStats();
                    },
                    
                    onComplete: (finalData) => {
                        const endTime = performance.now();
                        log(`Chunked loading completed: ${finalData.length.toLocaleString()} rows in ${(endTime - startTime).toFixed(2)}ms`);
                        log(`Verification: Expected ${count.toLocaleString()}, Got ${finalData.length.toLocaleString()} records`);
                        updateStats();
                    },
                    
                    onError: (error) => {
                        log(`Chunked loading failed: ${error.message}`);
                        console.error('Chunked loading error:', error);
                    },
                    
                    onCancel: (data) => {
                        log(`Chunked loading cancelled. Loaded ${data.length.toLocaleString()} rows`);
                        updateStats();
                    }
                });
                
            } catch (error) {
                log(`Failed to load chunked dataset: ${error.message}`);
                console.error('Chunked dataset loading error:', error);
            }
        }
        
        // Test checkboxes
        function testCheckboxes() {
            if (!grid) {
                log('No grid available. Initialize grid first.');
                return;
            }
            
            log('Testing checkbox functionality...');
            log('- Click individual row checkboxes to select/deselect');
            log('- Click header checkbox to select/deselect all');
            log('- Selection state will be logged automatically');
        }
        
        // Test column dragging
        function testColumnDragging() {
            if (!grid) {
                log('No grid available. Initialize grid first.');
                return;
            }
            
            log('Testing column dragging...');
            log('- Hover over column headers (except checkbox column)');
            log('- Drag and drop to reorder columns');
            log('- Column moves will be logged automatically');
        }
        
        // Export selected rows
        function exportSelected() {
            if (!grid) {
                log('No grid available. Initialize grid first.');
                return;
            }
            
            const selectedData = grid.getSelectedData();
            if (selectedData.length === 0) {
                log('No rows selected for export');
                return;
            }
            
            log(`Exporting ${selectedData.length} selected rows...`);
            
            // Create CSV content
            const headers = columnDefs.map(col => col.headerName || col.field);
            const csvContent = [
                headers.join(','),
                ...selectedData.map(row => 
                    columnDefs.map(col => {
                        const value = row[col.field];
                        return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                    }).join(',')
                )
            ].join('\n');
            
            // Download CSV
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `snap-grid-selected-${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            URL.revokeObjectURL(url);
            
            log(`Export completed: ${selectedData.length} rows`);
        }
        
        // Test min-width functionality
        function testMinWidth() {
            if (!grid) {
                log('Grid not initialized. Please initialize grid first.', 'error');
                return;
            }
            
            log('Testing min-width functionality...');
            
            // Test different column types
            const testColumns = [
                { field: 'marketplace', headerName: 'Marketplace' },
                { field: 'productType', headerName: 'Product Type' },
                { field: 'title', headerName: 'Product Title' },
                { field: 'checkbox', headerName: '' },
                { field: 'preview', headerName: 'Preview' },
                { field: 'actions', headerName: 'Actions' }
            ];
            
            testColumns.forEach(column => {
                if (grid.calculateMinHeaderWidth) {
                    const minWidth = grid.calculateMinHeaderWidth(column);
                    log(`${column.field}: min-width = ${minWidth}px (header: "${column.headerName}")`);
                } else {
                    log(`calculateMinHeaderWidth method not found!`, 'error');
                    return;
                }
            });
            
            // Test setting width below minimum
            log('Testing width enforcement...');
            const testField = 'marketplace';
            const originalWidth = grid.getColumn(testField)?.width;
            log(`Original width for ${testField}: ${originalWidth}px`);
            
            // Try to set width below minimum
            grid.setColumnWidth(testField, 50);
            const newWidth = grid.getColumn(testField)?.width;
            log(`After setting to 50px, actual width: ${newWidth}px (should be >= min-width)`);
            
            // Restore original width
            if (originalWidth) {
                grid.setColumnWidth(testField, originalWidth);
                log(`Restored original width: ${originalWidth}px`);
            }
            
            log('Min-width test completed!', 'success');
        }
        
        // Test ellipsis functionality
        function testEllipsis() {
            if (!grid) {
                log('Grid not initialized. Please initialize grid first.', 'error');
                return;
            }
            
            log('Testing ellipsis functionality...');
            
            // Check if cell content wrappers exist
            const cells = document.querySelectorAll('.snap-grid-cell-content');
            log(`Found ${cells.length} cell content wrappers`);
            
            if (cells.length > 0) {
                const firstCell = cells[0];
                const styles = window.getComputedStyle(firstCell);
                const hasEllipsis = styles.textOverflow === 'ellipsis';
                const hasOverflow = styles.overflow === 'hidden';
                const hasWhiteSpace = styles.whiteSpace === 'nowrap';
                
                log(`Ellipsis CSS check: text-overflow=${styles.textOverflow}, overflow=${styles.overflow}, white-space=${styles.whiteSpace}`);
                
                if (hasEllipsis && hasOverflow && hasWhiteSpace) {
                    log('✅ Ellipsis CSS is properly applied!', 'success');
                } else {
                    log('❌ Ellipsis CSS is not properly applied!', 'error');
                }
            } else {
                log('❌ No cell content wrappers found!', 'error');
            }
            
            // Check for long text that should be truncated
            const titleCells = document.querySelectorAll('.snap-grid-cell[data-field="title"] .snap-grid-cell-content');
            let foundTruncated = false;
            
            titleCells.forEach((cell, index) => {
                const text = cell.textContent || cell.innerText;
                if (text && text.length > 30) { // Assuming long text
                    log(`Long text found in cell ${index}: "${text}"`);
                    foundTruncated = true;
                }
            });
            
            if (foundTruncated) {
                log('✅ Long text found - check if ellipsis is working visually', 'success');
            } else {
                log('ℹ️ No long text found to test ellipsis', 'info');
            }
            
            log('Ellipsis test completed!', 'success');
        }
        
        // Toggle theme
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            
            const button = document.querySelector('.theme-toggle');
            button.textContent = newTheme === 'dark' ? '☀️ Toggle Theme' : '🌙 Toggle Theme';
            
            log(`Theme changed to: ${newTheme}`);
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            
            const button = document.querySelector('.theme-toggle');
            button.textContent = savedTheme === 'dark' ? '☀️ Toggle Theme' : '🌙 Toggle Theme';
            
            log('SnapGrid updated test page loaded. Auto-initializing grid...');
            
            // Test if 100K, 1M, and 4M buttons exist
            const btn100k = document.getElementById('btn-100000');
            const btn1m = document.getElementById('btn-1000000');
            const btn4m = document.getElementById('btn-4000000');
            console.log('100K button found:', !!btn100k);
            console.log('1M button found:', !!btn1m);
            console.log('4M button found:', !!btn4m);
            
            if (btn100k) {
                log('100K button is available', 'success');
            } else {
                log('100K button NOT found!', 'error');
            }
            
            if (btn1m) {
                log('1M button is available', 'success');
            } else {
                log('1M button NOT found!', 'error');
            }
            
            if (btn4m) {
                log('4M button is available', 'success');
            } else {
                log('4M button NOT found!', 'error');
            }
            
            // Auto-initialize grid with default dataset
            initializeGrid();
        });
    </script>
</body>
</html>

