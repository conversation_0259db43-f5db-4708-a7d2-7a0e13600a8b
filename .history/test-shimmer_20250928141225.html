<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shimmer Effect Test</title>

    <!-- Amazon Ember Font Family -->
    <style>
        @font-face {
            font-family: 'Amazon Ember';
            src: url('fonts/AmazonEmber_Regular.woff2') format('woff2'),
                 url('fonts/AmazonEmber_Regular.woff') format('woff'),
                 url('fonts/AmazonEmber_Regular.ttf') format('truetype');
            font-weight: 400;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Amazon Ember';
            src: url('fonts/AmazonEmber_Bold.woff2') format('woff2'),
                 url('fonts/AmazonEmber_Bold.woff') format('woff'),
                 url('fonts/AmazonEmber_Bold.ttf') format('truetype');
            font-weight: 700;
            font-style: normal;
            font-display: swap;
        }

        @font-face {
            font-family: 'Amazon Ember';
            src: url('fonts/Amazon-Ember-Medium.woff2') format('woff2'),
                 url('fonts/Amazon-Ember-Medium.woff') format('woff'),
                 url('fonts/Amazon-Ember-Medium.ttf') format('truetype');
            font-weight: 500;
            font-style: normal;
            font-display: swap;
        }
    </style>

    <!-- SnapGrid CSS -->
    <link rel="stylesheet" href="components/data-grid/snap-grid.css">

    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Amazon Ember', Arial, sans-serif;
            padding: 40px;
            margin: 0;
        }

        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .theme-toggle {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .theme-toggle:hover {
            background: #0056b3;
        }

        .test-title {
            color: #333;
            margin-bottom: 20px;
        }

        .test-description {
            color: #666;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">🌙 Toggle Dark Mode</button>

    <div class="test-container">
        <h1 class="test-title">Shimmer Effect Test</h1>
        <p class="test-description">Testing the shimmer animation on loading text</p>

        <!-- Simulate the loading overlay structure -->
        <div class="snap-grid-loading-overlay" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 9999;">
            <div class="snap-grid-loading-content">
                <div class="snap-grid-loading-main-content">
                    <div class="snap-grid-loading-text">Preparing products..</div>
                    <div class="snap-grid-loading-percentage">0.0%</div>
                    <div class="snap-grid-popup-loaded-info">
                        <img src="assets/data-cell-ic.svg" alt="Data" class="loaded-info-icon">
                        <span class="snap-grid-loaded-count">0 / 0</span>
                    </div>
                </div>
                <div class="snap-grid-cancel-loading-container">
                    <button class="snap-grid-cancel-loading" onclick="this.closest('.snap-grid-loading-overlay').style.display='none'">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            document.body.setAttribute('data-theme', document.body.getAttribute('data-theme') === 'dark' ? 'light' : 'dark');
        }

        // Set initial theme
        document.body.setAttribute('data-theme', 'light');
    </script>
</body>
</html>
