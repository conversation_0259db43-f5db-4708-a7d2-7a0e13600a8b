<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Loading UI</title>
    <link rel="stylesheet" href="snapapp.css">
    <link rel="stylesheet" href="components/data-grid/snap-grid.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Amazon Ember', Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-grid {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
            position: relative;
            background: white;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }
        
        button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            cursor: pointer;
        }
        
        button:hover {
            background: #f0f0f0;
        }
        
        .theme-toggle {
            margin-left: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Snap Grid Loading UI Test</h1>
        
        <div class="controls">
            <button onclick="showLoading()">Show Loading</button>
            <button onclick="hideLoading()">Hide Loading</button>
            <button onclick="simulateProgress()">Simulate Progress</button>
            <button class="theme-toggle" onclick="toggleTheme()">Toggle Dark Mode</button>
        </div>
        
        <div class="test-grid" id="testGrid">
            <p style="padding: 20px; margin: 0; color: #666;">
                Test grid container. Click "Show Loading" to see the new loading UI.
            </p>
        </div>
    </div>

    <script src="components/data-grid/snap-grid.js"></script>
    <script>
        let grid;
        let progressInterval;
        
        // Initialize a minimal grid instance for testing
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('testGrid');
            
            // Create a minimal grid-like object for testing
            grid = {
                container: container,
                loadingOverlay: null,
                
                showLoadingState(show = true) {
                    if (!this.container) return;

                    if (show) {
                        // Create loading overlay if it doesn't exist
                        if (!this.loadingOverlay) {
                            this.loadingOverlay = document.createElement('div');
                            this.loadingOverlay.className = 'snap-grid-loading-overlay';
                            this.loadingOverlay.innerHTML = `
                                <div class="snap-grid-loading-content">
                                    <div class="snap-grid-loading-main-content">
                                        <div class="snap-grid-loading-text">Preparing products..</div>
                                        <div class="snap-grid-loading-percentage">0.0%</div>
                                        <div class="snap-grid-popup-loaded-info">
                                            <img src="assets/data-cell-ic.svg" alt="Data" class="loaded-info-icon">
                                            <span class="snap-grid-loaded-count">0 / 0</span>
                                        </div>
                                    </div>
                                    <div class="snap-grid-cancel-loading-container">
                                        <button class="snap-grid-cancel-loading" onclick="hideLoading()">
                                            Cancel Loading
                                        </button>
                                    </div>
                                </div>
                            `;
                        }

                        this.container.style.position = 'relative';
                        this.container.appendChild(this.loadingOverlay);
                    } else {
                        if (this.loadingOverlay && this.loadingOverlay.parentNode) {
                            this.loadingOverlay.parentNode.removeChild(this.loadingOverlay);
                        }
                    }
                },
                
                updateLoadingProgress(progress, totalRecords, loadedRecords) {
                    if (!this.loadingOverlay) return;

                    const percentageElement = this.loadingOverlay.querySelector('.snap-grid-loading-percentage');
                    const countElement = this.loadingOverlay.querySelector('.snap-grid-loaded-count');

                    if (percentageElement) {
                        percentageElement.textContent = `${progress.toFixed(1)}%`;
                    }

                    if (countElement) {
                        countElement.textContent = `${loadedRecords.toLocaleString()} / ${totalRecords.toLocaleString()}`;
                    }
                }
            };
        });
        
        function showLoading() {
            grid.showLoadingState(true);
        }
        
        function hideLoading() {
            grid.showLoadingState(false);
            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }
        }
        
        function simulateProgress() {
            showLoading();
            
            let progress = 0;
            const totalRecords = 1167765;
            
            progressInterval = setInterval(() => {
                progress += Math.random() * 5;
                if (progress > 100) progress = 100;
                
                const loadedRecords = Math.floor((progress / 100) * totalRecords);
                grid.updateLoadingProgress(progress, totalRecords, loadedRecords);
                
                if (progress >= 100) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                    setTimeout(() => {
                        hideLoading();
                    }, 1000);
                }
            }, 200);
        }
        
        function toggleTheme() {
            const body = document.body;
            const currentTheme = body.getAttribute('data-theme');
            
            if (currentTheme === 'dark') {
                body.removeAttribute('data-theme');
            } else {
                body.setAttribute('data-theme', 'dark');
            }
        }
    </script>
</body>
</html>
